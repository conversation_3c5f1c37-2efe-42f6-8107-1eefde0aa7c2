name: pgl_mobile_app
description: "PGL."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 3.2.14+52

environment:
  sdk: ^3.5.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2
  get: ^4.6.6
  flutter_svg: ^2.0.9
  material_symbols_icons: ^4.2711.0
  d_chart: ^3.0.0
  equatable: ^2.0.5
  timeago: ^3.6.0
  infinite_carousel: ^1.0.3
  galleryimage: ^2.0.1
  url_launcher: ^6.2.2
  flutter_bloc: ^9.0.0
  bloc: ^9.0.0
  formz: ^0.8.0
  hydrated_bloc: ^10.0.0
  path_provider: ^2.1.1
  skeletonizer: ^2.0.1
  stream_transform: ^2.1.0
  firebase_core: ^4.0.0
  firebase_messaging: ^16.0.0
  firebase_analytics: ^12.0.0
  flutter_local_notifications: ^19.0.0
  flutter_widget_from_html: ^0.17.0
  fluttertoast: ^8.2.4
  flutter_upgrade_version: ^1.1.3
  cached_network_image: ^3.3.1
  authentication_repository:
    path: packages/authentication_repository
  user_repository:
    path: packages/user_repository
  dio_client:
    path: packages/dio_client
  meta_data_repository:
    path: packages/meta_data_repository
  vehicles_repository:
    path: packages/vehicles_repository
  shipments_repository:
    path: packages/shipments_repository
  invoices_repository:
    path: packages/invoices_repository
  payments_repository:
    path: packages/payments_repository
  mix_shipping_repository:
    path: packages/mix_shipping_repository
  announcements_repository:
    path: packages/announcements_repository
  notifications_repository:
    path: packages/notifications_repository
  shipping_rates_repository:
    path: packages/shipping_rates_repository
  towing_rates_repository:
    path: packages/towing_rates_repository
  mix_shipping_rates_repository:
    path: packages/mix_shipping_rates_repository
  secure_storage:
    path: packages/secure_storage
  transactions_repository:
    path: packages/transactions_repository
  tracking_repository:
    path: packages/tracking_repository
  auction_payments_repository:
    path: packages/auction_payments_repository
  google_fonts: ^6.2.1
  shared_preferences: ^2.2.2
  provider: ^6.1.2
  stepper_list_view: ^0.0.2
  connectivity_plus: ^6.0.3
  timelines_plus: ^1.0.2
  expandable: ^5.0.1
  collection: ^1.18.0
  icons_launcher: ^3.0.0
  open_file: ^3.5.10


icons_launcher:
  platforms:
    android:
      enable: true
      image_path: "assets/new_launcher_icons/ic_logo_radius.png"
      notification_image: "assets/new_launcher_icons/ic_notification.png"
      # adaptive_background_color: "#ffffff"
      adaptive_background_image: "assets/new_launcher_icons/ic_background.png"
      adaptive_foreground_image: "assets/new_launcher_icons/ic_foreground.png"
      adaptive_round_image: "assets/new_launcher_icons/ic_logo_round.png"
      adaptive_monochrome_image: "assets/new_launcher_icons/ic_black_white.png"

    ios:
      enable: true
      image_path: "assets/new_launcher_icons/ic_logo_rectangle_light.png"
      # For iOS 18+ (support dark and tinted)
      dark_path: "assets/new_launcher_icons/ic_logo_rectangle_dark.png"
      tinted_path: "assets/new_launcher_icons/ic_logo_rectangle_tinted.png"


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0
  bloc_test: ^10.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  generate: true
  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/svgs/login-top.svg
    - assets/images/login-top.png
    - assets/svgs/cars-icon.svg
    - assets/svgs/truck-icon.svg
    - assets/svgs/invoice-icon.svg
    - assets/svgs/car-icon.svg
    - assets/svgs/car-building-icon.svg
    - assets/svgs/car-shipped-icon.svg
    - assets/svgs/car-2-icon.svg
    - assets/svgs/invoice-open-icon.svg
    - assets/svgs/invoice-paid-icon.svg
    - assets/svgs/invoice-past-due-icon.svg
    - assets/svgs/image-icon.svg
    - assets/svgs/no-image-icon.svg
    - assets/images/logo.svg
    - assets/svgs/bottom-right-turn-icon.svg
    - assets/svgs/car-image-icon.svg
    - assets/svgs/no-car-image-icon.svg
    - assets/svgs/shipment-image-icon.svg
    - assets/svgs/no-shipment-image-icon.svg
    - assets/flags/en.svg
    - assets/flags/ru.svg
    - assets/flags/ka.svg
    - assets/flags/ar.svg
    - assets/flags/square/en.svg
    - assets/flags/square/ru.svg
    - assets/flags/square/ka.svg
    - assets/flags/square/ar.svg
    - assets/svgs/tracking/auction_paid.svg
    - assets/svgs/tracking/auction_unpaid.svg
    - assets/svgs/tracking/on_the_way_to_warehouse.svg
    - assets/svgs/tracking/on_the_way_to_destination.svg
    - assets/svgs/tracking/on_hand_no_title.svg
    - assets/svgs/tracking/on_hand_with_title.svg
    - assets/svgs/tracking/shipped.svg
    - assets/svgs/tracking/at_loading.svg
    - assets/svgs/tracking/at_the_dock.svg
    - assets/svgs/tracking/on_hand_with_load.svg
    - assets/new_launcher_icons/ic_logo_splash.png

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
