import 'package:equatable/equatable.dart';

class VehicleContainer extends Equatable {
  // info
  final String? containerNumber;
  final DateTime loadingDate;
  final VehicleContainerBooking booking;

  // dates
  final DateTime? purchaseAt;

  const VehicleContainer({
    // info
    required this.containerNumber,
    required this.loadingDate,
    required this.booking,
    // dates
    this.purchaseAt,
  });

  factory VehicleContainer.fromMap(Map<String, dynamic> map) {
    return VehicleContainer(
      containerNumber: map['container_number'],
      loadingDate: DateTime.parse(map['loading_date']),
      booking: VehicleContainerBooking(
        bookingNumber: map['bookings']['booking_number'],
        eta: map['bookings']['eta'] != null
            ? DateTime.parse(
                map['bookings']['eta'],
              )
            : null,
        etd: map['bookings']['vessels']['etd'] != null
            ? DateTime.parse(
                map['bookings']['vessels']['etd'],
              )
            : null,
      ),
    );
  }

  @override
  List<Object?> get props => [
        containerNumber,
        loadingDate,
        booking,
      ];
}

class VehicleContainerBooking extends Equatable {
  // info
  final String bookingNumber;
  // dates
  final DateTime? eta;
  final DateTime? etd;

  const VehicleContainerBooking({
    // info
    required this.bookingNumber,
    // dates
    this.eta,
    this.etd,
  });

  @override
  List<Object?> get props => [
        bookingNumber,
        eta,
        etd,
      ];
}
