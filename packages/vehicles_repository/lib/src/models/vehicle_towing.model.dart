import 'package:equatable/equatable.dart';

class VehicleTowing extends Equatable {
  // info
  final int? towAmount;
  final String? towingCompany;

  // dates
  // final DateTime? towingRequestDate;

  const VehicleTowing({
    // info
    this.towAmount,
    this.towingCompany,
    // dates
    // this.towingRequestDate,
  });

  factory VehicleTowing.fromMap(Map<String, dynamic> map) {
    return VehicleTowing(
      towAmount: map['tow_amount'],
      towingCompany: map['towing_company'],
      // towingRequestDate: map['towing_request_date'] != null
      //     ? DateTime.parse(
      //         map['towing_request_date'],
      //       )
      //     : null,
    );
  }

  @override
  List<Object?> get props => [
        towAmount,
        towingCompany,
        // towingRequestDate,
      ];
}
