import 'dart:convert';

import 'package:equatable/equatable.dart';

class VehicleSummaryItem extends Equatable {
  // info
  final int locationId;
  final String locationName;
  final int onTheWay;
  final int auctionPaid;
  final int auctionUnpaid;
  final int shipped;
  final int onHandNoTitle;
  final int onHandWithTitle;
  final int onHandWithLoad;
  final int total;

  const VehicleSummaryItem({
    // info
    required this.locationId,
    required this.locationName,
    required this.onTheWay,
    required this.auctionPaid,
    required this.auctionUnpaid,
    required this.shipped,
    required this.onHandNoTitle,
    required this.onHandWithTitle,
    required this.onHandWithLoad,
    required this.total,
  });

  factory VehicleSummaryItem.fromMap(Map<String, dynamic> map) {
    return VehicleSummaryItem(
      locationId: map['location_id'] ?? 0,
      locationName: map['location_name'] ?? '',
      onTheWay: map['on_the_way'] ?? 0,
      auctionPaid: map['auction_paid'] ?? 0,
      auctionUnpaid: map['auction_unpaid'] ?? 0,
      shipped: map['shipped'] ?? 0,
      onHandNoTitle: map['on_hand_no_title'] ?? 0,
      onHandWithTitle: map['on_hand_with_title'] ?? 0,
      onHandWithLoad: map['on_hand_with_load'] ?? 0,
      total: map['total'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'location_id': locationId,
      'location_name': locationName,
      'on_the_way': onTheWay,
      'auction_paid': auctionPaid,
      'auction_unpaid': auctionUnpaid,
      'shipped': shipped,
      'on_hand_no_title': onHandNoTitle,
      'on_hand_with_title': onHandWithTitle,
      'on_hand_with_load': onHandWithLoad,
      'total': total,
    };
  }

  factory VehicleSummaryItem.fromJson(String json) => VehicleSummaryItem.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        locationId,
        locationName,
        onTheWay,
        auctionPaid,
        auctionUnpaid,
        shipped,
        onHandNoTitle,
        onHandWithTitle,
        onHandWithLoad,
        total,
      ];
}
