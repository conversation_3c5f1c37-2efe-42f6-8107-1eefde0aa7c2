import 'package:equatable/equatable.dart';
import 'package:vehicles_repository/src/models/models.dart';

class Vehicle extends Equatable {
  // info
  final int id;
  final String vin;
  final String lotNumber;
  final String year;
  final String make;
  final String model;
  final String color;
  final VehicleState vehicleState;
  final bool? isKeyPresent;
  final String? titleState;
  final String? titleStatus;
  final bool isTitleExist;
  final String? buyerNumber;
  final String? auctionName;
  final String? auctionCity;
  final String? auctionInvoice;
  final String? pointOfLoading;
  final String? photoLink;
  final String? coverPhoto;
  // dates
  final DateTime? purchaseAt;
  final DateTime? deliverDate;
  final DateTime? paymentDate;
  final DateTime? pickUpDate;
  final DateTime? datePostedInCenteraldispach;
  final DateTime? pickUpDueDate;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? titleRecievedDate;
  final List<String> images;
  final DateTime? towingRequestDate;

  // relations
  final VehicleContainer? container;
  final VehicleTowing? vehicleTowing;

  const Vehicle({
    // info
    required this.id,
    required this.vin,
    required this.lotNumber,
    required this.year,
    required this.make,
    required this.model,
    required this.color,
    required this.vehicleState,
    this.isKeyPresent,
    this.titleState,
    this.titleStatus,
    required this.isTitleExist,
    this.buyerNumber,
    this.auctionName,
    this.auctionCity,
    this.auctionInvoice,
    this.pointOfLoading,
    this.photoLink,
    this.coverPhoto,
    // dates
    this.purchaseAt,
    this.deliverDate,
    this.paymentDate,
    this.pickUpDate,
    this.datePostedInCenteraldispach,
    this.pickUpDueDate,
    this.createdAt,
    this.updatedAt,
    this.titleRecievedDate,
    this.towingRequestDate,
    this.images = const [],

    // relations
    this.container,
    this.vehicleTowing,
  });

  String getDescription() {
    return "$year $make $model $color";
  }

  static VehicleState getVehicleStatus(Map<String, dynamic> vehicle) {
    VehicleContainer? container = vehicle['containers'] != null
        ? VehicleContainer.fromMap(
            vehicle['containers'],
          )
        : null;
    if (container != null && container.containerNumber == null && vehicle['carstate'] == 'shipped') {
      return VehicleState.onHandWithLoad;
    }
    return VehicleState.fromJson(vehicle['carstate']);
  }

  factory Vehicle.fromMap(Map<String, dynamic> map) {
    return Vehicle(
      id: map['id'],
      vin: map['vin'],
      lotNumber: map['lot_number'] ?? '',
      year: map['year'] ?? '',
      make: map['make'] ?? '',
      model: map['model'] ?? '',
      color: map['color'] ?? '',
      vehicleState: getVehicleStatus(map),
      isKeyPresent: map['is_key_present'],
      titleState: map['title_state'],
      titleStatus: map['title_status'],
      isTitleExist: map['is_title_exist'] ?? false,
      buyerNumber: map['buyer_number'],
      auctionName: map['auction_name'],
      auctionCity: map['auction_city'],
      auctionInvoice: map['auction_invoice'],
      pointOfLoading: map['pol_locations']['name'],
      photoLink: map['photo_link'],
      coverPhoto: map['cover_photo'],
      purchaseAt: map['purchased_at'] != null ? DateTime.parse(map['purchased_at']) : null,
      deliverDate: map['deliver_date'] != null ? DateTime.parse(map['deliver_date']) : null,
      paymentDate: map['payment_date'] != null ? DateTime.parse(map['payment_date']) : null,
      pickUpDate: map['pickup_date'] != null ? DateTime.parse(map['pickup_date']) : null,
      titleRecievedDate: map['title_receive_date'] != null ? DateTime.parse(map['title_receive_date']) : null,
      towingRequestDate: map['request_for_pickup_date'] != null ? DateTime.parse(map['request_for_pickup_date']) : null,
      datePostedInCenteraldispach: map['date_posted_in_central_dispatch'] != null
          ? DateTime.parse(map['date_posted_in_central_dispatch'])
          : null,
      pickUpDueDate: map['pickup_due_date'] != null ? DateTime.parse(map['pickup_due_date']) : null,
      createdAt: map['created_at'] != null ? DateTime.parse(map['created_at']) : null,
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      images: map['vehicle_images'] != null
          ? map['vehicle_images'].map<String>((image) => image['url'] as String).toList()
          : const [],
      container: map['containers'] != null
          ? VehicleContainer.fromMap(
              map['containers'],
            )
          : null,
      vehicleTowing: map['vehicle_towings'] != null
          ? VehicleTowing.fromMap(
              map['vehicle_towings'],
            )
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {};
  }

  @override
  List<Object?> get props => [
        id,
        vin,
        lotNumber,
        year,
        make,
        model,
        color,
        vehicleState,
        titleRecievedDate,
        towingRequestDate,
      ];
}

enum VehicleState {
  auctionPaid, // auction_paid
  auctionUnpaid, // auction_unpaid
  onTheWay, // on_the_way
  onHandNoTitle, //on_hand_no_title
  onHandWithTitle, //on_hand_with_title
  onHandWithLoad, //on_hand_with_load
  shipped; // shipped,

  String toJson() {
    switch (name) {
      case 'auctionPaid':
        return 'auction_paid';
      case 'auctionUnpaid':
        return 'auction_unpaid';
      case 'onTheWay':
        return 'on_the_way';
      case 'onHandNoTitle':
        return 'on_hand_no_title';
      case 'onHandWithTitle':
        return 'on_hand_with_title';
      case 'onHandWithLoad':
        return 'on_hand_with_load';
      case 'shipped':
        return 'shipped';
      default:
        return 'shipped';
    }
  }

  static VehicleState fromJson(String json) {
    switch (json) {
      case 'auction_paid':
        return VehicleState.auctionPaid;
      case 'auction_unpaid':
        return VehicleState.auctionUnpaid;
      case 'on_the_way':
      case 'at_the_dock':
      case 'checked':
      case 'final_checked':
        return VehicleState.onTheWay;
      case 'on_hand_no_title':
        return VehicleState.onHandNoTitle;
      case 'on_hand_with_title':
        return VehicleState.onHandWithTitle;
      case 'on_hand_with_load':
        return VehicleState.onHandWithLoad;
      case 'shipped':
        return VehicleState.shipped;
      default:
        return VehicleState.shipped;
    }
  }
}
