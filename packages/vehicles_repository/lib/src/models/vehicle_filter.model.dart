import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:vehicles_repository/src/models/models.dart';
import 'package:intl/intl.dart';

class VehiclesFilter extends Equatable {
  final int page;
  final int perPage;
  final String state;
  final String search;
  // page filter
  final VehicleFilterData? filterData;

  const VehiclesFilter({
    this.page = 1,
    this.perPage = 10,
    this.state = '',
    this.search = '',
    this.filterData,
  });

  VehiclesFilter copyWith({
    int? page,
    int? perPage,
    String? state,
    String? search,
    VehicleFilterData? filterData,
    bool setFilterDataNull = false,
  }) {
    return VehiclesFilter(
      page: page ?? this.page,
      perPage: perPage ?? this.perPage,
      state: state ?? this.state,
      search: search ?? this.search,
      filterData: setFilterDataNull ? null : filterData ?? this.filterData,
    );
  }

  factory VehiclesFilter.fromMap(Map<String, dynamic> map) {
    return VehiclesFilter(
      page: map['page'] ?? 1,
      perPage: map['per_page'] ?? 1,
      state: map['state'] ?? '',
      search: map['search'] ?? '',
      filterData: map['filterData'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'page': page,
      'per_page': perPage,
      'state': state,
      'search': search,
      if (filterData != null) 'filterData': jsonEncode(filterData?.toMap()),
    };
  }

  factory VehiclesFilter.fromJson(String json) => VehiclesFilter.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        page,
        perPage,
        state,
        search,
        filterData,
      ];
}

class VehicleFilterData extends Equatable {
  final List<VehicleState> carState;
  final String lotNumber;
  final String vin;
  final String make;
  final String model;
  final String year;
  final VehicleFilterPointOfLoading? filterPointOfLoading;
  final VehicleFilterContainer? filterContainer;
  final Map? price;
  final DateTime? purchaseFromDate;
  final DateTime? purchaseToDate;
  final DateTime? paymentFromDate;
  final DateTime? paymentToDate;
  final DateTime? deliverFromDate;
  final DateTime? deliverToDate;

  const VehicleFilterData({
    this.carState = const [],
    this.lotNumber = '',
    this.vin = '',
    this.make = '',
    this.model = '',
    this.year = '',
    this.filterPointOfLoading,
    this.filterContainer,
    this.price,
    this.purchaseFromDate,
    this.purchaseToDate,
    this.paymentFromDate,
    this.paymentToDate,
    this.deliverFromDate,
    this.deliverToDate,
  });

  VehicleFilterData copyWith({
    List<VehicleState>? carState,
    String? lotNumber,
    String? vin,
    String? make,
    String? model,
    String? year,
    VehicleFilterPointOfLoading? filterPointOfLoading,
    VehicleFilterContainer? filterContainer,
    Map? price,
    DateTime? purchaseFromDate,
    DateTime? purchaseToDate,
    DateTime? paymentFromDate,
    DateTime? paymentToDate,
    DateTime? deliverFromDate,
    DateTime? deliverToDate,
  }) {
    return VehicleFilterData(
      carState: carState ?? this.carState,
      lotNumber: lotNumber ?? this.lotNumber,
      vin: vin ?? this.vin,
      make: make ?? this.make,
      model: model ?? this.model,
      year: year ?? this.year,
      filterPointOfLoading: filterPointOfLoading ?? this.filterPointOfLoading,
      filterContainer: filterContainer ?? this.filterContainer,
      price: price ?? this.price,
      purchaseFromDate: purchaseFromDate ?? this.purchaseFromDate,
      purchaseToDate: purchaseToDate ?? this.purchaseToDate,
      paymentFromDate: paymentFromDate ?? this.paymentFromDate,
      paymentToDate: paymentToDate ?? this.paymentToDate,
      deliverFromDate: deliverFromDate ?? this.deliverFromDate,
      deliverToDate: deliverToDate ?? this.deliverToDate,
    );
  }

  factory VehicleFilterData.fromMap(Map<String, dynamic> map) {
    return VehicleFilterData(
      carState: map['carstate'],
      lotNumber: map['lot_number'],
      vin: map['vin'],
      make: map['make'],
      model: map['model'],
      year: map['year'],
      filterPointOfLoading: map['point_of_loading'],
      filterContainer: map['filter_container'],
      price: map['price'],
      purchaseFromDate: map['purchase_from_date'],
      purchaseToDate: map['purchase_to_date'],
      paymentFromDate: map['payment_from_date'],
      paymentToDate: map['payment_to_date'],
      deliverFromDate: map['deliver_from_date'],
      deliverToDate: map['deliver_to_date'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (carState.isNotEmpty)
        'carstate': carState
            .map(
              (st) => st.toJson(),
            )
            .toList(),
      if (lotNumber != '') 'lot_number': lotNumber,
      if (vin != '') 'vin': vin,
      if (make != '') 'make': make,
      if (model != '') 'model': model,
      if (year != '') 'year': year,
      if (filterPointOfLoading != null) 'int@@point_of_loading': [filterPointOfLoading?.id],
      if (filterContainer != null) 'container_id': [filterContainer?.id],
      if (price != null) 'price': price,
      if (purchaseFromDate != null || purchaseToDate != null)
        'purchased_at': {
          if (purchaseFromDate != null)
            'from': DateFormat('yyyy-MM-dd').format(
              purchaseFromDate ?? DateTime.now(),
            ),
          if (purchaseToDate != null)
            'to': DateFormat('yyyy-MM-dd').format(
              purchaseToDate ?? DateTime.now(),
            ),
        },
      if (paymentFromDate != null || paymentToDate != null)
        'payment_date': {
          if (paymentFromDate != null)
            'from': DateFormat('yyyy-MM-dd').format(
              paymentFromDate ?? DateTime.now(),
            ),
          if (paymentToDate != null)
            'to': DateFormat('yyyy-MM-dd').format(
              paymentToDate ?? DateTime.now(),
            ),
        },
      if (deliverFromDate != null || deliverToDate != null)
        'deliver_date': {
          if (deliverFromDate != null)
            'from': DateFormat('yyyy-MM-dd').format(
              deliverFromDate ?? DateTime.now(),
            ),
          if (deliverToDate != null)
            'to': DateFormat('yyyy-MM-dd').format(
              deliverToDate ?? DateTime.now(),
            ),
        },
    };
  }

  factory VehicleFilterData.fromJson(String json) => VehicleFilterData.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        carState,
        lotNumber,
        vin,
        make,
        model,
        year,
        filterPointOfLoading,
        price,
        purchaseFromDate,
        purchaseToDate,
        paymentFromDate,
        paymentToDate,
        deliverFromDate,
        deliverToDate,
      ];
}

class VehicleFilterContainer extends Equatable {
  final int id;
  final String containerNo;

  const VehicleFilterContainer({
    required this.id,
    required this.containerNo,
  });

  factory VehicleFilterContainer.fromMap(Map<String, dynamic> map) {
    return VehicleFilterContainer(
      id: map['id'],
      containerNo: map['container_number'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'container_number': containerNo,
    };
  }

  factory VehicleFilterContainer.fromJson(String json) => VehicleFilterContainer.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [id, containerNo];
}

class VehicleFilterPointOfLoading extends Equatable {
  final int id;
  final String name;

  const VehicleFilterPointOfLoading({
    required this.id,
    required this.name,
  });

  factory VehicleFilterPointOfLoading.fromMap(Map<String, dynamic> map) {
    return VehicleFilterPointOfLoading(
      id: map['id'],
      name: map['name'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
    };
  }

  factory VehicleFilterPointOfLoading.fromJson(String json) => VehicleFilterPointOfLoading.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [id, name];
}
