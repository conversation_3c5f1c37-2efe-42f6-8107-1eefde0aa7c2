import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:dio_client/dio_client.dart';
import 'package:path_provider/path_provider.dart';
import 'package:vehicles_repository/vehicles_repository.dart';

class VehiclesRepository {
  final String _url = '/api/customer/vehicles';
  final String _dockReceiptPdfURL = '/api/customer/vehicles/generatepdf/dockRecepit';
  final String _billOfLoadingPdfURL = '/api/customer/vehicles/generatepdf/billOfLoading';

  final DioClient dioClient;

  VehiclesRepository({
    required this.dioClient,
  });

  Future<List<Vehicle>> getVehicles(VehiclesFilter vehiclesFilter) async {
    try {
      final response = await dioClient.get(
        _url,
        queryParameters: vehiclesFilter.toMap(),
      );
      return response['data'].map<Vehicle>((item) => Vehicle.fromMap(item)).toList();
    } catch (e) {
      log(e.toString());
      throw Exception('error fetching vehicles');
    }
  }

  Future<File> getDockReceiptPDF(int id) async {
    try {
      Directory tempDir = await getTemporaryDirectory();
      String tempPath = tempDir.path;
      File file = File('$tempPath/vehicle-dock-receipt-$id.pdf');
      final response = await dioClient.get(
        '$_dockReceiptPdfURL/$id',
        options: Options(responseType: ResponseType.bytes),
      );
      await file.writeAsBytes(response);
      return file;
    } catch (e) {
      throw Exception('error downloading dock receipt');
    }
  }

  Future<File> getbillOfLoadingPDF(int id) async {
    try {
      Directory tempDir = await getTemporaryDirectory();
      String tempPath = tempDir.path;
      File file = File('$tempPath/vehicle-BOL-$id.pdf');
      final response = await dioClient.get(
        '$_billOfLoadingPdfURL/$id',
        options: Options(responseType: ResponseType.bytes),
      );
      await file.writeAsBytes(response);
      return file;
    } catch (e) {
      throw Exception('error downloading BOL');
    }
  }
}
