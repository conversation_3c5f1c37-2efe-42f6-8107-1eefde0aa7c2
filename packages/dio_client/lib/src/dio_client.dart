import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:secure_storage/secure_storage.dart';

const _defaultConnectTimeout = Duration(minutes: 1);
const _defaultReceiveTimeout = Duration(minutes: 1);

class DioClient {
  late String baseUrl;
  late String minioUrl;
  String contentType;
  final SecureStorage _secureStorage = SecureStorage();

  late Dio _dio;

  final List<Interceptor>? interceptors;

  DioClient({
    this.interceptors,
    this.contentType = 'application/json; charset=UTF-8',
  }) {
    _dio = Dio();
    // baseUrl = "EnvironmentConfig.apiBase";
    baseUrl = "https://api.pglsystem.com";
    minioUrl = "https://storage-server.pglsystem.com";
    // baseUrl = "https://latest-api.pglsystem.com";
    // baseUrl = "http://192.168.8.67:8000";
    _dio
      ..options.baseUrl = baseUrl
      ..options.connectTimeout = _defaultConnectTimeout
      ..options.receiveTimeout = _defaultReceiveTimeout
      ..httpClientAdapter;
    if (interceptors?.isNotEmpty ?? false) {
      _dio.interceptors.addAll(interceptors!);
    }
    if (kDebugMode) {
      _dio.interceptors.add(
        LogInterceptor(
          responseBody: true,
          error: true,
          requestHeader: true,
          responseHeader: false,
          request: false,
          requestBody: false,
        ),
      );
    }
  }

  Future<dynamic> get(
    String uri, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      var token = await _secureStorage.getToken();
      var locale = await _secureStorage.readFromLocalStorage('locale');
      _dio.options.headers = {"Authorization": "Bearer $token", "X-Language": locale};
      var response = await _dio.get(
        uri,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
      return response.data;
    } on SocketException catch (e) {
      throw SocketException(e.toString());
    } on FormatException catch (_) {
      throw const FormatException("Unable to process the data");
    } catch (e) {
      rethrow;
    }
  }

  Future<dynamic> post(
    String uri, {
    data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      var token = await _secureStorage.getToken();
      var locale = await _secureStorage.readFromLocalStorage('locale');
      _dio.options.headers = {"Authorization": "Bearer $token", "X-Language": locale};
      var response = await _dio.post(
        uri,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );

      return response.data;
    } on FormatException catch (_) {
      throw const FormatException("Unable to process the data");
    } catch (e) {
      rethrow;
    }
  }

  Future<dynamic> patch(
    String uri, {
    data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      var token = await _secureStorage.getToken();
      var locale = await _secureStorage.readFromLocalStorage('locale');
      _dio.options.headers = {"Authorization": "Bearer $token", "X-Language": locale};
      var response = await _dio.patch(
        uri,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      return response.data;
    } on FormatException catch (_) {
      throw const FormatException("Unable to process the data");
    } catch (e) {
      rethrow;
    }
  }

  Future<dynamic> delete(
    String uri, {
    data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      var token = await _secureStorage.getToken();
      var locale = await _secureStorage.readFromLocalStorage('locale');
      _dio.options.headers = {"Authorization": "Bearer $token", "X-Language": locale};
      var response = await _dio.delete(
        uri,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      return response.data;
    } on FormatException catch (_) {
      throw const FormatException("Unable to process the data");
    } catch (e) {
      rethrow;
    }
  }

  String getMinioUrl() {
    return minioUrl;
  }
}
