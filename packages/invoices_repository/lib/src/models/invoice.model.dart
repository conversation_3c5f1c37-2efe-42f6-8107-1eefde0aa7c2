import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:payments_repository/payments_repository.dart';

class Invoice extends Equatable {
  // info
  final int id;
  final String invoiceNumber;
  final String containerNumber; // containers.container_number
  final String purpose;
  final int invoiceAmount;
  final InvoiceState invoiceState;
  final int? paymentReceived;
  final String? paymentMethod;
  final String? description;

  final List<PaymentReceived<Invoice>> payments;

  // dates
  final DateTime invoiceDate;
  final DateTime? invoiceDueDate;
  final List<DateTime> receivedDates;

  const Invoice({
    // info
    required this.id,
    required this.invoiceNumber,
    required this.containerNumber,
    required this.purpose,
    required this.invoiceAmount,
    required this.invoiceState,
    this.paymentReceived,
    this.paymentMethod,
    this.description,
    required this.payments,
    // dates
    required this.invoiceDate,
    required this.invoiceDueDate,
    this.receivedDates = const [],

    // relations
  });

  String getAmount() {
    return NumberFormat('###,###,###,###').format(invoiceAmount);
  }

  String getPaymentReceived() {
    return NumberFormat('###,###,###,###').format(getPaymentReceivedNumber());
  }

  double getPaymentReceivedNumber() {
    // double totalAmountApplied = 0.0;

    // for (var payment in payments) {
    //   totalAmountApplied += payment.amountApplied;
    // }
    return paymentReceived?.toDouble() ?? 0;
  }

  String getAmountDue() {
    double amount = invoiceAmount - (getPaymentReceivedNumber());
    return amount > 0 ? NumberFormat('###,###,###,###').format(amount) : '0';
  }

  factory Invoice.fromMap(Map<String, dynamic> map) {
    Invoice invoice = Invoice(
      id: map["id"],
      invoiceNumber: map['invoice_number'] ?? '',
      containerNumber: map['container_number'] ?? '',
      purpose: map['purpose'] ?? '',
      invoiceAmount: map['invoice_amount'].toInt(),
      invoiceState: InvoiceState.fromJson(map['status']),
      invoiceDate: DateTime.parse(map['invoice_date']),
      invoiceDueDate: map['invoice_due_date'] != null ? DateTime.parse(map['invoice_due_date']) : null,
      paymentMethod: map['payment_method'],
      paymentReceived: map['payment_received'],
      receivedDates: map['payment_date'] != null
          ? map['payment_date']
              .toString()
              .split('|')
              .where((String str) => str.trim().isNotEmpty)
              .map<DateTime>(
                (String date) => DateTime.parse(date.trim()),
              )
              .toList()
          : [],
      description: map['description'],
      payments: [],
    );
    // List<PaymentReceived<Invoice>> payments = map['payments'] != null
    //     ? map['payments']
    //         .map<PaymentReceived<Invoice>>(
    //           (item) => PaymentReceived<Invoice>.fromMap(
    //             item,
    //             passedParent: invoice,
    //           ),
    //         )
    //         .toList()
    //     : [];
    // invoice.payments.addAll(payments);
    return invoice;
  }

  Map<String, dynamic> toMap() {
    return {};
  }

  @override
  List<Object?> get props => [
        id,
      ];
}

enum InvoiceState {
  open, // open
  paid, // paid
  pastDue; // past_due

  String toJson() {
    switch (name) {
      case 'open':
        return 'open';
      case 'paid':
        return 'paid';
      case 'pastDue':
        return 'past_due';
      default:
        return 'open';
    }
  }

  static InvoiceState fromJson(String json) {
    switch (json) {
      case 'open':
        return InvoiceState.open;
      case 'paid':
        return InvoiceState.paid;
      case 'past_due':
        return InvoiceState.pastDue;
      default:
        return InvoiceState.open;
    }
  }
}
