import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:invoices_repository/invoices_repository.dart';

class InvoicesFilter extends Equatable {
  final int page;
  final int perPage;
  final String status;
  final String search;
  final InvoiceFilterData? filterData;

  const InvoicesFilter({
    this.page = 1,
    this.perPage = 10,
    this.status = '',
    this.search = '',
    this.filterData,
  });

  InvoicesFilter copyWith({
    int? page,
    int? perPage,
    String? status,
    String? search,
    InvoiceFilterData? filterData,
    bool setFilterDataNull = false,
  }) {
    return InvoicesFilter(
      page: page ?? this.page,
      perPage: perPage ?? this.perPage,
      status: status ?? this.status,
      search: search ?? this.search,
      filterData: setFilterDataNull ? null : filterData ?? this.filterData,
    );
  }

  factory InvoicesFilter.fromMap(Map<String, dynamic> map) {
    return InvoicesFilter(
      page: map['page'] ?? 1,
      perPage: map['per_page'] ?? 1,
      status: map['status'] ?? '',
      search: map['search'] ?? '',
      filterData: map['filterData'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'page': page,
      'per_page': perPage,
      'status': status,
      'search': search,
      if (filterData != null) 'filterData': jsonEncode(filterData?.toMap()),
    };
  }

  factory InvoicesFilter.fromJson(String json) => InvoicesFilter.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        page,
        perPage,
        status,
        search,
        filterData,
      ];
}

class InvoiceFilterData extends Equatable {
  final List<InvoiceState> status;
  final InvoiceFilterContainer? filterContainer;
  final InvoiceFilterInvoiceNo? filterInvoiceNo;
  final DateTime? issueFromDate;
  final DateTime? issueToDate;
  final Map? invoiceAmount;
  final Map? paymentReceived;

  const InvoiceFilterData({
    this.status = const [],
    this.filterContainer,
    this.filterInvoiceNo,
    this.issueFromDate,
    this.issueToDate,
    this.invoiceAmount,
    this.paymentReceived,
  });

  InvoiceFilterData copyWith({
    List<InvoiceState>? status,
    InvoiceFilterContainer? filterContainer,
    InvoiceFilterInvoiceNo? filterInvoiceNo,
    DateTime? issueFromDate,
    DateTime? issueToDate,
    Map? invoiceAmount,
    Map? paymentReceived,
  }) {
    return InvoiceFilterData(
      status: status ?? this.status,
      filterContainer: filterContainer ?? this.filterContainer,
      filterInvoiceNo: filterInvoiceNo ?? this.filterInvoiceNo,
      issueFromDate: issueFromDate ?? this.issueFromDate,
      issueToDate: issueToDate ?? this.issueToDate,
      invoiceAmount: invoiceAmount ?? this.invoiceAmount,
      paymentReceived: paymentReceived ?? this.paymentReceived,
    );
  }

  factory InvoiceFilterData.fromMap(Map<String, dynamic> map) {
    return InvoiceFilterData(
      status: map['status'],
      filterContainer: map['container_no'],
      filterInvoiceNo: map['invoice_no'],
      issueFromDate: map['issue_from_date'],
      issueToDate: map['issue_to_date'],
      invoiceAmount: map['invoice_amount'],
      paymentReceived: map['payment_received'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (status.isNotEmpty)
        'status': status
            .map(
              (st) => st.toJson(),
            )
            .toList(),
      if (filterContainer != null) 'container_id': [filterContainer?.id],
      if (filterInvoiceNo != null) 'id': [filterInvoiceNo?.id],
      if (issueFromDate != null || issueToDate != null)
        'invoice_date': {
          if (issueFromDate != null)
            'from': DateFormat('yyyy-MM-dd').format(
              issueFromDate ?? DateTime.now(),
            ),
          if (issueToDate != null)
            'to': DateFormat('yyyy-MM-dd').format(
              issueToDate ?? DateTime.now(),
            ),
        },
      if (invoiceAmount != null) 'invoice_amount': invoiceAmount,
      if (paymentReceived != null) 'payment_received': paymentReceived,
    };
  }

  factory InvoiceFilterData.fromJson(String json) => InvoiceFilterData.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        status,
        filterContainer,
        filterInvoiceNo,
        issueFromDate,
        issueToDate,
        invoiceAmount,
        paymentReceived,
      ];
}

class InvoiceFilterContainer extends Equatable {
  final int id;
  final String containerNo;

  const InvoiceFilterContainer({
    required this.id,
    required this.containerNo,
  });

  factory InvoiceFilterContainer.fromMap(Map<String, dynamic> map) {
    return InvoiceFilterContainer(
      id: map['id'],
      containerNo: map['container_number'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'container_number': containerNo,
    };
  }

  factory InvoiceFilterContainer.fromJson(String json) => InvoiceFilterContainer.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [id, containerNo];
}

class InvoiceFilterInvoiceNo extends Equatable {
  final int id;
  final String invoiceNo;

  const InvoiceFilterInvoiceNo({
    required this.id,
    required this.invoiceNo,
  });

  factory InvoiceFilterInvoiceNo.fromMap(Map<String, dynamic> map) {
    return InvoiceFilterInvoiceNo(
      id: map['id'],
      invoiceNo: map['invoice_number'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoice_number': invoiceNo,
    };
  }

  factory InvoiceFilterInvoiceNo.fromJson(String json) => InvoiceFilterInvoiceNo.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [id, invoiceNo];
}
