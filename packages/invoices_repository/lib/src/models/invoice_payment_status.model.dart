import 'dart:convert';

import 'package:equatable/equatable.dart';

class InvoicePaymentStatus extends Equatable {
  final double all;
  final double paid;
  final double due;
  final double allInvoice;
  final double paidInvoice;
  final double dueInvoice;
  final double allMixshipping;
  final double paidMixshipping;
  final double dueMixshipping;

  const InvoicePaymentStatus({
    required this.all,
    required this.paid,
    required this.due,
    required this.allInvoice,
    required this.paidInvoice,
    required this.dueInvoice,
    required this.allMixshipping,
    required this.paidMixshipping,
    required this.dueMixshipping,
  });

  factory InvoicePaymentStatus.fromMap(Map<String, dynamic> map) {
    return InvoicePaymentStatus(
      // all: map['all'].toDouble() ?? 0.0,
      // paid: map['paid'].toDouble() ?? 0.0,
      // due: map['due'].toDouble() ?? 0.0,
      all: map['all'].toDouble() ?? 0.0,
      paid: map['paid'].toDouble() ?? 0.0,
      due: map['due'].toDouble() ?? 0.0,
      allInvoice: map['invoice_all'].toDouble() ?? 0.0,
      paidInvoice: map['invoice_paid'].toDouble() ?? 0.0,
      dueInvoice: map['invoice_due'].toDouble() ?? 0.0,
      allMixshipping: map['mix_shipping_all'].toDouble() ?? 0.0,
      paidMixshipping: map['mix_shipping_paid'].toDouble() ?? 0.0,
      dueMixshipping: map['mix_shipping_due'].toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'all': all,
      'paid': paid,
      'due': due,
      'invoice_all': allInvoice,
      'invoice_paid': paidInvoice,
      'invoice_due': dueInvoice,
      'mix_shipping_all': allMixshipping,
      'mix_shipping_paid': paidMixshipping,
      'mix_shipping_due': dueMixshipping,
    };
  }

  factory InvoicePaymentStatus.fromJson(String json) => InvoicePaymentStatus.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        all,
        paid,
        due,
        allInvoice,
        paidInvoice,
        dueInvoice,
        allMixshipping,
        paidMixshipping,
        dueMixshipping,
      ];
}
