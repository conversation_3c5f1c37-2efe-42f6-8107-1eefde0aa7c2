import 'dart:async';
import 'dart:io';

import 'package:dio_client/dio_client.dart';
import 'package:invoices_repository/src/models/models.dart';
import 'package:path_provider/path_provider.dart';
import 'package:payments_repository/payments_repository.dart';

class InvoicesRepository {
  final String _url = '/api/customer/invoices';
  final String _urlV1 = '/api/customer/invoices/v1';
  final String _invoicePdfURL = '/api/customer/invoices/generatepdf/invoice';
  final DioClient dioClient;

  InvoicesRepository({
    required this.dioClient,
  });

  Future<List<Invoice>> getInvoices(InvoicesFilter invoiceFilter) async {
    try {
      final response = await dioClient.get(
        _urlV1,
        queryParameters: invoiceFilter.toMap(),
      );
      return response['data'].map<Invoice>((item) => Invoice.fromMap(item)).toList();
    } catch (e) {
      throw Exception('error fetching invoices');
    }
  }

  Future<File> getInvoicePDF(int id) async {
    try {
      Directory tempDir = await getTemporaryDirectory();
      String tempPath = tempDir.path;
      File file = File('$tempPath/invoice-$id.pdf');
      final response = await dioClient.get(
        '$_invoicePdfURL/$id',
        options: Options(responseType: ResponseType.bytes),
      );
      await file.writeAsBytes(response);
      return file;
    } catch (e) {
      throw Exception('error downloading invoice');
    }
  }

  Future<Invoice> getInvoicePayments(Invoice invoice) async {
    try {
      final response = await dioClient.get(
        '$_url/${invoice.id}',
      );
      List<PaymentReceived<Invoice>> paymentReceiveds = response['data']['payments']
          .map<PaymentReceived<Invoice>>((item) => PaymentReceived<Invoice>.fromMap(item, passedParent: invoice))
          .toList();
      invoice.payments.addAll(paymentReceiveds);
      return invoice;
    } catch (e) {
      throw Exception('error fetching invoice Payments');
    }
  }
}
