import 'dart:async';
import 'dart:io';

import 'package:dio_client/dio_client.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shipping_rates_repository/shipping_rates_repository.dart';

class ShippingRatesRepository {
  final String _url = '/api/customer/shipping-rates';
  final String _invoicePdfURL = '/api/customer/invoices/generatepdf/invoice';
  final DioClient dioClient;

  ShippingRatesRepository({
    required this.dioClient,
  });

  Future<Map<String, dynamic>> getShippingRates(ShippingRatesFilter shippingRatesFilter) async {
    try {
      final response = await dioClient.get(
        _url,
        queryParameters: shippingRatesFilter.toMap(),
      );
      return {
        'items': response['data'].map<ShippingRate>((item) => ShippingRate.fromMap(item)).toList(),
        'note': response['note'] ?? ''
      };
    } catch (e) {
      throw Exception('error fetching shipping_rates');
    }
  }

  Future<bool> markAllAsSeen() async {
    try {
      await dioClient.patch(
        _url,
      );
      return true;
    } catch (e) {
      throw Exception('error reading shipping_rates');
    }
  }

  Future<File> getShippingRatesPDF(int id) async {
    try {
      Directory tempDir = await getTemporaryDirectory();
      String tempPath = tempDir.path;
      File file = File('$tempPath/invoice-$id.pdf');
      final response = await dioClient.get(
        '$_invoicePdfURL/$id',
        options: Options(responseType: ResponseType.bytes),
      );
      await file.writeAsBytes(response);
      return file;
    } catch (e) {
      throw Exception('error downloading invoice');
    }
  }
}
