import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';

extension CustomIntFunctions on int {
  String formatComma() {
    return NumberFormat('###,###,###,###').format(this);
  }
}

class ShippingRate extends Equatable {
  // info
  final int id;
  final ShippingRateType shippingRateType;
  final int rate_40hc;
  final int rate_45hc;
  final int sedanRate;
  final int sedanDismantleRate;
  final int suvRate;
  final int suvDismantleRate;
  final int mixRate;
  final int tdsAmount;
  final String? companyType;
  final String? vehicleType;
  final String location;
  final String destination;
  // dates
  final DateTime effectiveDate;

  const ShippingRate({
    // info
    required this.id,
    required this.shippingRateType,
    required this.rate_40hc,
    required this.rate_45hc,
    required this.sedanRate,
    required this.sedanDismantleRate,
    required this.suvRate,
    required this.suvDismantleRate,
    required this.mixRate,
    required this.tdsAmount,
    required this.companyType,
    required this.vehicleType,
    required this.location,
    required this.destination,
    required this.effectiveDate,
  });

  String getAmount() {
    return NumberFormat('###,###,###,###').format(rate_40hc);
  }

  factory ShippingRate.fromMap(Map<String, dynamic> map) {
    return ShippingRate(
      id: map["id"],
      shippingRateType: ShippingRateType.fromJson(map['type'] ?? ''),
      rate_40hc: map['rate_40hc'] ?? 0,
      rate_45hc: map['rate_45hc'] ?? 0,
      sedanRate: map['sedan_rate'] ?? 0,
      sedanDismantleRate: map['sedan_dismantle_rate'] ?? 0,
      suvRate: map['suv_rate'] ?? 0,
      suvDismantleRate: map['suv_dismantle_rate'] ?? 0,
      mixRate: map['mix_rate'] ?? 0,
      tdsAmount: map['tds_amount'] ?? 0,
      companyType: map['special_shipping_rates'] != null ? map['special_shipping_rates']['company_type'] : null,
      vehicleType: map['special_shipping_rates'] != null ? map['special_shipping_rates']['vehicle_type'] : null,
      location: map['locations'] != null ? map['locations']['name'] : '',
      destination: map['destinations'] != null ? map['destinations']['name'] : '',
      effectiveDate: map['general_shipping_rates'] != null
          ? DateTime.parse(map['general_shipping_rates']['effective_date'])
          : DateTime.parse(map['special_shipping_rates']['effective_date']),
    );
  }

  Map<String, dynamic> toMap() {
    return {};
  }

  @override
  List<Object?> get props => [
        id,
      ];
}

enum ShippingRateType {
  complete, // complete
  halfcut, // halfcut
  booking; // booking

  String toJson() {
    switch (name) {
      case 'complete':
        return 'complete';
      case 'halfcut':
        return 'halfcut';
      case 'booking':
        return 'booking';
      default:
        return 'complete';
    }
  }

  static ShippingRateType fromJson(String json) {
    switch (json) {
      case 'complete':
        return ShippingRateType.complete;
      case 'halfcut':
        return ShippingRateType.halfcut;
      case 'booking':
        return ShippingRateType.booking;
      default:
        return ShippingRateType.complete;
    }
  }
}
