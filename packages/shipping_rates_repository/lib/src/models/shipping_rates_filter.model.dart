import 'dart:convert';

import 'package:equatable/equatable.dart';

class ShippingRatesFilter extends Equatable {
  final int page;
  final int perPage;
  final String search;

  const ShippingRatesFilter({
    this.page = 1,
    this.perPage = 20000,
    this.search = '',
  });

  factory ShippingRatesFilter.fromMap(Map<String, dynamic> map) {
    return ShippingRatesFilter(
      page: map['page'] ?? 1,
      perPage: map['per_page'] ?? 1,
      search: map['search'] ?? '',
    );
  }

  ShippingRatesFilter copyWith({
    int? page,
    int? perPage,
    String? search,
  }) {
    return ShippingRatesFilter(
      page: page ?? this.page,
      perPage: perPage ?? this.perPage,
      search: search ?? this.search,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'page': page,
      'per_page': perPage,
      'search': search,
    };
  }

  factory ShippingRatesFilter.fromJson(String json) => ShippingRatesFilter.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        page,
        perPage,
        search,
      ];
}
