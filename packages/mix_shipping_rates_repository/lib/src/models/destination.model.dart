import 'dart:convert';

import 'package:equatable/equatable.dart';

class DestinationModel extends Equatable {
  final int id;
  final String name;

  const DestinationModel({
    required this.id,
    required this.name,
  });

  factory DestinationModel.fromMap(Map<String, dynamic> map) {
    return DestinationModel(
      id: map['id'],
      name: map['name'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
    };
  }

  factory DestinationModel.fromJson(String json) => DestinationModel.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [id, name];
}
