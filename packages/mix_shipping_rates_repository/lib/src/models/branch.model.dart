import 'dart:convert';

import 'package:equatable/equatable.dart';

class BranchModel extends Equatable {
  final int id;
  final String name;

  const BranchModel({
    required this.id,
    required this.name,
  });

  factory BranchModel.fromMap(Map<String, dynamic> map) {
    return BranchModel(
      id: map['id'],
      name: map['name'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
    };
  }

  factory BranchModel.fromJson(String json) => BranchModel.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [id, name];
}
