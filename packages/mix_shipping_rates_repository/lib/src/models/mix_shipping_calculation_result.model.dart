import 'dart:convert';

import 'package:equatable/equatable.dart';

class MixShoppingCalculationModel extends Equatable {
  final double towing;
  final double shipping;
  final double clearance;
  final double tdsCharges;
  final double taxDuty;
  final double fullSizeSUVs;
  final double manheimAdesa;
  final double majorAccident;
  final double moreThan10000AED;
  final Map locations;
  final Map loadingCities;
  final double exchangeRate;
  final bool notSupported;

  const MixShoppingCalculationModel({
    required this.towing,
    required this.shipping,
    required this.clearance,
    required this.tdsCharges,
    required this.taxDuty,
    required this.fullSizeSUVs,
    required this.manheimAdesa,
    required this.majorAccident,
    required this.moreThan10000AED,
    required this.locations,
    required this.loadingCities,
    required this.exchangeRate,
    this.notSupported = false,
  });

  factory MixShoppingCalculationModel.fromMap(Map<String, dynamic> map) {
    return MixShoppingCalculationModel(
      towing: map['towing'] != null ? map['towing'].toDouble() : 0,
      shipping: map['shipping'] != null ? map['shipping'].toDouble() : 0,
      clearance: map['clearance'] != null ? map['clearance'].toDouble() : 0,
      tdsCharges: map['TDS_charges'] != null ? map['TDS_charges'].toDouble() : 0,
      taxDuty: map['tax_Duty'] != null ? map['tax_Duty'].toDouble() : 0,
      fullSizeSUVs: map['full_size_SUVs'] != null ? map['full_size_SUVs'].toDouble() : 0,
      manheimAdesa: map['manheim_adesa'] != null ? map['manheim_adesa'].toDouble() : 0,
      majorAccident: map['major_accident'] != null ? map['major_accident'].toDouble() : 0,
      moreThan10000AED: map['more_than_10000_AED'] != null ? map['more_than_10000_AED'].toDouble() : 0,
      locations: map['locations'],
      loadingCities: map['locations'],
      exchangeRate: map['exchange_rate'].toDouble(),
    );
  }

  double getTotal() {
    return towing +
        shipping +
        clearance +
        tdsCharges +
        taxDuty +
        fullSizeSUVs * exchangeRate +
        manheimAdesa * exchangeRate +
        majorAccident * exchangeRate +
        moreThan10000AED * exchangeRate;
  }

  Map<String, dynamic> toMap() {
    return {};
  }

  factory MixShoppingCalculationModel.fromJson(String json) => MixShoppingCalculationModel.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        towing,
        shipping,
        clearance,
        taxDuty,
        tdsCharges,
        fullSizeSUVs,
        manheimAdesa,
        majorAccident,
        moreThan10000AED,
        locations,
        loadingCities,
        exchangeRate,
        notSupported,
      ];
}
