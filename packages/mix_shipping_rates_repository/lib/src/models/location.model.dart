import 'dart:convert';

import 'package:equatable/equatable.dart';

class LocationModel extends Equatable {
  final int id;
  final String name;

  const LocationModel({
    required this.id,
    required this.name,
  });

  factory LocationModel.fromMap(Map<String, dynamic> map) {
    return LocationModel(
      id: map['locations']['id'],
      name: map['locations']['name'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
    };
  }

  factory LocationModel.fromJson(String json) => LocationModel.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [id, name];
}
