import 'dart:io';

import 'package:dio_client/dio_client.dart';
import 'package:path_provider/path_provider.dart';
import 'models/models.dart';

class MixShippingRatesRepository {
  final DioClient dioClient;

  MixShippingRatesRepository({
    required this.dioClient,
  });

  Future<List<StateModel>> fetchStates() async {
    try {
      var res = await dioClient.get(
        '/api/customer/shipment-costs/states',
      );
      return res['data'].map<StateModel>((item) => StateModel.fromMap(item)).toList();
    } catch (e) {
      return [];
    }
  }

  Future<List<BranchModel>> fetchBranches(int stateId) async {
    try {
      var res = await dioClient.get('/api/customer/shipment-costs/branches', queryParameters: {
        'stateId': stateId,
      });
      return res['data'].map<BranchModel>((item) => BranchModel.fromMap(item)).toList();
    } catch (e) {
      return [];
    }
  }

  Future<List<CityModel>> fetchCities(int branchId) async {
    try {
      var res = await dioClient.get('/api/customer/shipment-costs/cities', queryParameters: {
        'branchId': branchId,
      });
      return res['data'].map<CityModel>((item) => CityModel.fromMap(item)).toList();
    } catch (e) {
      return [];
    }
  }

  Future<List<LocationModel>> fetchLocations(int loadingCityId) async {
    try {
      var res = await dioClient.get('/api/customer/shipment-costs/locations', queryParameters: {
        'loading_city_id': loadingCityId,
      });
      return res['data'].map<LocationModel>((item) => LocationModel.fromMap(item)).toList();
    } catch (e) {
      return [];
    }
  }

  Future<List<DestinationModel>> fetchDestinations() async {
    try {
      var res = await dioClient.get(
        '/api/customer/shipment-costs/destination',
      );
      return res['data'].map<DestinationModel>((item) => DestinationModel.fromMap(item)).toList();
    } catch (e) {
      return [];
    }
  }

  Future<MixShoppingCalculationModel?> getCalculation(Map<String, dynamic> data) async {
    try {
      var res = await dioClient.post(
        '/api/customer/shipment-costs/special-calculation',
        data: data,
      );
      return MixShoppingCalculationModel.fromMap(res['data']);
    } catch (e) {
      return const MixShoppingCalculationModel(
        towing: 0,
        shipping: 0,
        clearance: 0,
        tdsCharges: 0,
        taxDuty: 0,
        fullSizeSUVs: 0,
        manheimAdesa: 0,
        majorAccident: 0,
        moreThan10000AED: 0,
        locations: {},
        loadingCities: {},
        exchangeRate: 0,
        notSupported: true,
      );
    }
  }

  Future<File> downloadPDF() async {
    try {
      Directory tempDir = await getTemporaryDirectory();
      String tempPath = tempDir.path;
      File file = File('$tempPath/mix-shipping-rates.pdf');
      final response = await dioClient.get(
        '/api/customer/shipment-costs/shipmentRatePdf',
        options: Options(responseType: ResponseType.bytes),
      );
      await file.writeAsBytes(response);
      return file;
    } catch (e) {
      throw Exception('error downloading Mix shipping rates');
    }
  }
}
