import 'dart:async';

import 'package:auction_payments_repository/auction_payments_repository.dart';
import 'package:dio_client/dio_client.dart';

class AuctionPaymentsRepository {
  final String _url = '/api/customer/auction-payments';
  final DioClient dioClient;

  AuctionPaymentsRepository({
    required this.dioClient,
  });

  Future<List<AuctionPaymentModel>> getPayments(AuctionPaymentsFilter transactionsFilter) async {
    try {
      final response = await dioClient.get(
        _url,
        queryParameters: transactionsFilter.toMap(),
      );
      return response['data'].map<AuctionPaymentModel>((item) => AuctionPaymentModel.fromMap(item)).toList();
    } catch (e) {
      throw Exception('error fetching auction payments');
    }
  }
}
