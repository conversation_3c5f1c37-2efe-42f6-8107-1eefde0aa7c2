import 'package:equatable/equatable.dart';
import 'package:payments_repository/payments_repository.dart';

class AuctionPaymentModel extends Equatable {
  // info
  final int id;

  final String lotNumber;
  final String vin;
  final double price;

  final List<PaymentReceived<AuctionPaymentModel>> payments;

  const AuctionPaymentModel({
    // info
    required this.id,
    required this.lotNumber,
    required this.vin,
    required this.price,
    required this.payments,
  });

  factory AuctionPaymentModel.fromMap(Map<String, dynamic> map) {
    AuctionPaymentModel payment = AuctionPaymentModel(
      id: map["id"],
      lotNumber: map['lot_number'],
      vin: map['vin'],
      price: map['price'] != null ? map["price"].toDouble() : 0,
      payments: [],
    );
    List<PaymentReceived<AuctionPaymentModel>> payments = map['payments'] != null
        ? map['payments']
            .map<PaymentReceived<AuctionPaymentModel>>(
              (item) => PaymentReceived<AuctionPaymentModel>.fromMap(
                item,
                passedParent: payment,
              ),
            )
            .toList()
        : [];
    payment.payments.addAll(payments);

    return payment;
  }

  double getAmountApplied() {
    return payments.fold<double>(0, (prev, element) => prev + element.amountApplied);
  }

  double getBalance() {
    return getAmountApplied() - price;
  }

  Map<String, dynamic> toMap() {
    return {};
  }

  AuctionPaymentModel copyWith({
    int? id,
    String? lotNumber,
    String? vin,
    double? price,
    List<PaymentReceived<AuctionPaymentModel>>? payments,
  }) {
    return AuctionPaymentModel(
      id: id ?? this.id,
      lotNumber: lotNumber ?? this.lotNumber,
      vin: vin ?? this.vin,
      price: price ?? this.price,
      payments: payments ?? this.payments,
    );
  }

  @override
  List<Object?> get props => [
        id,
      ];
}
