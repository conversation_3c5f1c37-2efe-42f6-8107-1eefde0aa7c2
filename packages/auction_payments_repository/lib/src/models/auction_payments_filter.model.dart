import 'dart:convert';

import 'package:equatable/equatable.dart';

class AuctionPaymentsFilter extends Equatable {
  final int page;
  final int perPage;
  final String search;
  final String type;

  const AuctionPaymentsFilter({
    this.page = 1,
    this.perPage = 10,
    this.search = '',
    this.type = 'auction',
  });

  factory AuctionPaymentsFilter.fromMap(Map<String, dynamic> map) {
    return AuctionPaymentsFilter(
      page: map['page'] ?? 1,
      perPage: map['per_page'] ?? 1,
      search: map['search'] ?? '',
      type: map['type'] ?? 'auction',
    );
  }

  AuctionPaymentsFilter copyWith({
    int? page,
    int? perPage,
    String? search,
    String? type,
  }) {
    return AuctionPaymentsFilter(
      page: page ?? this.page,
      perPage: perPage ?? this.perPage,
      search: search ?? this.search,
      type: type ?? this.type,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'page': page,
      'per_page': perPage,
      'search': search,
      'type': type,
    };
  }

  factory AuctionPaymentsFilter.fromJson(String json) => AuctionPaymentsFilter.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        page,
        perPage,
        search,
        type,
      ];
}
