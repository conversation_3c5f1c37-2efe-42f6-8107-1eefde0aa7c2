import 'dart:async';

import 'package:dio_client/dio_client.dart';
import 'package:secure_storage/secure_storage.dart';

enum AuthenticationStatus {
  unknown,
  authenticated,
  unauthenticated;

  String toJson() => name;
  static AuthenticationStatus from<PERSON>son(String json) => values.byName(json);
}

enum AuthenticationError {
  none,
  invalidCreds,
  unexpectedError;

  String toJson() => name;
  static AuthenticationError fromJson(String json) => values.byName(json);
}

class AuthenticationRepository {
  final _controller = StreamController<AuthenticationStatus>();
  final _errorController = StreamController<AuthenticationError>();

  final DioClient dioClient;
  final SecureStorage _secureStorage = SecureStorage();

  AuthenticationRepository({
    required this.dioClient,
  });

  Stream<AuthenticationStatus> get status async* {
    yield* _controller.stream;
  }

  Stream<AuthenticationError> get error async* {
    yield* _errorController.stream;
  }

  Future<void> logIn({
    required String username,
    required String password,
  }) async {
    try {
      final Map<String, dynamic> response = await dioClient.post("/api/customer/auth/login", data: {
        "email_username": username,
        "password": password,
      });
      await _secureStorage.persistTokan(response['access_token']);
      _controller.add(
        AuthenticationStatus.authenticated,
      );
    } on DioException catch (e) {
      switch (e.response!.statusCode) {
        case 403:
          _errorController.add(
            AuthenticationError.invalidCreds,
          );
          break;
        default:
          _errorController.add(
            AuthenticationError.unexpectedError,
          );
          break;
      }
    } catch (e) {
      _errorController.add(
        AuthenticationError.unexpectedError,
      );
    }
  }

  void logOut() async {
    await _secureStorage.deleteToken();
    await _secureStorage.deleteFromLocalStorage('locale');
    _controller.add(AuthenticationStatus.unauthenticated);
  }

  void dispose() => _controller.close();
}
