import 'package:equatable/equatable.dart';
import 'package:timeago/timeago.dart' as timeago;

class Announcement extends Equatable {
  // info
  final int announcementId;
  final int customerId;
  final AnnouncementData announcementData;

  // dates
  final DateTime createdAt;
  final DateTime? seenAt;

  const Announcement({
    // info
    required this.announcementId,
    required this.customerId,
    required this.announcementData,

    // dates
    required this.createdAt,
    this.seenAt,

    // relations
  });

  String getTimeAgo() {
    return timeago.format(createdAt);
  }

  factory Announcement.fromMap(Map<String, dynamic> map) {
    return Announcement(
      announcementId: map["announcement_id"],
      customerId: map["customer_id"],
      announcementData: AnnouncementData.fromMap(map['announcement']),
      createdAt: DateTime.parse(map['created_at']),
      seenAt: map['seen_at'] != null ? DateTime.parse(map['seen_at']) : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {};
  }

  Announcement copyWith({
    int? announcementId,
    int? customerId,
    AnnouncementData? announcementData,
    DateTime? createdAt,
    DateTime? seenAt,
  }) {
    return Announcement(
      announcementId: announcementId ?? this.announcementId,
      customerId: customerId ?? this.customerId,
      announcementData: announcementData ?? this.announcementData,
      createdAt: createdAt ?? this.createdAt,
      seenAt: seenAt ?? this.seenAt,
    );
  }

  @override
  List<Object?> get props => [
        announcementId,
        customerId,
        announcementData,
        createdAt,
        seenAt,
      ];
}

class AnnouncementData extends Equatable {
  // info
  final int id;
  final String title;
  final String description;

  const AnnouncementData({
    // info
    required this.id,
    required this.title,
    required this.description,
  });

  factory AnnouncementData.fromMap(Map<String, dynamic> map) {
    return AnnouncementData(
      id: map["id"],
      title: map["title"],
      description: map["description"],
    );
  }

  Map<String, dynamic> toMap() {
    return {};
  }

  AnnouncementData copyWith({
    int? id,
    String? title,
    String? description,
  }) {
    return AnnouncementData(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
      ];
}
