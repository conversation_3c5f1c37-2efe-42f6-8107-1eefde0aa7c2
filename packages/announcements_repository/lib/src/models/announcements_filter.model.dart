import 'dart:convert';

import 'package:equatable/equatable.dart';

class AnnouncementsFilter extends Equatable {
  final int page;
  final int perPage;

  const AnnouncementsFilter({
    this.page = 1,
    this.perPage = 10,
  });

  factory AnnouncementsFilter.fromMap(Map<String, dynamic> map) {
    return AnnouncementsFilter(
      page: map['page'] ?? 1,
      perPage: map['per_page'] ?? 1,
    );
  }

  AnnouncementsFilter copyWith({
    int? page,
    int? perPage,
    String? status,
    String? search,
  }) {
    return AnnouncementsFilter(
      page: page ?? this.page,
      perPage: perPage ?? this.perPage,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'page': page,
      'per_page': perPage,
    };
  }

  factory AnnouncementsFilter.fromJson(String json) => AnnouncementsFilter.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        page,
        perPage,
      ];
}
