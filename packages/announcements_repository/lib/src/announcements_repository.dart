import 'dart:async';

import 'package:announcements_repository/src/models/models.dart';
import 'package:dio_client/dio_client.dart';

class AnnouncementsRepository {
  final String _url = '/api/customer/announcements';
  final DioClient dioClient;

  AnnouncementsRepository({
    required this.dioClient,
  });

  Future<List<Announcement>> getAnnouncement(AnnouncementsFilter announcementsFilter) async {
    try {
      final response = await dioClient.get(
        _url,
        queryParameters: announcementsFilter.toMap(),
      );
      return response['data'].map<Announcement>((item) => Announcement.fromMap(item)).toList();
    } catch (e) {
      throw Exception('error fetching Announcement');
    }
  }

  Future<bool> markAllAsRead() async {
    try {
      await dioClient.patch(
        _url,
      );
      return true;
    } catch (e) {
      throw Exception('error fetching Announcement');
    }
  }
}
