import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';

extension CustomIntFunctions2 on double {
  String formatComma() {
    return NumberFormat('###,###,###,###').format(this);
  }
}

class TowingRate extends Equatable {
  // info
  final int id;
  final TowingRateType towingRateType;
  final String stateName;
  final String branchName;
  final String cityName;
  final double? ga;
  final double? ca;
  final double? tx;
  final double? nj;
  final double? ba;
  // dates

  const TowingRate({
    // info
    required this.id,
    required this.towingRateType,
    required this.stateName,
    required this.branchName,
    required this.cityName,
    this.ga,
    this.ca,
    this.tx,
    this.nj,
    this.ba,
  });

  factory TowingRate.fromMap(Map<String, dynamic> map) {
    return TowingRate(
      id: map["stateid"],
      towingRateType: TowingRateType.fromJson(map['type'] ?? ''),
      stateName: map['state_name'],
      branchName: map['branch_name'],
      cityName: map['city_name'],
      ga: map['ga']?.toDouble(),
      ca: map['ca']?.toDouble(),
      tx: map['tx']?.toDouble(),
      nj: map['nj']?.toDouble(),
      ba: map['ba']?.toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {};
  }

  @override
  List<Object?> get props => [
        id,
        towingRateType,
        stateName,
        branchName,
        cityName,
        ga,
        ca,
        tx,
        nj,
        ba,
      ];
}

enum TowingRateType {
  complete, // open
  halfcut; // past_due

  String toJson() {
    switch (name) {
      case 'complete':
        return 'complete';
      case 'halfcut':
        return 'halfcut';
      default:
        return 'complete';
    }
  }

  static TowingRateType fromJson(String json) {
    switch (json) {
      case 'complete':
        return TowingRateType.complete;
      case 'halfcut':
        return TowingRateType.halfcut;
      default:
        return TowingRateType.complete;
    }
  }
}
