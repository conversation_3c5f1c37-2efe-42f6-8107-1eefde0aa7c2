import 'dart:convert';

import 'package:equatable/equatable.dart';

class TowingRatesFilter extends Equatable {
  final int page;
  final int perPage;
  final String search;
  final String tab;

  const TowingRatesFilter({
    this.page = 1,
    this.perPage = 20000,
    this.search = '',
    this.tab = 'complete',
  });

  factory TowingRatesFilter.fromMap(Map<String, dynamic> map) {
    return TowingRatesFilter(
      page: map['page'] ?? 1,
      perPage: map['per_page'] ?? 1,
      search: map['search'] ?? '',
      tab: map['tab'] ?? '',
    );
  }

  TowingRatesFilter copyWith({
    int? page,
    int? perPage,
    String? search,
    String? tab,
  }) {
    return TowingRatesFilter(
      page: page ?? this.page,
      perPage: perPage ?? this.perPage,
      search: search ?? this.search,
      tab: tab ?? this.tab,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'page': page,
      'per_page': perPage,
      'search': search,
      'tab': tab,
      'filterData': '{}',
    };
  }

  factory TowingRatesFilter.fromJson(String json) => TowingRatesFilter.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        page,
        perPage,
        search,
        tab,
      ];
}
