import 'dart:async';
import 'dart:io';

import 'package:dio_client/dio_client.dart';
import 'package:path_provider/path_provider.dart';
import 'package:towing_rates_repository/towing_rates_repository.dart';

class TowingRatesRepository {
  final String _url = '/api/customer/towing-rates';
  final String _towingRatePDFURL = '/api/customer/towing-rates/towingRatePdf';
  final String _halfcutRatePDFURL = '/api/customer/towing-rates/halfCutRatePdf';
  final DioClient dioClient;

  TowingRatesRepository({
    required this.dioClient,
  });

  Future<List<TowingRate>> getTowingRates(TowingRatesFilter towingRatesFilter) async {
    try {
      final response = await dioClient.get(
        _url,
        queryParameters: towingRatesFilter.toMap(),
      );
      return response['data'].map<TowingRate>((item) => TowingRate.fromMap(item)).toList();
    } catch (e) {
      throw Exception('error fetching towing_rates');
    }
  }

  Future<File> getTowingRatePDF() async {
    try {
      Directory tempDir = await getTemporaryDirectory();
      String tempPath = tempDir.path;
      File file = File('$tempPath/Complete towing rate.pdf');
      final response = await dioClient.get(
        _towingRatePDFURL,
        options: Options(responseType: ResponseType.bytes),
      );
      await file.writeAsBytes(response);
      return file;
    } catch (e) {
      throw Exception('error downloading towing rates');
    }
  }

  Future<File> getHalfcutRatePDF() async {
    try {
      Directory tempDir = await getTemporaryDirectory();
      String tempPath = tempDir.path;
      File file = File('$tempPath/Halfcut towing rate.pdf');
      final response = await dioClient.get(
        _halfcutRatePDFURL,
        options: Options(responseType: ResponseType.bytes),
      );
      await file.writeAsBytes(response);
      return file;
    } catch (e) {
      throw Exception('error downloading halfcut rates');
    }
  }
}
