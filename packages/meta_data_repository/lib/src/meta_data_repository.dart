import 'dart:async';

import 'package:dio_client/dio_client.dart';

class MetaDataRepository {
  final DioClient dioClient;

  final String _countsUrl = "/api/customer/statistics/sidebarCounts";
  final String _paymentStatusUrl = "/api/customer/invoices/paymentStatus";
  final String _invoicePaymentStatusUrl = "/api/customer/invoices/invoiceTotal";
  final String _mixShippingPaymentStatusUrl = "/api/customer/mix-shipping/MixInvoiceTotal";
  final String _vehicleSummaryUrl = "/api/customer/statistics/vehicleSummary";
  final String _shipmentSummaryUrl = "/api/customer/statistics/shipmentSummary";

  MetaDataRepository({
    required this.dioClient,
  });

  Future<Map<String, dynamic>?> getCounts({required String key}) async {
    try {
      final Map<String, dynamic> response = await dioClient.get(
        _countsUrl,
        queryParameters: {'key': key},
      );
      return response;
    } catch (e) {
      return null;
    }
  }

  Future<Map<String, dynamic>?> getPaymentStatus() async {
    try {
      final Map<String, dynamic> response = await dioClient.get(
        _paymentStatusUrl,
      );
      return response['data'];
    } catch (e) {
      return null;
    }
  }

  Future<Map<String, dynamic>?> getInvoicePaymentStatus() async {
    try {
      final Map<String, dynamic> response = await dioClient.get(
        _invoicePaymentStatusUrl,
      );
      return response['data'];
    } catch (e) {
      return null;
    }
  }

  Future<Map<String, dynamic>?> getMixShippingPaymentStatus() async {
    try {
      final Map<String, dynamic> response = await dioClient.get(
        _mixShippingPaymentStatusUrl,
      );
      return response['data'];
    } catch (e) {
      return null;
    }
  }

  Future<List<dynamic>?> getVehicleSummary() async {
    try {
      final Map<String, dynamic> response = await dioClient.get(
        _vehicleSummaryUrl,
      );
      return response['data'];
    } catch (e) {
      return null;
    }
  }

  Future<List<dynamic>?> getShipmentSummary() async {
    try {
      final Map<String, dynamic> response = await dioClient.get(
        _shipmentSummaryUrl,
      );
      return response['data'];
    } catch (e) {
      return null;
    }
  }
}
