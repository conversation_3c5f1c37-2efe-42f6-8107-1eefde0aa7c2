import 'dart:async';
import 'dart:io';

import 'package:dio_client/dio_client.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shipments_repository/shipments_repository.dart';

class ShipmentsRepository {
  final DioClient dioClient;
  final String _shipmentsURL = '/api/customer/shipments';
  final String _billOfLoadingPdfURL = '/api/customer/shipments/generatepdf/billOfLoading';

  ShipmentsRepository({
    required this.dioClient,
  });

  Future<List<Shipment>> getShipments(ShipmentsFilter shipmentsFilter) async {
    try {
      final response = await dioClient.get(
        _shipmentsURL,
        queryParameters: shipmentsFilter.toMap(),
      );
      return response['data'].map<Shipment>((item) => Shipment.fromMap(item)).toList();
    } catch (e) {
      throw Exception('error fetching shipments');
    }
  }

  Future<File> getbillOfLoadingPDF(int id) async {
    try {
      Directory tempDir = await getTemporaryDirectory();
      String tempPath = tempDir.path;
      File file = File('$tempPath/vehicle-BOL-$id.pdf');
      final response = await dioClient.get(
        '$_billOfLoadingPdfURL/$id',
        options: Options(responseType: ResponseType.bytes),
      );
      await file.writeAsBytes(response);
      return file;
    } catch (e) {
      throw Exception('error downloading BOL');
    }
  }
}
