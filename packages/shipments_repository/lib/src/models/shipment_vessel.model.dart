import 'package:equatable/equatable.dart';

class ShipmentVessel extends Equatable {
  // info
  final DateTime? etd;
  final int? portOfLoading;
  final String? location;

  const ShipmentVessel({
    required this.etd,
    required this.portOfLoading,
    required this.location,
  });

  factory ShipmentVessel.fromMap(Map<String, dynamic> json) {
    return ShipmentVessel(
      etd: json["etd"] != null ? DateTime.parse(json["etd"]) : null,
      portOfLoading: json["port_of_loading"],
      location: json["locations"]["name"],
    );
  }

  Map<String, dynamic> toMap() => {
        "etd": etd?.toIso8601String(),
        "port_of_loading": portOfLoading,
        "location": location,
      };

  @override
  List<Object?> get props => [
        etd,
        portOfLoading,
        location,
      ];
}
