import 'package:equatable/equatable.dart';
import 'package:shipments_repository/src/models/shipment_booking.model.dart';

class Shipment extends Equatable {
  // info
  final int id;
  final String containerNumber;
  final ShipmentBooking bookings;
  final String bookingSuffix;
  final String? noUnitsLoad;
  final String? measurement;
  final ShipmentState shipmentState;
  final String? aesItnNumber;
  final String? trackingContatiner;
  final DateTime? containerIdUpdateDate;
  final String? billOfLoadingNumber;
  final String? sealNumber;
  final String? actions;
  final String? invoiceNumber;
  final String company;
  final String? amount;
  final String? photoLink;
  final String? status;
  final String? pinIn;
  final String? pinOut;
  final bool? ingate;
  final String? clearanceInvoiceLink;
  final DateTime? loadingDate;
  final String? coverPhoto;

  const Shipment({
    required this.id,
    required this.containerNumber,
    required this.noUnitsLoad,
    required this.bookingSuffix,
    required this.company,
    required this.measurement,
    required this.bookings,
    required this.shipmentState,
    this.aesItnNumber,
    this.trackingContatiner,
    this.containerIdUpdateDate,
    this.billOfLoadingNumber,
    this.sealNumber,
    this.actions,
    this.invoiceNumber,
    this.amount,
    this.photoLink,
    this.status,
    this.pinIn,
    this.pinOut,
    this.ingate,
    this.clearanceInvoiceLink,
    this.loadingDate,
    this.coverPhoto,
  });

  factory Shipment.fromMap(Map<String, dynamic> json) {
    return Shipment(
      id: json["id"],
      containerNumber: json["container_number"] ?? '',
      bookingSuffix: json["booking_suffix"] ?? '',
      company: json["companies"]['name'] ?? '',
      measurement: json["measurement"],
      noUnitsLoad: json["no_units_load"],
      shipmentState: ShipmentState.fromJson(
        json["status"],
      ),
      bookings: ShipmentBooking.fromMap(
        json["bookings"],
      ),
      aesItnNumber: json["aes_itn_number"],
      trackingContatiner: json["tracking_contatiner"],
      containerIdUpdateDate: json["container_id_update_date"] == null
          ? null
          : DateTime.parse(
              json["container_id_update_date"],
            ),
      billOfLoadingNumber: json["bill_of_loading_number"],
      sealNumber: json["seal_number"],
      actions: json["actions"],
      invoiceNumber: json["invoice_number"],
      amount: json["amount"],
      photoLink: json["photo_link"],
      status: json["status"],
      pinIn: json["pin_in"],
      pinOut: json["pin_out"],
      ingate: json["ingate"],
      clearanceInvoiceLink: json["clearance_invoice_link"],
      loadingDate: json["loading_date"] == null
          ? null
          : DateTime.parse(
              json["loading_date"],
            ),
      coverPhoto: json["cover_photo"],
    );
  }

  Map<String, dynamic> toMap() => {
        "id": id,
        "container_number": containerNumber,
        "booking_suffix": bookingSuffix,
        "aes_itn_number": aesItnNumber,
        "tracking_contatiner": trackingContatiner,
        "container_id_update_date":
            "${containerIdUpdateDate!.year.toString().padLeft(4, '0')}-${containerIdUpdateDate!.month.toString().padLeft(2, '0')}-${containerIdUpdateDate!.day.toString().padLeft(2, '0')}",
        "bill_of_loading_number": billOfLoadingNumber,
        "seal_number": sealNumber,
        "actions": actions,
        "measurement": measurement,
        "no_units_load": noUnitsLoad,
        "invoice_number": invoiceNumber,
        "bookings": bookings.toMap(),
        "amount": amount,
        "photo_link": photoLink,
        "status": status,
        "pin_in": pinIn,
        "pin_out": pinOut,
        "ingate": ingate,
        "clearance_invoice_link": clearanceInvoiceLink,
        "loading_date": loadingDate?.toIso8601String(),
        "cover_photo": coverPhoto,
      };

  @override
  List<Object?> get props => [
        id,
        containerNumber,
        bookingSuffix,
        company,
        coverPhoto,
      ];

  String getBookingNo() {
    return bookingSuffix != '' ? "${bookings.bookingNumber}-$bookingSuffix" : bookings.bookingNumber ?? '';
  }
}

enum ShipmentState {
  onTheWay, // on_the_way
  atLoading, //at_loading
  arrived; //arrived

  String toJson() {
    switch (name) {
      case 'onTheWay':
        return 'on_the_way';
      case 'atLoading':
        return 'at_loading';
      case 'arrived':
        return 'arrived';
      default:
        return 'arrived';
    }
  }

  static ShipmentState fromJson(String json) {
    switch (json) {
      case 'on_the_way':
      case 'at_the_dock':
      case 'checked':
      case 'final_checked':
        return ShipmentState.onTheWay;
      case 'at_loading':
        return ShipmentState.atLoading;
      case 'arrived':
        return ShipmentState.arrived;
      default:
        return ShipmentState.arrived;
    }
  }
}
