import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:shipments_repository/shipments_repository.dart';

class ShipmentsFilter extends Equatable {
  final int page;
  final int perPage;
  final String state;
  final String search;
  final ShipmentFilterData? filterData;

  const ShipmentsFilter({
    this.page = 1,
    this.perPage = 10,
    this.state = '',
    this.search = '',
    this.filterData,
  });

  ShipmentsFilter copyWith({
    int? page,
    int? perPage,
    String? state,
    String? search,
    ShipmentFilterData? filterData,
    bool setFilterDataNull = false,
  }) {
    return ShipmentsFilter(
      page: page ?? this.page,
      perPage: perPage ?? this.perPage,
      state: state ?? this.state,
      search: search ?? this.search,
      filterData: setFilterDataNull ? null : filterData ?? this.filterData,
    );
  }

  factory ShipmentsFilter.fromMap(Map<String, dynamic> map) {
    return ShipmentsFilter(
      page: map['page'] ?? 1,
      perPage: map['per_page'] ?? 1,
      state: map['status'] ?? '',
      search: map['search'] ?? '',
      filterData: map['filterData'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'page': page,
      'per_page': perPage,
      'status': state,
      'search': search,
      if (filterData != null) 'filterData': jsonEncode(filterData?.toMap()),
    };
  }

  factory ShipmentsFilter.fromJson(String json) => ShipmentsFilter.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        page,
        perPage,
        state,
        search,
        filterData,
      ];
}

class ShipmentFilterData extends Equatable {
  final List<ShipmentState> status;
  final FilterPointOfLoadingShipment? filterPointOfLoading;
  final ShipmentFilterContainer? filterContainer;
  final ShipmentFilterBooking? filterBooking;
  final DateTime? loadingFromDate;
  final DateTime? loadingToDate;
  final DateTime? etdFromDate;
  final DateTime? etdToDate;
  final List containerIds;

  const ShipmentFilterData({
    this.status = const [],
    this.filterPointOfLoading,
    this.filterContainer,
    this.filterBooking,
    this.loadingFromDate,
    this.loadingToDate,
    this.etdFromDate,
    this.etdToDate,
    this.containerIds = const [],
  });

  ShipmentFilterData copyWith({
    List<ShipmentState>? status,
    FilterPointOfLoadingShipment? filterPointOfLoading,
    ShipmentFilterContainer? filterContainer,
    ShipmentFilterBooking? filterBooking,
    DateTime? loadingFromDate,
    DateTime? loadingToDate,
    DateTime? etdFromDate,
    DateTime? etdToDate,
    List? containerIds,
  }) {
    return ShipmentFilterData(
      status: status ?? this.status,
      filterPointOfLoading: filterPointOfLoading ?? this.filterPointOfLoading,
      filterContainer: filterContainer ?? this.filterContainer,
      filterBooking: filterBooking ?? this.filterBooking,
      loadingFromDate: loadingFromDate ?? this.loadingFromDate,
      loadingToDate: loadingToDate ?? this.loadingToDate,
      etdFromDate: etdFromDate ?? this.etdFromDate,
      etdToDate: etdToDate ?? this.etdToDate,
      containerIds: containerIds ?? this.containerIds,
    );
  }

  factory ShipmentFilterData.fromMap(Map<String, dynamic> map) {
    return ShipmentFilterData(
      status: map['status'],
      filterPointOfLoading: map['point_of_loading'],
      filterContainer: map['container_no'],
      filterBooking: map['booking_no'],
      loadingFromDate: map['loading_from_date'],
      loadingToDate: map['loading_to_date'],
      etdFromDate: map['etd_from_date'],
      etdToDate: map['etd_to_date'],
      containerIds: map['container_ids'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (status.isNotEmpty)
        'status': status
            .map(
              (st) => st.toJson(),
            )
            .toList(),
      if (filterPointOfLoading != null) 'bookings.vessels.int@@port_of_loading': [filterPointOfLoading?.id],
      if (filterContainer != null || containerIds.isNotEmpty)
        'id': [
          if (filterContainer != null) filterContainer?.id,
          ...containerIds,
        ],
      if (filterBooking != null) 'booking_id': [filterBooking?.id],
      if (loadingFromDate != null || loadingToDate != null)
        'loading_date': {
          if (loadingFromDate != null)
            'from': DateFormat('yyyy-MM-dd').format(
              loadingFromDate ?? DateTime.now(),
            ),
          if (loadingToDate != null)
            'to': DateFormat('yyyy-MM-dd').format(
              loadingToDate ?? DateTime.now(),
            ),
        },
      if (etdFromDate != null || etdToDate != null)
        'etd_date': {
          if (etdFromDate != null)
            'from': DateFormat('yyyy-MM-dd').format(
              etdFromDate ?? DateTime.now(),
            ),
          if (etdToDate != null)
            'to': DateFormat('yyyy-MM-dd').format(
              etdToDate ?? DateTime.now(),
            ),
        },
    };
  }

  factory ShipmentFilterData.fromJson(String json) => ShipmentFilterData.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        status,
        filterPointOfLoading,
        filterContainer,
        filterBooking,
        loadingFromDate,
        loadingToDate,
        etdFromDate,
        etdToDate,
        containerIds
      ];
}

class FilterPointOfLoadingShipment extends Equatable {
  final int id;
  final String name;

  const FilterPointOfLoadingShipment({
    required this.id,
    required this.name,
  });

  factory FilterPointOfLoadingShipment.fromMap(Map<String, dynamic> map) {
    return FilterPointOfLoadingShipment(
      id: map['id'],
      name: map['name'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
    };
  }

  factory FilterPointOfLoadingShipment.fromJson(String json) => FilterPointOfLoadingShipment.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [id, name];
}

class ShipmentFilterContainer extends Equatable {
  final int id;
  final String containerNo;

  const ShipmentFilterContainer({
    required this.id,
    required this.containerNo,
  });

  factory ShipmentFilterContainer.fromMap(Map<String, dynamic> map) {
    return ShipmentFilterContainer(
      id: map['id'],
      containerNo: map['container_number'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'container_number': containerNo,
    };
  }

  factory ShipmentFilterContainer.fromJson(String json) => ShipmentFilterContainer.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [id, containerNo];
}

class ShipmentFilterBooking extends Equatable {
  final int id;
  final String bookingNo;

  const ShipmentFilterBooking({
    required this.id,
    required this.bookingNo,
  });

  factory ShipmentFilterBooking.fromMap(Map<String, dynamic> map) {
    return ShipmentFilterBooking(
      id: map['id'],
      bookingNo: map['booking_number'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'booking_number': bookingNo,
    };
  }

  factory ShipmentFilterBooking.fromJson(String json) => ShipmentFilterBooking.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [id, bookingNo];
}
