import 'package:equatable/equatable.dart';
import 'package:shipments_repository/src/models/shipment_vessel.model.dart';

class ShipmentBooking extends Equatable {
  // info
  final String? bookingNumber;
  final String? size;
  final int? portOfDischarge;
  final String? destination;
  final DateTime? eta;
  final ShipmentVessel? vessel;

  const ShipmentBooking({
    required this.bookingNumber,
    required this.size,
    required this.eta,
    required this.portOfDischarge,
    required this.destination,
    required this.vessel,
  });
  factory ShipmentBooking.fromMap(Map<String, dynamic> json) {
    return ShipmentBooking(
      bookingNumber: json["booking_number"],
      size: json["size"],
      eta: json["eta"] != null ? DateTime.parse(json["eta"]) : null,
      portOfDischarge: json["port_of_discharge"],
      destination: json["destinations"]["name"],
      vessel: ShipmentVessel.fromMap(json["vessels"]),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      "eta": eta?.toIso8601String(),
      "booking_number": bookingNumber,
      "port_of_discharge": portOfDischarge,
      "destinations": destination,
      "size": size,
      "vessels": vessel,
    };
  }

  @override
  List<Object?> get props => [
        bookingNumber,
      ];
}
