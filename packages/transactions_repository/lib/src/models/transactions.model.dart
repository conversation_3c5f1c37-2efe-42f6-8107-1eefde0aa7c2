import 'package:equatable/equatable.dart';
import 'package:transactions_repository/transactions_repository.dart';

class TransactionModel extends Equatable {
  // info
  final int id;
  final TransactionType type;
  final TransactionStatus status;
  final TransactionPaymentMethod paymentMethod;
  final double amount;
  final String description;
  final TransactionCategory category;
  final List<TransactionContainerVehicle> transactionContainerVehicles;
  // dates
  final DateTime createdAt;

  const TransactionModel({
    // info
    required this.id,
    required this.type,
    required this.status,
    required this.paymentMethod,
    required this.amount,
    required this.description,
    required this.category,
    required this.transactionContainerVehicles,

    // dates
    required this.createdAt,
  });

  factory TransactionModel.fromMap(Map<String, dynamic> map) {
    return TransactionModel(
      id: map["id"],
      type: TransactionType.fromJson(map['type']),
      status: TransactionStatus.fromJson(map['status']),
      paymentMethod: TransactionPaymentMethod.fromJson(map['payment_method']),
      amount: map['amount'] != null ? map['amount'].toDouble() : 0,
      description: map['description'] ?? '',
      category: TransactionCategory.fromMap(map['category']),
      createdAt: DateTime.parse(map['created_at']),
      transactionContainerVehicles: map['transaction_container_vehicle']
          .map<TransactionContainerVehicle>(
            (item) => TransactionContainerVehicle.fromMap(item),
          )
          .toList(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      "id": id,
      'type': type.toJson(),
      'status': status.toJson(),
      'payment_method': paymentMethod.toJson(),
      'amount': amount,
      'description': description,
      'created_at': createdAt.toIso8601String(),
      'category': category.toJson(),
    };
  }

  TransactionModel copyWith({
    int? id,
    DateTime? createdAt,
    TransactionType? type,
    TransactionStatus? status,
    TransactionPaymentMethod? paymentMethod,
    double? amount,
    String? description,
    TransactionCategory? category,
    List<TransactionContainerVehicle>? transactionContainerVehicles,
  }) {
    return TransactionModel(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      type: type ?? this.type,
      status: status ?? this.status,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      category: category ?? this.category,
      transactionContainerVehicles: transactionContainerVehicles ?? this.transactionContainerVehicles,
    );
  }

  @override
  List<Object?> get props => [
        id,
        createdAt,
        type,
        status,
        paymentMethod,
        amount,
        description,
        category,
        transactionContainerVehicles,
      ];
}

enum TransactionType {
  credit,
  debit;

  String toJson() {
    switch (name) {
      case 'credit':
        return 'credit';
      case 'debit':
        return 'debit';
      default:
        return 'credit';
    }
  }

  static TransactionType fromJson(String json) {
    switch (json) {
      case 'credit':
        return TransactionType.credit;
      case 'debit':
        return TransactionType.debit;
      default:
        return TransactionType.debit;
    }
  }
}

enum TransactionStatus {
  pending,
  reviewed,
  canceled;

  String toJson() {
    switch (name) {
      case 'pending':
        return 'pending';
      case 'reviewed':
        return 'reviewed';
      case 'canceled':
        return 'canceled';
      default:
        return 'pending';
    }
  }

  static TransactionStatus fromJson(String json) {
    switch (json) {
      case 'pending':
        return TransactionStatus.pending;
      case 'reviewed':
        return TransactionStatus.reviewed;
      case 'canceled':
        return TransactionStatus.canceled;
      default:
        return TransactionStatus.pending;
    }
  }
}

enum TransactionPaymentMethod {
  cash,
  bankTransfer;

  String toJson() {
    switch (name) {
      case 'cash':
        return 'cash';
      case 'bankTransfer':
        return 'bank_transfer';
      default:
        return 'cash';
    }
  }

  static TransactionPaymentMethod fromJson(String json) {
    switch (json) {
      case 'cash':
        return TransactionPaymentMethod.cash;
      case 'bank_transfer':
        return TransactionPaymentMethod.bankTransfer;
      default:
        return TransactionPaymentMethod.cash;
    }
  }
}
