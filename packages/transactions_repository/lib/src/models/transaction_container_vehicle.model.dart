import 'dart:convert';

import 'package:equatable/equatable.dart';

class TransactionContainerVehicle extends Equatable {
  final int id;
  final int referenceId;
  final int type;
  final Map? vehicle;
  final Map? container;

  const TransactionContainerVehicle({
    required this.id,
    required this.referenceId,
    required this.type,
    this.vehicle,
    this.container,
  });

  factory TransactionContainerVehicle.fromMap(Map<String, dynamic> map) {
    return TransactionContainerVehicle(
      id: map['id'],
      referenceId: map['reference_id'],
      type: map['type'],
      vehicle: map['vehicle'],
      container: map['container'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': referenceId,
      'type': type,
    };
  }

  factory TransactionContainerVehicle.fromJson(String json) => TransactionContainerVehicle.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [id, referenceId, type, container, vehicle];
}
