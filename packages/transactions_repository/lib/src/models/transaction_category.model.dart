import 'dart:convert';

import 'package:equatable/equatable.dart';

class TransactionCategory extends Equatable {
  final int id;
  final String name;

  const TransactionCategory({
    required this.id,
    required this.name,
  });

  factory TransactionCategory.fromMap(Map<String, dynamic> map) {
    return TransactionCategory(
      id: map['id'],
      name: map['name'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
    };
  }

  factory TransactionCategory.fromJson(String json) => TransactionCategory.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [id, name];
}
