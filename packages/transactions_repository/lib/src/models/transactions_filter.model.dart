import 'dart:convert';

import 'package:equatable/equatable.dart';

class TransactionsFilter extends Equatable {
  final int page;
  final int perPage;
  final String search;

  const TransactionsFilter({
    this.page = 1,
    this.perPage = 10,
    this.search = '',
  });

  factory TransactionsFilter.fromMap(Map<String, dynamic> map) {
    return TransactionsFilter(
      page: map['page'] ?? 1,
      perPage: map['per_page'] ?? 1,
      search: map['search'] ?? '',
    );
  }

  TransactionsFilter copyWith({
    int? page,
    int? perPage,
    String? search,
  }) {
    return TransactionsFilter(
      page: page ?? this.page,
      perPage: perPage ?? this.perPage,
      search: search ?? this.search,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'page': page,
      'per_page': perPage,
      'search': search,
    };
  }

  factory TransactionsFilter.fromJson(String json) => TransactionsFilter.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        page,
        perPage,
        search,
      ];
}
