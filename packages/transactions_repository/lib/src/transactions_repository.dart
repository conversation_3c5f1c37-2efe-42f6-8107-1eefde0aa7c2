import 'dart:async';

import 'package:dio_client/dio_client.dart';
import 'package:transactions_repository/src/models/models.dart';

class TransactionsRepository {
  final String _url = '/api/customer/transactions';
  final DioClient dioClient;

  TransactionsRepository({
    required this.dioClient,
  });

  Future<List<TransactionModel>> getTransactions(TransactionsFilter transactionsFilter) async {
    try {
      final response = await dioClient.get(
        _url,
        queryParameters: transactionsFilter.toMap(),
      );
      return response['data'].map<TransactionModel>((item) => TransactionModel.fromMap(item)).toList();
    } catch (e) {
      throw Exception('error fetching Transactions');
    }
  }
}
