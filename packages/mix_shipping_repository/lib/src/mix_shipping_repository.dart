import 'dart:async';
import 'dart:io';

import 'package:dio_client/dio_client.dart';
import 'package:mix_shipping_repository/mix_shipping_repository.dart';
import 'package:path_provider/path_provider.dart';

class MixshippingRepository {
  final String _url = '/api/customer/mix-shipping/mixShipping';
  final String _invoicePdfURL = '/api/customer/mix-shipping/generatepdf/invoice';

  final DioClient dioClient;

  MixshippingRepository({
    required this.dioClient,
  });

  Future<List<MixShipping>> getMixshipping(MixShippingFilter mixShippingFilter) async {
    try {
      final response = await dioClient.get(
        _url,
        queryParameters: mixShippingFilter.toMap(),
      );
      return response['data'].map<MixShipping>((item) => MixShipping.fromMap(item)).toList();
    } catch (e) {
      throw Exception('error fetching mix shipping');
    }
  }

  Future<File> getInvoicePDF(int id) async {
    try {
      Directory tempDir = await getTemporaryDirectory();
      String tempPath = tempDir.path;
      File file = File('$tempPath/mix-shipping-invoice-$id.pdf');
      final response = await dioClient.get(
        '$_invoicePdfURL/$id',
        options: Options(responseType: ResponseType.bytes),
      );
      await file.writeAsBytes(response);
      return file;
    } catch (e) {
      throw Exception('error downloading invoice');
    }
  }
}
