import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:mix_shipping_repository/src/models/mix_shipping_vehicle.model.dart';

class MixShipping extends Equatable {
  // info
  final int id;
  final MixShippingType type;
  final String containerNumber; // containers.container_number
  final String invoiceNumber; // containers.invoice_number
  final String bookingNumber; // containers.bookings.booking_number
  final double exchangeRate;
  final String currency;
  final String company;
  final String? description;
  final MixShippingStatus mixShippingState;
  // dates
  final DateTime invDate;
  final DateTime? invDueDate;

  // relations
  final List<MixShippingVehicle> mixShippingVehicles;

  const MixShipping({
    // info
    required this.id,
    required this.type,
    required this.containerNumber,
    required this.invoiceNumber,
    required this.bookingNumber,
    required this.exchangeRate,
    required this.currency,
    required this.company,
    this.description,
    required this.mixShippingState,
    // dates
    required this.invDate,
    this.invDueDate,

    // relations
    required this.mixShippingVehicles,
  });

  double getTotalCost() {
    double cost = 0;
    for (var vehicle in mixShippingVehicles) {
      cost += vehicle.getTotalCost();
    }
    return cost;
  }

  String getTotalCostString() {
    double totalCost = getTotalCost();
    return totalCost > 0 ? NumberFormat('###,###,###,###.##').format(totalCost) : '0';
  }

  double getTotalCostAED() {
    double cost = 0;
    for (var vehicle in mixShippingVehicles) {
      cost += vehicle.getTotalCost();
    }
    return cost * exchangeRate;
  }

  String getTotalCostStringAED() {
    double totalCost = getTotalCostAED();
    return totalCost > 0 ? NumberFormat('###,###,###,###.##').format(totalCost) : '0';
  }

  double getTotalPaid() {
    double paid = 0;
    for (var vehicle in mixShippingVehicles) {
      paid += vehicle.getPaymentReceivedNumber();
    }
    return paid;
  }

  double getTotalPaidInvoiceCurrency() {
    double paid = getTotalPaid();
    if (type == MixShippingType.mix) {
      return paid * exchangeRate;
    } else {
      return paid;
    }
  }

  String getTotalPaidString() {
    double totalPaid = getTotalPaid();
    return totalPaid > 0 ? NumberFormat('###,###,###,###.##').format(totalPaid) : '0';
  }

  String getTotalPaidStringCurrency() {
    double totalPaid = getTotalPaidInvoiceCurrency();
    return totalPaid > 0 ? NumberFormat('###,###,###,###.##').format(totalPaid) : '0';
  }

  double getTotalDue() {
    return getTotalCost() - getTotalPaid();
  }

  String getTotalDueString() {
    double totalDue = getTotalDue();
    return totalDue > 0 ? NumberFormat('###,###,###,###.##').format(totalDue) : '0';
  }

  double getTotalDueAED() {
    return getTotalCostAED() - getTotalPaidInvoiceCurrency();
  }

  String getTotalDueStringAED() {
    double totalDue = getTotalDueAED();
    return totalDue > 0 ? NumberFormat('###,###,###,###.##').format(totalDue) : '0';
  }

  factory MixShipping.fromMap(Map<String, dynamic> map) {
    MixShipping mixShipping = MixShipping(
      id: map['id'],
      type: MixShippingType.fromJson(map['type']),
      containerNumber: map['containers']['container_number'],
      invoiceNumber: map['containers']['invoice_number'],
      bookingNumber: map['containers']['bookings']['booking_number'],
      exchangeRate: map['exchange_rate'].toDouble(),
      currency: map['currency'],
      company: map['companies']['name'],
      mixShippingState: MixShippingStatus.fromJson(map['status']),
      invDate: DateTime.parse(map['inv_date']),
      invDueDate: map['inv_due_date'] != null ? DateTime.parse(map['inv_due_date']) : null,
      description: map['description'],
      mixShippingVehicles: [],
    );

    List<MixShippingVehicle> mixShippingVehicles = map['mix_shipping_vehicles']
        .map<MixShippingVehicle>(
          (item) => MixShippingVehicle.fromMap(item, mixShipping: mixShipping),
        )
        .toList();

    mixShipping.mixShippingVehicles.addAll(mixShippingVehicles);
    return mixShipping;
  }

  Map<String, dynamic> toMap() {
    return {};
  }

  @override
  List<Object?> get props => [
        id,
        containerNumber,
        invoiceNumber,
        bookingNumber,
      ];
}

enum MixShippingStatus {
  open, // open
  paid, // paid
  pastDue; // past_due

  String toJson() {
    switch (name) {
      case 'open':
        return 'open';
      case 'paid':
        return 'paid';
      case 'pastDue':
        return 'past_due';
      default:
        return 'open';
    }
  }

  static MixShippingStatus fromJson(String json) {
    switch (json) {
      case 'open':
        return MixShippingStatus.open;
      case 'paid':
        return MixShippingStatus.paid;
      case 'past_due':
        return MixShippingStatus.pastDue;
      default:
        return MixShippingStatus.open;
    }
  }
}

enum MixShippingType {
  mix, // mix
  full; // full

  String toJson() {
    switch (name) {
      case 'mix':
        return 'mix';
      case 'full':
        return 'full';
      default:
        return 'mix';
    }
  }

  static MixShippingType fromJson(String json) {
    switch (json) {
      case 'mix':
        return MixShippingType.mix;
      case 'full':
        return MixShippingType.full;
      default:
        return MixShippingType.mix;
    }
  }
}
