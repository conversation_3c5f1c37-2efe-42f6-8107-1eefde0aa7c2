import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:mix_shipping_repository/mix_shipping_repository.dart';

class MixShippingFilter extends Equatable {
  final int page;
  final int perPage;
  final String status;
  final String search;
  final MixShippingFilterData? filterData;

  const MixShippingFilter({
    this.page = 1,
    this.perPage = 10,
    this.status = '',
    this.search = '',
    this.filterData,
  });

  MixShippingFilter copyWith({
    int? page,
    int? perPage,
    String? status,
    String? search,
    MixShippingFilterData? filterData,
    bool setFilterDataNull = false,
  }) {
    return MixShippingFilter(
      page: page ?? this.page,
      perPage: perPage ?? this.perPage,
      status: status ?? this.status,
      search: search ?? this.search,
      filterData: setFilterDataNull ? null : filterData ?? this.filterData,
    );
  }

  factory MixShippingFilter.fromMap(Map<String, dynamic> map) {
    return MixShippingFilter(
      page: map['page'] ?? 1,
      perPage: map['per_page'] ?? 1,
      status: map['status'] ?? '',
      search: map['search'] ?? '',
      filterData: map['filterData'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'page': page,
      'per_page': perPage,
      'status': status,
      'search': search,
      if (filterData != null) 'filterData': jsonEncode(filterData?.toMap()),
    };
  }

  factory MixShippingFilter.fromJson(String json) => MixShippingFilter.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        page,
        perPage,
        status,
        search,
        filterData,
      ];
}

class MixShippingFilterData extends Equatable {
  final List<MixShippingStatus> status;
  final MixShippingFilterContainer? filterContainer;
  final MixShippingFilterInvoiceNo? filterInvoiceNo;
  final DateTime? issueFromDate;
  final DateTime? issueToDate;
  final Map? invoiceAmount;
  final Map? paymentReceived;

  const MixShippingFilterData({
    this.status = const [],
    this.filterContainer,
    this.filterInvoiceNo,
    this.issueFromDate,
    this.issueToDate,
    this.invoiceAmount,
    this.paymentReceived,
  });

  MixShippingFilterData copyWith({
    List<MixShippingStatus>? status,
    MixShippingFilterContainer? filterContainer,
    MixShippingFilterInvoiceNo? filterInvoiceNo,
    DateTime? issueFromDate,
    DateTime? issueToDate,
    Map? invoiceAmount,
    Map? paymentReceived,
  }) {
    return MixShippingFilterData(
      status: status ?? this.status,
      filterContainer: filterContainer ?? this.filterContainer,
      filterInvoiceNo: filterInvoiceNo ?? this.filterInvoiceNo,
      issueFromDate: issueFromDate ?? this.issueFromDate,
      issueToDate: issueToDate ?? this.issueToDate,
      invoiceAmount: invoiceAmount ?? this.invoiceAmount,
      paymentReceived: paymentReceived ?? this.paymentReceived,
    );
  }

  factory MixShippingFilterData.fromMap(Map<String, dynamic> map) {
    return MixShippingFilterData(
      status: map['status'],
      filterContainer: map['container_no'],
      filterInvoiceNo: map['invoice_no'],
      issueFromDate: map['issue_from_date'],
      issueToDate: map['issue_to_date'],
      invoiceAmount: map['invoice_amount'],
      paymentReceived: map['payment_received'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (status.isNotEmpty)
        'status': status
            .map(
              (st) => st.toJson(),
            )
            .toList(),
      if (filterContainer != null) 'container_id': [filterContainer?.id],
      if (filterInvoiceNo != null) 'invoice_number': [filterInvoiceNo?.id],
      if (issueFromDate != null || issueToDate != null)
        'invoice_date': {
          if (issueFromDate != null)
            'from': DateFormat('yyyy-MM-dd').format(
              issueFromDate ?? DateTime.now(),
            ),
          if (issueToDate != null)
            'to': DateFormat('yyyy-MM-dd').format(
              issueToDate ?? DateTime.now(),
            ),
        },
      if (invoiceAmount != null) 'invoice_amount': invoiceAmount,
      if (paymentReceived != null) 'payment_received': paymentReceived,
    };
  }

  factory MixShippingFilterData.fromJson(String json) => MixShippingFilterData.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        status,
        filterContainer,
        filterInvoiceNo,
        issueFromDate,
        issueToDate,
        invoiceAmount,
        paymentReceived,
      ];
}

class MixShippingFilterContainer extends Equatable {
  final int id;
  final String containerNo;

  const MixShippingFilterContainer({
    required this.id,
    required this.containerNo,
  });

  factory MixShippingFilterContainer.fromMap(Map<String, dynamic> map) {
    return MixShippingFilterContainer(
      id: map['id'],
      containerNo: map['container_number'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'container_number': containerNo,
    };
  }

  factory MixShippingFilterContainer.fromJson(String json) => MixShippingFilterContainer.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [id, containerNo];
}

class MixShippingFilterInvoiceNo extends Equatable {
  final int id;
  final String invoiceNo;

  const MixShippingFilterInvoiceNo({
    required this.id,
    required this.invoiceNo,
  });

  factory MixShippingFilterInvoiceNo.fromMap(Map<String, dynamic> map) {
    return MixShippingFilterInvoiceNo(
      id: map['id'],
      invoiceNo: map['invoice_number'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoice_number': invoiceNo,
    };
  }

  factory MixShippingFilterInvoiceNo.fromJson(String json) => MixShippingFilterInvoiceNo.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [id, invoiceNo];
}
