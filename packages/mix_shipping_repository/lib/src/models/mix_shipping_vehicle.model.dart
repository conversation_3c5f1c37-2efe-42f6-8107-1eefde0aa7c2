import 'package:equatable/equatable.dart';
import 'package:mix_shipping_repository/src/models/mix_shipping.model.dart';
import 'package:payments_repository/payments_repository.dart';

class MixShippingVehicle extends Equatable {
  // info
  final String vin; // vehicles.vin
  final String lotNumber; // vehicles.lot_number
  final double clearance;
  final double freight;
  final double vatAndCustom;
  final double towAmount;
  final MixShipping mixShipping;

  final double? paymentAmount;
  // dates

  final DateTime? paymentDate;

  // relations
  final List<MixShippingVehicleCharges> mixShippingVehicleCharges;
  final List<PaymentReceived<MixShippingVehicle>> payments;

  const MixShippingVehicle({
    // info
    required this.vin,
    required this.lotNumber,
    required this.clearance,
    required this.freight,
    required this.vatAndCustom,
    required this.towAmount,
    this.paymentAmount,
    // dates
    this.paymentDate,
    this.mixShippingVehicleCharges = const [],
    required this.payments,
    required this.mixShipping,
    // relations
  });

  double getTotalVehicleCharges() {
    double charges = 0;
    for (var element in mixShippingVehicleCharges) {
      charges += element.value;
    }
    return charges;
  }

  double getTotalCost() {
    return clearance + freight + vatAndCustom + towAmount + getTotalVehicleCharges();
  }

  factory MixShippingVehicle.fromMap(Map<String, dynamic> map, {required MixShipping mixShipping}) {
    MixShippingVehicle vehicle = MixShippingVehicle(
      vin: map['vehicles']['vin'],
      lotNumber: map['vehicles']['lot_number'],
      clearance: map['clearance'] != null ? map['clearance'].toDouble() : 0.0,
      freight: map['freight'] != null ? map['freight'].toDouble() : 0.0,
      vatAndCustom: map['vat_and_custom'] != null ? map['vat_and_custom'].toDouble() : 0.0,
      towAmount: map['tow_amount'] != null ? map['tow_amount'].toDouble() : 0.0,
      paymentAmount: map['payment_amount'] != null ? map['payment_amount'].toDouble() : 0.0,
      paymentDate: map['payment_date'] != null ? DateTime.parse(map['payment_date']) : null,
      mixShippingVehicleCharges: map['mix_shipping_vehicle_charges']
          .map<MixShippingVehicleCharges>(
            (item) => MixShippingVehicleCharges(
              name: item['name'],
              value: item['value'] != null ? item['value'].toDouble() : 0.0,
            ),
          )
          .toList(),
      payments: [],
      mixShipping: mixShipping,
    );
    List<PaymentReceived<MixShippingVehicle>> payments = map['payments'] != null
        ? map['payments']
            .map<PaymentReceived<MixShippingVehicle>>(
                (item) => PaymentReceived<MixShippingVehicle>.fromMap(item, passedParent: vehicle))
            .toList()
        : [];

    vehicle.payments.addAll(payments);
    return vehicle;
  }

  double getPaymentReceivedNumber() {
    double totalAmountApplied = 0.0;

    for (PaymentReceived payment in payments) {
      totalAmountApplied += payment.amountApplied;
    }
    return totalAmountApplied;
  }

  Map<String, dynamic> toMap() {
    return {};
  }

  @override
  List<Object?> get props => [
        vin,
        lotNumber,
      ];
}

class MixShippingVehicleCharges extends Equatable {
  final String name;
  final double value;

  const MixShippingVehicleCharges({
    required this.name,
    required this.value,
  });

  @override
  List<Object?> get props => [
        name,
        value,
      ];
}
