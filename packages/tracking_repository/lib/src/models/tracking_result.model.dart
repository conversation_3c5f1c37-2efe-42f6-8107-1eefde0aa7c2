import 'package:tracking_repository/tracking_repository.dart';

class TrackingResult {
  TrackingItemType trackingItemType;
  List<TrackingItem> trackingItems;
  TrackingItemVehicle? trackingItemVehicle;
  TrackingItemContainer? trackingItemContainer;

  TrackingResult({
    required this.trackingItemType,
    required this.trackingItems,
    this.trackingItemVehicle,
    this.trackingItemContainer,
  });
}
