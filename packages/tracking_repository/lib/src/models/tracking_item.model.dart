import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:tracking_repository/src/models/models.dart';

class TrackingItem extends Equatable {
  // info
  final TrackingItemType trackingItemType;
  final TrackingItemStatus trackingItemStatus;
  final int? vehicleId;
  final int? containerId;
  final DateTime? createdAt;
  final TrackingItemVehicle? vehicle;
  final TrackingItemContainer? container;
  final bool isReached;

  const TrackingItem({
    // info
    required this.trackingItemType,
    required this.trackingItemStatus,
    this.createdAt,
    required this.vehicleId,
    required this.containerId,
    this.vehicle,
    this.container,
    this.isReached = true,
  });

  TrackingItem copyWith({
    DateTime? createdAt,
    TrackingItemType? trackingItemType,
    TrackingItemStatus? trackingItemStatus,
    int? vehicleId,
    int? containerId,
    TrackingItemVehicle? vehicle,
    TrackingItemContainer? container,
    bool? isReached,
  }) {
    return TrackingItem(
      createdAt: createdAt ?? this.createdAt,
      trackingItemType: trackingItemType ?? this.trackingItemType,
      trackingItemStatus: trackingItemStatus ?? this.trackingItemStatus,
      vehicleId: vehicleId ?? this.vehicleId,
      containerId: containerId ?? this.containerId,
      vehicle: vehicle ?? this.vehicle,
      container: container ?? this.container,
      isReached: isReached ?? this.isReached,
    );
  }

  factory TrackingItem.fromMap(Map<String, dynamic> map) {
    return TrackingItem(
      trackingItemType: map['vehicle_id'] != null ? TrackingItemType.vehicle : TrackingItemType.shipment,
      trackingItemStatus: TrackingItemStatus.fromJson(map['status']),
      createdAt: map['created_at'] != null ? DateTime.parse(map['created_at']).toUtc() : null,
      vehicleId: map['vehicle_id'],
      containerId: map['container_id'],
      vehicle: map['vehicles'] != null ? TrackingItemVehicle.fromMap(map['vehicles']) : null,
      container: map['containers'] != null
          ? TrackingItemContainer.fromMap(map['containers'])
          : map['vehicles']['containers'] != null
              ? TrackingItemContainer.fromMap(map['vehicles']['containers'])
              : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {};
  }

  factory TrackingItem.fromJson(String json) => TrackingItem.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        trackingItemStatus,
        trackingItemType,
        createdAt,
        vehicleId,
        containerId,
        vehicle,
      ];
}

enum TrackingItemType {
  vehicle,
  shipment,
  none;

  static TrackingItemType fromJson(String json) {
    switch (json) {
      case 'vehicle':
        return TrackingItemType.vehicle;
      case 'container':
        return TrackingItemType.shipment;
      default:
        return TrackingItemType.none;
    }
  }
}

enum TrackingItemStatus {
  auctionUnpaid, // auction_unpaid
  auctionPaid, // auction_paid
  onTheWay, // on_the_way
  onHandNoTitle, //on_hand_no_title
  onHandWithTitle, //on_hand_with_title
  onHandWithLoad, //on_hand_with_load
  shipped, // shipped
  atLoading, //at_loading
  atTheDock, //at_the_dock
  arrived; //arrived

  String toJson() {
    switch (name) {
      case 'auctionPaid':
        return 'auction_paid';
      case 'auctionUnpaid':
        return 'auction_unpaid';
      case 'onTheWay':
        return 'on_the_way';
      case 'onHandNoTitle':
        return 'on_hand_no_title';
      case 'onHandWithTitle':
        return 'on_hand_with_title';
      case 'onHandWithLoad':
        return 'on_hand_with_load';
      case 'shipped':
        return 'shipped';
      case 'atLoading':
        return 'at_loading';
      case 'atTheDock':
        return 'at_the_dock';
      case 'arrived':
        return 'arrived';
      default:
        return 'arrived';
    }
  }

  static TrackingItemStatus fromJson(String json) {
    switch (json) {
      case 'auction_paid':
        return TrackingItemStatus.auctionPaid;
      case 'auction_unpaid':
        return TrackingItemStatus.auctionUnpaid;
      case 'on_the_way':
      case 'checked':
      case 'final_checked':
        return TrackingItemStatus.onTheWay;
      case 'at_the_dock':
        return TrackingItemStatus.atTheDock;
      case 'on_hand_no_title':
        return TrackingItemStatus.onHandNoTitle;
      case 'on_hand_with_title':
        return TrackingItemStatus.onHandWithTitle;
      case 'on_hand_with_load':
        return TrackingItemStatus.onHandWithLoad;
      case 'shipped':
        return TrackingItemStatus.shipped;
      case 'at_loading':
        return TrackingItemStatus.atLoading;
      case 'arrived':
        return TrackingItemStatus.arrived;
      default:
        return TrackingItemStatus.shipped;
    }
  }
}
