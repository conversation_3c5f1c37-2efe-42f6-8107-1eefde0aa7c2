import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:tracking_repository/src/models/models.dart';

class TrackingItemVehicle extends Equatable {
  // info
  final String vin;
  final String? lotNumber;
  final String? photoLink;
  final String? coverPhoto;
  final String polLocation;
  final TrackingItemContainer? container;

  const TrackingItemVehicle({
    // info
    required this.vin,
    this.lotNumber,
    this.photoLink,
    this.coverPhoto,
    required this.polLocation,
    this.container,
  });

  factory TrackingItemVehicle.fromMap(Map<String, dynamic> map) {
    return TrackingItemVehicle(
      vin: map['vin'] ?? '',
      lotNumber: map['lot_number'],
      photoLink: map['photo_link'],
      coverPhoto: map['cover_photo'],
      polLocation: map['pol_locations']['name'],
      container: map['container'] != null ? TrackingItemContainer.fromMap(map['container']) : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {};
  }

  factory TrackingItemVehicle.fromJson(String json) => TrackingItemVehicle.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        vin,
        lotNumber,
        photoLink,
        coverPhoto,
        polLocation,
        container,
      ];
}
