import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:tracking_repository/src/models/models.dart';

class TrackingItemContainer extends Equatable {
  // info
  final String containerNumber;
  final String? photoLink;
  final String? coverPhoto;
  final TrackingItemBooking booking;
  final List<TrackingItemVehicle> vehicles;

  const TrackingItemContainer({
    // info
    required this.containerNumber,
    this.photoLink,
    this.coverPhoto,
    required this.booking,
    required this.vehicles,
  });

  factory TrackingItemContainer.fromMap(Map<String, dynamic> map) {
    return TrackingItemContainer(
      containerNumber: map['container_number'] ?? '',
      photoLink: map['photo_link'],
      coverPhoto: map['cover_photo'],
      booking: TrackingItemBooking.fromMap(map['bookings']),
      vehicles: map['vehicles'] != null
          ? map['vehicles'].map<TrackingItemVehicle>((item) => TrackingItemVehicle.fromMap(item)).toList()
          : [],
    );
  }

  Map<String, dynamic> toMap() {
    return {};
  }

  factory TrackingItemContainer.fromJson(String json) => TrackingItemContainer.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        containerNumber,
        photoLink,
        coverPhoto,
      ];
}
