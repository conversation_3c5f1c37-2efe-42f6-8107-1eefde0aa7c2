import 'dart:convert';

import 'package:equatable/equatable.dart';

class TrackingItemBooking extends Equatable {
  // info
  final String bookingNumber;
  final String destination;
  final DateTime eta;
  final DateTime etd;

  const TrackingItemBooking({
    // info
    required this.bookingNumber,
    required this.destination,
    required this.eta,
    required this.etd,
  });

  factory TrackingItemBooking.fromMap(Map<String, dynamic> map) {
    return TrackingItemBooking(
      bookingNumber: map['booking_number'] ?? '',
      destination: map['destinations']['name'] ?? '',
      eta: DateTime.parse(map['eta']).toUtc(),
      etd: DateTime.parse(map['vessels']['etd']).toUtc(),
    );
  }

  Map<String, dynamic> toMap() {
    return {};
  }

  factory TrackingItemBooking.fromJson(String json) => TrackingItemBooking.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        bookingNumber,
        destination,
        eta,
        eta,
      ];
}
