import 'dart:async';

import 'package:dio_client/dio_client.dart';
import 'package:tracking_repository/tracking_repository.dart';

class TrackingRepository {
  final DioClient dioClient;

  final String _trackingUrl = "/api/public/tracking";

  TrackingRepository({
    required this.dioClient,
  });

  Future<TrackingResult> getTrackingInfo({required String trackingValue}) async {
    try {
      final Map<String, dynamic> response = await dioClient.get(
        _trackingUrl,
        queryParameters: {'tracking_value': trackingValue},
      );
      TrackingItemContainer? container;
      TrackingItemVehicle? vehicle;
      List<TrackingItem> remainingItems = [];
      List<TrackingItem> trackingItems = response['data']
          .where((item) => !['pending', 'checked', 'final_checked'].contains(item['status']))
          .map<TrackingItem>(
        (item) {
          TrackingItem trItem = TrackingItem.fromMap(item);
          if (trItem.container != null) {
            container = trItem.container;
          }
          if (trItem.vehicle != null) {
            vehicle = trItem.vehicle;
          }
          return trItem;
        },
      ).toList();

      // Sort trackingItems based on the order in getDefaultOptions
      if (trackingItems.isNotEmpty) {
        List<TrackingItem> defaultOptions = getDefaultOptions(container, vehicle);
        trackingItems.sort((a, b) {
          int indexA = _findIndexInDefaultOptions(a, defaultOptions);
          int indexB = _findIndexInDefaultOptions(b, defaultOptions);
          return indexA.compareTo(indexB);
        });
      }

      if (trackingItems.isNotEmpty) {
        remainingItems = getRemainingOptions(container, vehicle, trackingItems.last);
      }

      return TrackingResult(
        trackingItems: [...trackingItems, ...remainingItems],
        trackingItemType: response['type'] != null
            ? TrackingItemType.fromJson(
                response['type'],
              )
            : TrackingItemType.none,
        trackingItemVehicle: vehicle,
        trackingItemContainer: container,
      );
    } catch (e) {
      return TrackingResult(trackingItemType: TrackingItemType.none, trackingItems: []);
    }
  }

  // Helper method to find the index of a tracking item in the default options list
  int _findIndexInDefaultOptions(TrackingItem item, List<TrackingItem> defaultOptions) {
    int index = defaultOptions.indexWhere(
      (defaultItem) =>
          defaultItem.trackingItemStatus == item.trackingItemStatus &&
          defaultItem.trackingItemType == item.trackingItemType,
    );
    return index != -1 ? index : defaultOptions.length; // If not found, put at the end
  }

  List<TrackingItem> getRemainingOptions(
    TrackingItemContainer? container,
    TrackingItemVehicle? vehicle,
    TrackingItem lastItem,
  ) {
    List<TrackingItem> defaultOptions = getDefaultOptions(container, vehicle);
    int lastIndex = defaultOptions.indexWhere(
      (item) =>
          item.trackingItemStatus == lastItem.trackingItemStatus && item.trackingItemType == lastItem.trackingItemType,
    );
    return defaultOptions.sublist(lastIndex + 1);
  }

  List<TrackingItem> getDefaultOptions(
    TrackingItemContainer? container,
    TrackingItemVehicle? vehicle,
  ) {
    return [
      TrackingItem(
        trackingItemType: TrackingItemType.vehicle,
        trackingItemStatus: TrackingItemStatus.auctionUnpaid,
        vehicleId: -1,
        containerId: -1,
        isReached: false,
        container: container,
        vehicle: vehicle,
      ),
      TrackingItem(
        trackingItemType: TrackingItemType.vehicle,
        trackingItemStatus: TrackingItemStatus.auctionPaid,
        vehicleId: -1,
        containerId: -1,
        isReached: false,
        container: container,
        vehicle: vehicle,
      ),
      TrackingItem(
        trackingItemType: TrackingItemType.vehicle,
        trackingItemStatus: TrackingItemStatus.onTheWay,
        vehicleId: -1,
        containerId: -1,
        isReached: false,
        container: container,
        vehicle: vehicle,
      ),
      TrackingItem(
        trackingItemType: TrackingItemType.vehicle,
        trackingItemStatus: TrackingItemStatus.onHandNoTitle,
        vehicleId: -1,
        containerId: -1,
        isReached: false,
        container: container,
        vehicle: vehicle,
      ),
      TrackingItem(
        trackingItemType: TrackingItemType.vehicle,
        trackingItemStatus: TrackingItemStatus.onHandWithTitle,
        vehicleId: -1,
        containerId: -1,
        isReached: false,
        container: container,
        vehicle: vehicle,
      ),
      TrackingItem(
        trackingItemType: TrackingItemType.vehicle,
        trackingItemStatus: TrackingItemStatus.onHandWithLoad,
        vehicleId: -1,
        containerId: -1,
        isReached: false,
        container: container,
        vehicle: vehicle,
      ),
      TrackingItem(
        trackingItemType: TrackingItemType.vehicle,
        trackingItemStatus: TrackingItemStatus.shipped,
        vehicleId: -1,
        containerId: -1,
        isReached: false,
        container: container,
        vehicle: vehicle,
      ),
      TrackingItem(
        trackingItemType: TrackingItemType.shipment,
        trackingItemStatus: TrackingItemStatus.atLoading,
        vehicleId: -1,
        containerId: -1,
        isReached: false,
        container: container,
        vehicle: vehicle,
      ),
      TrackingItem(
        trackingItemType: TrackingItemType.shipment,
        trackingItemStatus: TrackingItemStatus.atTheDock,
        vehicleId: -1,
        containerId: -1,
        isReached: false,
        container: container,
        vehicle: vehicle,
      ),
      TrackingItem(
        trackingItemType: TrackingItemType.shipment,
        trackingItemStatus: TrackingItemStatus.onTheWay,
        vehicleId: -1,
        containerId: -1,
        isReached: false,
        container: container,
        vehicle: vehicle,
      ),
      TrackingItem(
        trackingItemType: TrackingItemType.shipment,
        trackingItemStatus: TrackingItemStatus.arrived,
        vehicleId: -1,
        containerId: -1,
        isReached: false,
        container: container,
        vehicle: vehicle,
      ),
    ];
  }
}
