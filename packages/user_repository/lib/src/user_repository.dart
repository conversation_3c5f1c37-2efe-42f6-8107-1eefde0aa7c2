import 'dart:async';

import 'package:dio_client/dio_client.dart';
import 'package:user_repository/src/models/models.dart';

class UserRepository {
  final DioClient dioClient;
  final String _profileUrl = "/api/customer/auth/profile";
  final String _fCMUrl = "/api/customer/fcm-tokens";

  UserRepository({
    required this.dioClient,
  });

  Future<User?> getUser() async {
    try {
      final Map<String, dynamic> response = await dioClient.get(
        _profileUrl,
      );
      return User.fromMap(response['data']);
    } catch (e) {
      return null;
    }
  }

  Future<void> setFCMToken(String token) async {
    try {
      await dioClient.post(_fCMUrl, data: {
        'token': token,
      });
    } catch (e) {
      //
    }
  }

  Future<bool> deleteFCMToken(String token) async {
    try {
      await dioClient.delete(_fCMUrl, queryParameters: {
        'token': token,
      });
      return true;
    } catch (e) {
      return false;
    }
  }
}
