import 'dart:convert';

import 'package:equatable/equatable.dart';

class Company extends Equatable {
  const Company({
    required this.id,
    required this.name,
    required this.complete,
    required this.completeHalfcut,
    required this.isSpecialRate,
    required this.mix,
    required this.mixHalfcut,
    required this.showShippingRate,
    this.destinationID,
  });

  final int id;
  final String name;
  final bool complete;
  final bool completeHalfcut;
  final bool isSpecialRate;
  final bool mix;
  final bool mixHalfcut;
  final bool showShippingRate;
  final int? destinationID;

  Company copyWith({
    int? id,
    String? name,
    bool? complete,
    bool? completeHalfcut,
    bool? isSpecialRate,
    bool? mix,
    bool? mixHalfcut,
    bool? showShippingRate,
    int? destinationID,
  }) {
    return Company(
      id: id ?? this.id,
      name: name ?? this.name,
      complete: complete ?? this.complete,
      completeHalfcut: completeHalfcut ?? this.completeHalfcut,
      isSpecialRate: isSpecialRate ?? this.isSpecialRate,
      mix: mix ?? this.mix,
      mixHalfcut: mixHalfcut ?? this.mixHalfcut,
      showShippingRate: showShippingRate ?? this.showShippingRate,
      destinationID: destinationID ?? this.destinationID,
    );
  }

  factory Company.fromMap(Map<String, dynamic> map) {
    return Company(
      id: map['id'],
      name: map['name'],
      complete: map['complete'],
      completeHalfcut: map['complete_halfcut'],
      isSpecialRate: map['is_special_rate'],
      mix: map['mix'],
      mixHalfcut: map['mix_halfcut'],
      showShippingRate: map['show_shipping_rate'],
      destinationID: map['destination_id'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'complete': complete,
      'complete_halfcut': completeHalfcut,
      'is_special_rate': isSpecialRate,
      'mix': mix,
      'mix_halfcut': mixHalfcut,
      'show_shipping_rate': showShippingRate,
      'destination_id': destinationID,
    };
  }

  factory Company.fromJson(String json) => Company.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        id,
        name,
        complete,
        completeHalfcut,
        isSpecialRate,
        mix,
        mixHalfcut,
        showShippingRate,
        destinationID,
      ];
}
