import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:user_repository/src/models/models.dart';

class User extends Equatable {
  const User({
    required this.id,
    required this.fullname,
    required this.company,
    required this.address,
    required this.phone,
    required this.secondaryPhone,
    required this.email,
    required this.username,
    required this.secondaryEmail,
    required this.gender,
    required this.photo,
    required this.totalAnnouncements,
    required this.mixShipping,
    required this.showShippingRate,
    required this.hasShippingRate,
    required this.hasStatements,
    required this.towingRates,
    this.destinationID,
    this.companyObject,
  });

  final int id;
  final String fullname;
  final String company;
  final String? address;
  final String? phone;
  final String? secondaryPhone;
  final String email;
  final String username;
  final String? secondaryEmail;
  final Gender gender;
  final String? photo;
  final int totalAnnouncements;
  final bool mixShipping;
  final bool showShippingRate;
  final bool hasShippingRate;
  final bool hasStatements;
  final bool towingRates;
  final int? destinationID;
  final Company? companyObject;

  User copyWith({
    int? id,
    String? fullname,
    String? company,
    String? address,
    String? phone,
    String? secondaryPhone,
    String? email,
    String? username,
    String? secondaryEmail,
    Gender? gender,
    String? photo,
    int? totalAnnouncements,
    bool? mixShipping,
    bool? showShippingRate,
    bool? hasShippingRate,
    bool? hasStatements,
    bool? towingRates,
    int? destinationID,
    Company? companyObject,
  }) {
    return User(
      id: id ?? this.id,
      fullname: fullname ?? this.fullname,
      company: company ?? this.company,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      secondaryPhone: secondaryPhone ?? this.secondaryPhone,
      email: email ?? this.email,
      username: username ?? this.username,
      secondaryEmail: secondaryEmail ?? this.secondaryEmail,
      gender: gender ?? this.gender,
      photo: photo ?? this.photo,
      totalAnnouncements: totalAnnouncements ?? this.totalAnnouncements,
      mixShipping: mixShipping ?? this.mixShipping,
      showShippingRate: showShippingRate ?? this.showShippingRate,
      hasShippingRate: hasShippingRate ?? this.hasShippingRate,
      hasStatements: hasStatements ?? this.hasStatements,
      towingRates: towingRates ?? this.towingRates,
      destinationID: destinationID ?? this.destinationID,
      companyObject: companyObject ?? this.companyObject,
    );
  }

  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id'],
      fullname: map['fullname'],
      company: map['companies']['name'],
      address: map['address'],
      phone: map['phone'],
      secondaryPhone: map['secondary_phone'],
      email: map['loginable']['email'],
      username: map['loginable']['username'],
      secondaryEmail: map['secondary_email'],
      gender: map['gender'] == 'male' ? Gender.male : Gender.female,
      photo: map['photo'],
      totalAnnouncements: map['total_announcements'],
      mixShipping: map['mix_shipping'],
      showShippingRate: map['companies']['show_shipping_rate'] ?? false,
      hasShippingRate: map['has_shipping_rate'] ?? false,
      hasStatements: map['has_statement'] ?? false,
      towingRates: map['towing_rates'] ?? false,
      destinationID: map['companies']['destination_id'],
      companyObject: map['companies'] != null ? Company.fromMap(map['companies']) : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'fullname': fullname,
      'companies': companyObject?.toMap(),
      'address': address,
      'phone': phone,
      'secondary_phone': secondaryPhone,
      'loginable': <String, dynamic>{'email': email, 'username': username},
      'secondary_email': secondaryEmail,
      'gender': gender == Gender.male ? 'male' : 'female',
      'photo': photo,
      'total_announcements': totalAnnouncements,
      'mix_shipping': mixShipping,
      'has_shipping_rate': hasShippingRate,
      'has_statement': hasStatements,
      'towing_rates': towingRates,
    };
  }

  factory User.fromJson(String json) => User.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object> get props => [
        id,
        fullname,
        company,
        email,
        username,
        gender,
        totalAnnouncements,
        mixShipping,
        showShippingRate,
        hasShippingRate,
        hasStatements,
        towingRates,
      ];

  static const empty = User(
    id: -1,
    fullname: '',
    company: '',
    address: '',
    phone: '',
    secondaryPhone: '',
    email: '',
    username: '',
    secondaryEmail: null,
    gender: Gender.male,
    photo: '',
    totalAnnouncements: 0,
    mixShipping: false,
    showShippingRate: false,
    hasShippingRate: false,
    hasStatements: false,
    destinationID: -1,
    towingRates: false,
  );
}

enum Gender {
  male,
  female,
}
