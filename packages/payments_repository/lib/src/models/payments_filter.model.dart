import 'dart:convert';

import 'package:equatable/equatable.dart';

class PaymentsFilter extends Equatable {
  final int page;
  final int perPage;
  final String search;
  final PaymentTab state;

  const PaymentsFilter({
    this.page = 1,
    this.perPage = 10,
    this.search = '',
    this.state = PaymentTab.all,
  });

  factory PaymentsFilter.fromMap(Map<String, dynamic> map) {
    return PaymentsFilter(
      page: map['page'] ?? 1,
      perPage: map['per_page'] ?? 1,
      search: map['search'] ?? '',
      state: PaymentTab.fromJson(map['state'] ?? 'all'),
    );
  }

  PaymentsFilter copyWith({
    int? page,
    int? perPage,
    String? search,
    PaymentTab? state,
  }) {
    return PaymentsFilter(
      page: page ?? this.page,
      perPage: perPage ?? this.perPage,
      search: search ?? this.search,
      state: state ?? this.state,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'page': page,
      'per_page': perPage,
      'search': search,
      'state': state.toJson(),
    };
  }

  factory PaymentsFilter.fromJson(String json) => PaymentsFilter.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        page,
        perPage,
        search,
        state,
      ];
}

enum PaymentTab {
  all,
  pending,
  approved;

  String toJson() {
    switch (name) {
      case 'all':
        return 'all';
      case 'pending':
        return 'pending';
      case 'approved':
        return 'approved';
      default:
        return 'all';
    }
  }

  static PaymentTab fromJson(String json) {
    switch (json) {
      case 'all':
        return PaymentTab.all;
      case 'pending':
        return PaymentTab.pending;
      case 'reviewed':
        return PaymentTab.approved;
      default:
        return PaymentTab.pending;
    }
  }
}
