import 'package:equatable/equatable.dart';
import 'package:payments_repository/src/models/models.dart';

class PaymentReceived<T> extends Equatable {
  // info
  final int id;
  final PaymentReceivedType type;
  final String invoiceNumber;
  final String? vin;
  final String? lotNumber;
  final double amountApplied;
  final String? paymentRemark;
  final double? exchangeRate;
  final PaymentReceivedState state;
  final List<PaymentAlloction> paymentAllocations;

  final DateTime? paymentDate;

  final T parent;

  const PaymentReceived({
    // info
    required this.id,
    required this.type,
    required this.invoiceNumber,
    this.vin,
    this.lotNumber,
    required this.amountApplied,
    this.paymentRemark,
    this.exchangeRate,
    required this.state,
    this.paymentDate,
    this.paymentAllocations = const <PaymentAlloction>[],
    required this.parent,
  });

  factory PaymentReceived.fromMap(Map<String, dynamic> map, {required T passedParent}) {
    return PaymentReceived(
      id: map["id"],
      type: PaymentReceivedType.fromJson(map["type"]),
      invoiceNumber: map["invoiceNumber"] ?? '',
      vin: map["vin"],
      lotNumber: map["lot_number"],
      amountApplied: double.parse(map["amount_applied"]),
      paymentRemark: map["payment_remark"],
      exchangeRate: (map["exchange_rate"] as num?)?.toDouble() ?? 1.0,
      state: PaymentReceivedState.fromJson(map["state"]),
      paymentDate: map['payment_date'] != null ? DateTime.parse(map['payment_date']) : null,
      paymentAllocations: map['payment_allocations'] != null
          ? map['payment_allocations'].map<PaymentAlloction>((item) => PaymentAlloction.fromMap(item)).toList() ??
              const <PaymentAlloction>[]
          : const <PaymentAlloction>[],
      parent: passedParent,
    );
  }

  Map<String, dynamic> toMap() {
    return {};
  }

  @override
  List<Object?> get props => [
        id,
      ];
}

enum PaymentReceivedType {
  shipment, //shipment
  mix, //mix
  clearance, //clearance
  clearLog, //clear_log
  singleVcc, //single_vcc
  exitClaimCharge, //exit_claim_charge
  deliveryCharge, //delivery_charge
  detentionCharge,
  auction; //detention_charge

  String toJson() {
    switch (name) {
      case 'shipment':
        return 'shipment';
      case 'mix':
        return 'mix';
      case 'clearance':
        return 'clearance';
      case 'clearLog':
        return 'clear_log';
      case 'singleVcc':
        return 'single_vcc';
      case 'exitClaimCharge':
        return 'exit_claim_charge';
      case 'deliveryCharge':
        return 'delivery_charge';
      case 'detentionCharge':
        return 'detention_charge';
      case 'auction':
        return 'auction';
      default:
        return 'shipment';
    }
  }

  static PaymentReceivedType fromJson(String json) {
    switch (json) {
      case 'shipment':
        return PaymentReceivedType.shipment;
      case 'mix':
        return PaymentReceivedType.mix;
      case 'clearance':
        return PaymentReceivedType.clearance;
      case 'clear_log':
        return PaymentReceivedType.clearLog;
      case 'single_vcc':
        return PaymentReceivedType.singleVcc;
      case 'exit_claim_charge':
        return PaymentReceivedType.exitClaimCharge;
      case 'delivery_charge':
        return PaymentReceivedType.deliveryCharge;
      case 'detention_charge':
        return PaymentReceivedType.detentionCharge;
      case 'auction':
        return PaymentReceivedType.auction;
      default:
        return PaymentReceivedType.shipment;
    }
  }
}

enum PaymentReceivedState {
  pending, //pending
  approved; //approved

  String toJson() {
    switch (name) {
      case 'pending':
        return 'pending';
      case 'approved':
        return 'approved';
      default:
        return 'pending';
    }
  }

  static PaymentReceivedState fromJson(String json) {
    switch (json) {
      case 'pending':
        return PaymentReceivedState.pending;
      case 'approved':
        return PaymentReceivedState.approved;
      default:
        return PaymentReceivedState.pending;
    }
  }
}
