import 'package:equatable/equatable.dart';

class PaymentAlloction extends Equatable {
  // info
  final int id;
  final PaymentAllocationType type;
  final double amount;

  const PaymentAlloction({
    // info
    required this.id,
    required this.type,
    required this.amount,
  });

  factory PaymentAlloction.fromMap(Map<String, dynamic> map) {
    return PaymentAlloction(
      id: map["id"],
      type: PaymentAllocationType.fromJson(map["type"]),
      amount: double.parse(map["amount"]),
    );
  }

  Map<String, dynamic> toMap() {
    return {};
  }

  @override
  List<Object?> get props => [
        id,
      ];
}

enum PaymentAllocationType {
  towing, //towing
  shipping, //shipping
  title, //title
  storageClr, //storage_clr
  storageDo, //storage_do
  charge, //charge
  other, //other
  storage, //storage
  dismantle, //dismantle
  clearance, //clearance
  vatCustom, //vat_custom
  freight, //freight
  attestationFee, //attestation_fee
  inspectionCharges, //inspection_charges
  auctionStorage, //auction_storage
  sharjahYardStorage, //sharjah_yard_storage
  fedExOrMailingFee, //fed_ex_or_mailing_fee
  recoveryFee, //recovery_fee
  customHold, //custom_hold
  relistFee, //relist_fee
  detentionCharges, //detention_charges
  shortage, //shortage
  suvCharges, //suv_charges
  tdsCharges, //tds_charges
  registrationFee, //registration_fee
  transportationFee, //transportation_fee
  officeFeeAndBank, //office_fee_and_bank
  emptyContainers, //empty_containers
  previousPayment, //previous_payment
  vehiclePrice; //vehicle_price

  String toJson() {
    switch (name) {
      case 'towing':
        return 'towing';
      case 'shipping':
        return 'shipping';
      case 'title':
        return 'title';
      case 'storageClr':
        return 'storage_clr';
      case 'storageDo':
        return 'storage_do';
      case 'charge':
        return 'charge';
      case 'other':
        return 'other';
      case 'storage':
        return 'storage';
      case 'dismantle':
        return 'dismantle';
      case 'clearance':
        return 'clearance';
      case 'vatCustom':
        return 'vat_custom';
      case 'freight':
        return 'freight';
      case 'attestationFee':
        return 'attestation_fee';
      case 'inspectionCharges':
        return 'inspection_charges';
      case 'auctionStorage':
        return 'auction_storage';
      case 'sharjahYardStorage':
        return 'sharjah_yard_storage';
      case 'fedExOrMailingFee':
        return 'fed_ex_or_mailing_fee';
      case 'recoveryFee':
        return 'recovery_fee';
      case 'customHold':
        return 'custom_hold';
      case 'relistFee':
        return 'relist_fee';
      case 'detentionCharges':
        return 'detention_charges';
      case 'shortage':
        return 'shortage';
      case 'suvCharges':
        return 'suv_charges';
      case 'tdsCharges':
        return 'tds_charges';
      case 'registrationFee':
        return 'registration_fee';
      case 'transportationFee':
        return 'transportation_fee';
      case 'officeFeeAndBank':
        return 'office_fee_and_bank';
      case 'emptyContainers':
        return 'empty_containers';
      case 'previousPayment':
        return 'previous_payment';
      case 'vehiclePrice':
        return 'vehicle_price';
      default:
        return 'towing';
    }
  }

  static PaymentAllocationType fromJson(String json) {
    switch (json) {
      case 'towing':
        return PaymentAllocationType.towing;
      case 'shipping':
        return PaymentAllocationType.shipping;
      case 'title':
        return PaymentAllocationType.title;
      case 'storage_clr':
        return PaymentAllocationType.storageClr;
      case 'storage_do':
        return PaymentAllocationType.storageDo;
      case 'charge':
        return PaymentAllocationType.charge;
      case 'other':
        return PaymentAllocationType.other;
      case 'storage':
        return PaymentAllocationType.storage;
      case 'dismantle':
        return PaymentAllocationType.dismantle;
      case 'clearance':
        return PaymentAllocationType.clearance;
      case 'vat_custom':
        return PaymentAllocationType.vatCustom;
      case 'freight':
        return PaymentAllocationType.freight;
      case 'attestation_fee':
        return PaymentAllocationType.attestationFee;
      case 'inspection_charges':
        return PaymentAllocationType.inspectionCharges;
      case 'auction_storage':
        return PaymentAllocationType.auctionStorage;
      case 'sharjah_yard_storage':
        return PaymentAllocationType.sharjahYardStorage;
      case 'fed_ex_or_mailing_fee':
        return PaymentAllocationType.fedExOrMailingFee;
      case 'recovery_fee':
        return PaymentAllocationType.recoveryFee;
      case 'custom_hold':
        return PaymentAllocationType.customHold;
      case 'relist_fee':
        return PaymentAllocationType.relistFee;
      case 'detention_charges':
        return PaymentAllocationType.detentionCharges;
      case 'shortage':
        return PaymentAllocationType.shortage;
      case 'suv_charges':
        return PaymentAllocationType.suvCharges;
      case 'tds_charges':
        return PaymentAllocationType.tdsCharges;
      case 'registration_fee':
        return PaymentAllocationType.registrationFee;
      case 'transportation_fee':
        return PaymentAllocationType.transportationFee;
      case 'office_fee_and_bank':
        return PaymentAllocationType.officeFeeAndBank;
      case 'empty_containers':
        return PaymentAllocationType.emptyContainers;
      case 'previous_payment':
        return PaymentAllocationType.previousPayment;
      case 'vehicle_price':
        return PaymentAllocationType.vehiclePrice;
      default:
        return PaymentAllocationType.towing;
    }
  }
}
