import 'package:equatable/equatable.dart';
import 'package:payments_repository/src/models/models.dart';

class PaymentModel extends Equatable {
  // info
  final int id;
  final PaymentState state;
  final double amount;
  final double amountApplied;
  final double exchangeRate;
  final String currency;
  final PaymentMethod paymentMethod;
  final String transactionNumber;
  final String link;
  final String remark;
  final List<PayemntAttachment> paymentAttachments;

  final List<PaymentReceived<PaymentModel>> payments;

  // dates
  final DateTime createdAt;
  final DateTime paymentDate;

  const PaymentModel({
    // info
    required this.id,
    required this.state,
    required this.amount,
    required this.amountApplied,
    required this.exchangeRate,
    required this.currency,
    required this.paymentMethod,
    required this.transactionNumber,
    required this.link,
    required this.remark,
    required this.paymentAttachments,
    required this.payments,
    // dates
    required this.createdAt,
    required this.paymentDate,
  });

  factory PaymentModel.fromMap(Map<String, dynamic> map) {
    PaymentModel payment = PaymentModel(
      id: map["id"],
      state: PaymentState.fromJson(map['state']),
      amount: map['amount'] != null ? double.parse(map["amount"]) : 0,
      amountApplied: map['amount_applied'] != null ? double.parse(map["amount_applied"]) : 0,
      exchangeRate: (map["exchange_rate"] as num?)?.toDouble() ?? 1.0,
      currency: map['currency'] ?? '',
      paymentMethod: PaymentMethod.fromJson(map['payment_method']),
      link: map['link'] ?? '',
      transactionNumber: map['transaction_number'] ?? '',
      remark: map['remark'] ?? '',
      paymentAttachments: map['attachments'].map<PayemntAttachment>((item) => PayemntAttachment.fromMap(item)).toList(),
      createdAt: DateTime.parse(map['created_at']),
      paymentDate: DateTime.parse(map['payment_date']),
      payments: [],
    );
    List<PaymentReceived<PaymentModel>> payments = map['payments'] != null
        ? map['payments']
            .map<PaymentReceived<PaymentModel>>(
              (item) => PaymentReceived<PaymentModel>.fromMap(
                item,
                passedParent: payment,
              ),
            )
            .toList()
        : [];
    payment.payments.addAll(payments);

    return payment;
  }

  Map<String, dynamic> toMap() {
    return {};
  }

  PaymentModel copyWith({
    int? id,
    DateTime? createdAt,
    DateTime? paymentDate,
    PaymentState? state,
    double? amount,
    double? amountApplied,
    double? exchangeRate,
    String? currency,
    PaymentMethod? paymentMethod,
    String? transactionNumber,
    String? link,
    String? remark,
    List<PayemntAttachment>? paymentAttachments,
    List<PaymentReceived<PaymentModel>>? payments,
  }) {
    return PaymentModel(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      paymentDate: paymentDate ?? this.paymentDate,
      state: state ?? this.state,
      amount: amount ?? this.amount,
      amountApplied: amountApplied ?? this.amountApplied,
      exchangeRate: exchangeRate ?? this.exchangeRate,
      currency: currency ?? this.currency,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      transactionNumber: transactionNumber ?? this.transactionNumber,
      link: link ?? this.link,
      remark: remark ?? this.remark,
      paymentAttachments: paymentAttachments ?? this.paymentAttachments,
      payments: payments ?? this.payments,
    );
  }

  @override
  List<Object?> get props => [
        id,
      ];
}

enum PaymentState {
  pending,
  approved;

  String toJson() {
    switch (name) {
      case 'pending':
        return 'pending';
      case 'approved':
        return 'approved';
      default:
        return 'pending';
    }
  }

  static PaymentState fromJson(String json) {
    switch (json) {
      case 'pending':
        return PaymentState.pending;
      case 'reviewed':
        return PaymentState.approved;
      default:
        return PaymentState.pending;
    }
  }
}

enum PaymentMethod {
  cash,
  wire,
  check,
  mukhasa,
  salesTax,
  damageCredit,
  demurrageCredit,
  storageCredit,
  exitPaperCredit;

  String toJson() {
    switch (name) {
      case 'cash':
        return 'cash';
      case 'wire':
        return 'wire';
      case 'check':
        return 'check';
      case 'mukhasa':
        return 'mukhasa';
      case 'salesTax':
        return 'sales_tax';
      case 'damageCredit':
        return 'damage_credit';
      case 'demurrageCredit':
        return 'demurrage_credit';
      case 'storage_credit':
        return 'storage_credit';
      case 'exitPaperCredit':
        return 'exit_paper_credit';
      default:
        return 'cash';
    }
  }

  static PaymentMethod fromJson(String json) {
    switch (json) {
      case 'cash':
        return PaymentMethod.cash;
      case 'wire':
        return PaymentMethod.wire;
      case 'check':
        return PaymentMethod.check;
      case 'mukhasa':
        return PaymentMethod.mukhasa;
      case 'sales_tax':
        return PaymentMethod.salesTax;
      case 'damage_credit':
        return PaymentMethod.damageCredit;
      case 'demurrage_credit':
        return PaymentMethod.demurrageCredit;
      case 'storage_credit':
        return PaymentMethod.storageCredit;
      case 'exit_paper_credit':
        return PaymentMethod.exitPaperCredit;
      default:
        return PaymentMethod.cash;
    }
  }
}
