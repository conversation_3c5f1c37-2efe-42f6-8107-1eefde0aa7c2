import 'dart:convert';

import 'package:equatable/equatable.dart';

class PayemntAttachment extends Equatable {
  final int id;
  final String name;
  final String url;

  const PayemntAttachment({
    required this.id,
    required this.name,
    required this.url,
  });

  factory PayemntAttachment.fromMap(Map<String, dynamic> map) {
    return PayemntAttachment(
      id: map['id'],
      name: map['name'],
      url: map['url'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'url': url,
    };
  }

  factory PayemntAttachment.fromJson(String json) => PayemntAttachment.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [id, name, url];
}
