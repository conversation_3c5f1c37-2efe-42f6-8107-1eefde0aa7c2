import 'dart:async';

import 'package:dio_client/dio_client.dart';
import 'package:payments_repository/src/models/models.dart';

class PaymentsRepository {
  final String _url = '/api/customer/customer-payment-transaction';
  final DioClient dioClient;

  PaymentsRepository({
    required this.dioClient,
  });

  Future<List<PaymentModel>> getPayments(PaymentsFilter transactionsFilter) async {
    try {
      final response = await dioClient.get(
        _url,
        queryParameters: transactionsFilter.toMap(),
      );
      return response['data'].map<PaymentModel>((item) => PaymentModel.fromMap(item)).toList();
    } catch (e) {
      throw Exception('error fetching Payments');
    }
  }

  Future<PaymentModel> getPayment(PaymentModel payment) async {
    try {
      final response = await dioClient.get(
        '$_url/${payment.id}',
      );
      List<PaymentReceived<PaymentModel>> paymentReceiveds = response['data']['payments']
          .map<PaymentReceived<PaymentModel>>(
              (item) => PaymentReceived<PaymentModel>.fromMap(item, passedParent: payment))
          .toList();
      payment.payments.addAll(paymentReceiveds);
      return payment;
    } catch (e) {
      throw Exception('error fetching Payments');
    }
  }
}
