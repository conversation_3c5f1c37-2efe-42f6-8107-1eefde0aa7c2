import 'dart:async';
import 'dart:convert';

import 'package:dio_client/dio_client.dart';
import 'package:notifications_repository/src/models/models.dart';

class NotificationsRepository {
  final String _url = '/api/customer/notifications';
  final DioClient dioClient;

  NotificationsRepository({
    required this.dioClient,
  });

  Future<Map<String, dynamic>> getNotifications(NotificationsFilter notificationsFilter) async {
    try {
      final response = await dioClient.get(
        _url,
        queryParameters: notificationsFilter.toMap(),
      );
      return {
        'items': response['data'].map<NotificationModel>((item) => NotificationModel.fromMap(item)).toList(),
        'announcement_unread': response['unreadTotal']['announcement'] ?? 0,
        'arrival_notice_unread': response['unreadTotal']['arrival_notice'] ?? 0,
        'shipping_rate_unread': response['unreadTotal']['shipping_rate'] ?? 0,
        'mix_shipping_rate_unread': response['unreadTotal']['mix_shipping_rate'] ?? 0,
        'transaction_unread': response['unreadTotal']['transaction'] ?? 0,
      };
    } catch (e) {
      throw Exception('error fetching Notification');
    }
  }

  Future<bool> markOneAsRead(NotificationModel notificationModel) async {
    try {
      await dioClient.patch('$_url/${notificationModel.id}');
      return true;
    } catch (e) {
      throw Exception('error marking read Notification');
    }
  }

  Future<bool> markAllAsRead(List<NotificationType> notificationTypes) async {
    try {
      await dioClient.patch(_url, data: {
        'notification_types': jsonEncode(notificationTypes.map((e) => e.toJson()).toList()),
      });
      return true;
    } catch (e) {
      throw Exception('error marking read Notification');
    }
  }
}
