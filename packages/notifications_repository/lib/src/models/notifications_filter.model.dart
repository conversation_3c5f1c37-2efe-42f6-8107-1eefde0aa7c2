import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:notifications_repository/notifications_repository.dart';

class NotificationsFilter extends Equatable {
  final int page;
  final int perPage;
  final List<NotificationType> notificationTypes;

  const NotificationsFilter({
    this.page = 1,
    this.perPage = 10,
    this.notificationTypes = const [NotificationType.announcement],
  });

  factory NotificationsFilter.fromMap(Map<String, dynamic> map) {
    return NotificationsFilter(
      page: map['page'] ?? 1,
      perPage: map['per_page'] ?? 1,
      notificationTypes: map['notification_types'] ?? [],
    );
  }

  NotificationsFilter copyWith({
    int? page,
    int? perPage,
    List<NotificationType>? notificationTypes,
  }) {
    return NotificationsFilter(
      page: page ?? this.page,
      perPage: perPage ?? this.perPage,
      notificationTypes: notificationTypes ?? this.notificationTypes,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'page': page,
      'per_page': perPage,
      'notification_types': notificationTypes.contains(NotificationType.announcement)
          ? jsonEncode([
              NotificationType.announcement,
              NotificationType.shippingRate,
              NotificationType.mixShippingRate,
            ].map((e) => e.toJson()).toList())
          : jsonEncode(notificationTypes.map((e) => e.toJson()).toList()),
    };
  }

  factory NotificationsFilter.fromJson(String json) => NotificationsFilter.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        page,
        perPage,
        notificationTypes,
      ];
}
