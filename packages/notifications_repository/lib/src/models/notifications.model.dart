import 'package:equatable/equatable.dart';
import 'package:timeago/timeago.dart' as timeago;

class NotificationModel extends Equatable {
  // info
  final int id;
  final int receiverId;
  final String title;
  final String description;
  final NotificationType notificationType;
  final Map<String, dynamic>? data;
  // dates
  final DateTime createdAt;
  final DateTime? seenAt;

  const NotificationModel({
    // info
    required this.id,
    required this.receiverId,
    required this.title,
    required this.description,
    required this.notificationType,
    this.data,
    // dates
    required this.createdAt,
    this.seenAt,

    // relations
  });

  String getTimeAgo() {
    return timeago.format(createdAt);
  }

  factory NotificationModel.fromMap(Map<String, dynamic> map) {
    try {
      return NotificationModel(
        id: map["id"],
        receiverId: map["receiver_id"],
        title: map["title"],
        description: map["description"],
        notificationType: NotificationType.fromJson(map["notification_type"]),
        createdAt: DateTime.parse(map['created_at']),
        seenAt: map['seen_at'] != null ? DateTime.parse(map['seen_at']) : null,
        data: map['data'],
      );
    } catch (e) {
      return NotificationModel(
        id: map["id"],
        receiverId: map["receiver_id"],
        title: map["title"],
        description: map["description"],
        notificationType: NotificationType.fromJson(map["notification_type"]),
        createdAt: DateTime.parse(map['created_at']),
        seenAt: map['seen_at'] != null ? DateTime.parse(map['seen_at']) : null,
      );
    }
  }

  Map<String, dynamic> toMap() {
    return {
      "id": id,
      'receiver_id': receiverId,
      'title': title,
      'description': description,
      'notification_type': notificationType.toJson(),
      'created_at': createdAt.toIso8601String(),
      'data': data.toString(),
      'seen_at': seenAt?.toIso8601String(),
    };
  }

  NotificationModel copyWith({
    int? id,
    int? receiverId,
    String? title,
    String? description,
    NotificationType? notificationType,
    DateTime? createdAt,
    DateTime? seenAt,
    Map<String, dynamic>? data,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      receiverId: receiverId ?? this.receiverId,
      title: title ?? this.title,
      description: description ?? this.description,
      notificationType: notificationType ?? this.notificationType,
      createdAt: createdAt ?? this.createdAt,
      seenAt: seenAt ?? this.seenAt,
      data: data ?? this.data,
    );
  }

  @override
  List<Object?> get props => [
        id,
        receiverId,
        title,
        description,
        notificationType,
        createdAt,
        seenAt,
        data,
      ];
}

enum NotificationType {
  announcement,
  shippingRate,
  mixShippingRate,
  arrivalNotice,
  transaction;

  String toJson() {
    switch (name) {
      case 'announcement':
        return 'announcement';
      case 'shippingRate':
        return 'shipping_rate';
      case 'arrivalNotice':
        return 'arrival_notice';
      case 'mixShippingRate':
        return 'mix_shipping_rate';
      case 'transaction':
        return 'transaction';
      default:
        return 'announcement';
    }
  }

  static NotificationType fromJson(String json) {
    switch (json) {
      case 'announcement':
        return NotificationType.announcement;
      case 'shipping_rate':
        return NotificationType.shippingRate;
      case 'mix_shipping_rate':
        return NotificationType.mixShippingRate;
      case 'arrival_notice':
        return NotificationType.arrivalNotice;
      case 'transaction':
        return NotificationType.transaction;
      default:
        return NotificationType.announcement;
    }
  }
}
