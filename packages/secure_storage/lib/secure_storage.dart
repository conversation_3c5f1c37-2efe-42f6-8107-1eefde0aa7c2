import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SecureStorage {
  static SecureStorage? _instance;

  factory SecureStorage() => _instance ??= SecureStorage._(const FlutterSecureStorage());

  SecureStorage._(this._storage);

  final FlutterSecureStorage _storage;
  static const _tokenKey = 'TOKEN';

  Future<void> writeToLocalStorage(String keyValue, String value) async {
    await _storage.write(key: keyValue, value: value);
  }

  Future<String> readFromLocalStorage(String key) async {
    return await _storage.read(key: key) ?? "";
  }

  Future<void> deleteFromLocalStorage(String key) async {
    return _storage.delete(key: key);
  }

  Future<void> persistTokan(String tokan) async {
    await _storage.write(key: _tokenKey, value: tokan);
  }

  Future<String?> getToken() async {
    return _storage.read(key: _tokenKey);
  }

  Future<void> deleteToken() async {
    return _storage.delete(key: _token<PERSON>ey);
  }

  Future<void> deleteAll() async {
    return _storage.deleteAll();
  }
}
