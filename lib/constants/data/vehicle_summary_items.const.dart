import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

List<SelectItem> getVehiclesSummaryItems(BuildContext context) {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  return [
    SelectItem(value: 'auction_unpaid', label: tr!.auctionUnpaid),
    SelectItem(value: 'auction_paid', label: tr.auctionPaid),
    SelectItem(value: 'on_the_way', label: tr.onTheWay),
    SelectItem(value: 'on_hand_no_title', label: tr.onHandNoTitle),
    SelectItem(value: 'on_hand_with_title', label: tr.onHandWithTitle),
    SelectItem(value: 'on_hand_with_load', label: tr.onHandWithLoad),
    SelectItem(value: 'shipped', label: tr.shipped),
    SelectItem(value: 'total', label: tr.all),
  ];
}
