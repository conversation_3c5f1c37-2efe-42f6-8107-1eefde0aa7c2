import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

List<SelectItem> getShipmentsSummaryItems(BuildContext context) {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  return [
    SelectItem(value: 'at_loading', label: tr!.atLoading),
    SelectItem(value: 'on_the_way', label: tr.onTheWay),
    SelectItem(value: 'arrived', label: tr.arrived),
    SelectItem(value: 'total', label: tr.all),
  ];
}
