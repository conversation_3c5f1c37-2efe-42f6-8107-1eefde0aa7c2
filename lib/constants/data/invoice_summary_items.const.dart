import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

List<SelectItem> getInvoicesSummaryItems(BuildContext context) {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  return [
    SelectItem(value: 'open', label: tr!.open),
    SelectItem(value: 'paid', label: tr.paid),
    SelectItem(value: 'past_due', label: tr.pastDue),
    SelectItem(value: 'total', label: tr.all),
  ];
}
