import 'package:dio_client/dio_client.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pgl_mobile_app/configs/pages.dart';
import 'package:pgl_mobile_app/configs/themes/theme.dart';
import 'package:pgl_mobile_app/constants/routes.const.dart';
import 'package:pgl_mobile_app/exports/blocs.exports.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/screens/misc/splash.screen.dart';
import 'package:secure_storage/secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:provider/provider.dart';

class PGLApp extends StatefulWidget {
  const PGLApp({super.key, required this.prefs});

  final SharedPreferences prefs;

  @override
  State<PGLApp> createState() => _PGLAppState();
}

class _PGLAppState extends State<PGLApp> {
  late final AuthenticationRepository _authenticationRepository;
  late final UserRepository _userRepository;
  late final MetaDataRepository _metaDataRepository;
  late final VehiclesRepository _vehiclesRepository;
  late final ShipmentsRepository _shipmentsRepository;
  late final InvoicesRepository _invoicesRepository;
  late final MixshippingRepository _mixShippingRepository;
  late final AnnouncementsRepository _announcementsRepository;
  late final NotificationsRepository _notificationsRepository;
  late final ShippingRatesRepository _shippingRatesRepository;
  late final MixShippingRatesRepository _mixShippingRatesRepository;
  late final TransactionsRepository _transactionsRepository;
  late final PaymentsRepository _paymentsRepository;
  late final AuctionPaymentsRepository _auctionPaymentsRepository;
  late final TrackingRepository _trackingRepository;
  late final TowingRatesRepository _towingRatesRepository;
  late final DioClient _dioClient;
  late final ConnectivityBloc _connectivityBloc;
  late final AuthenticationBloc _authenticationBloc;
  late final DashboardBloc _dashboardBloc;
  late final VehiclesBloc _vehiclesBloc;
  late final ShipmentBloc _shipmentsBloc;
  late final InvoicesBloc _invoicesBloc;
  late final MixShippingBloc _mixShippingBloc;
  late final AnnouncementsBloc _announcementsBloc;
  late final NotificationsBloc _notificationsBloc;
  late final GlobalSearchBloc _globalSearchBloc;
  late final ShippingRatesBloc _shippingRatesBloc;
  late final TransactionsBloc _transactionsBloc;
  late final PaymentsBloc _paymentsBloc;
  late final AuctionPaymentsBloc _auctionPaymentsBloc;
  late final TowingRatesBloc _towingRatesBloc;
  FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    super.initState();
    _dioClient = DioClient();
    _authenticationRepository = AuthenticationRepository(dioClient: _dioClient);
    _userRepository = UserRepository(dioClient: _dioClient);
    _metaDataRepository = MetaDataRepository(dioClient: _dioClient);
    _vehiclesRepository = VehiclesRepository(dioClient: _dioClient);
    _shipmentsRepository = ShipmentsRepository(dioClient: _dioClient);
    _invoicesRepository = InvoicesRepository(dioClient: _dioClient);
    _mixShippingRepository = MixshippingRepository(dioClient: _dioClient);
    _announcementsRepository = AnnouncementsRepository(dioClient: _dioClient);
    _notificationsRepository = NotificationsRepository(dioClient: _dioClient);
    _shippingRatesRepository = ShippingRatesRepository(dioClient: _dioClient);
    _mixShippingRatesRepository = MixShippingRatesRepository(dioClient: _dioClient);
    _transactionsRepository = TransactionsRepository(dioClient: _dioClient);
    _trackingRepository = TrackingRepository(dioClient: _dioClient);
    _paymentsRepository = PaymentsRepository(dioClient: _dioClient);
    _auctionPaymentsRepository = AuctionPaymentsRepository(dioClient: _dioClient);
    _towingRatesRepository = TowingRatesRepository(dioClient: _dioClient);
    _connectivityBloc = ConnectivityBloc();
    _dashboardBloc = DashboardBloc(
      metaDataRepository: _metaDataRepository,
      connectivityBloc: _connectivityBloc,
      context: context,
    );
    _authenticationBloc = AuthenticationBloc(
      authenticationRepository: _authenticationRepository,
      userRepository: _userRepository,
      connectivityBloc: _connectivityBloc,
      onLogout: _onLogout,
    );
    _vehiclesBloc = VehiclesBloc(
      metaDataRepository: _metaDataRepository,
      vehiclesRepository: _vehiclesRepository,
      connectivityBloc: _connectivityBloc,
    );
    _shipmentsBloc = ShipmentBloc(
      metaDataRepository: _metaDataRepository,
      shipmentRepository: _shipmentsRepository,
      connectivityBloc: _connectivityBloc,
    );
    _invoicesBloc = InvoicesBloc(
      metaDataRepository: _metaDataRepository,
      invoicesRepository: _invoicesRepository,
      connectivityBloc: _connectivityBloc,
    );
    _mixShippingBloc = MixShippingBloc(
      metaDataRepository: _metaDataRepository,
      mixShippingRepository: _mixShippingRepository,
      connectivityBloc: _connectivityBloc,
    );
    _announcementsBloc = AnnouncementsBloc(
      announcementsRepository: _announcementsRepository,
      connectivityBloc: _connectivityBloc,
      dashboardBloc: _dashboardBloc,
    );
    _notificationsBloc = NotificationsBloc(
      notificationsRepository: _notificationsRepository,
      connectivityBloc: _connectivityBloc,
      dashboardBloc: _dashboardBloc,
    );
    _globalSearchBloc = GlobalSearchBloc(
      vehiclesRepository: _vehiclesRepository,
      shipmentsRepository: _shipmentsRepository,
      invoicesRepository: _invoicesRepository,
      mixshippingRepository: _mixShippingRepository,
      connectivityBloc: _connectivityBloc,
    );
    _shippingRatesBloc = ShippingRatesBloc(
      shippingRatesRepository: _shippingRatesRepository,
      connectivityBloc: _connectivityBloc,
    );
    _transactionsBloc = TransactionsBloc(
      transactionsRepository: _transactionsRepository,
      connectivityBloc: _connectivityBloc,
    );
    _paymentsBloc = PaymentsBloc(
      paymentsRepository: _paymentsRepository,
      connectivityBloc: _connectivityBloc,
    );
    _auctionPaymentsBloc = AuctionPaymentsBloc(
      auctionPaymentsRepository: _auctionPaymentsRepository,
      connectivityBloc: _connectivityBloc,
    );
    _towingRatesBloc = TowingRatesBloc(
      towingRatesRepository: _towingRatesRepository,
      connectivityBloc: _connectivityBloc,
    );
  }

  void _onLogout() {
    _dashboardBloc.add(DashboardDataSetStateInitaial());
    _notificationsBloc.add(NotificationsSetStateInitail());
    _vehiclesBloc.add(VehiclesSetStateInitail());
    _shipmentsBloc.add(ShipmentsSetStateInitail());
    _invoicesBloc.add(InvoicesSetStateInitail());
    _mixShippingBloc.add(MixShippingSetStateInitail());
  }

  @override
  void dispose() {
    _authenticationRepository.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiRepositoryProvider(
      providers: [
        RepositoryProvider.value(
          value: _dioClient,
        ),
        RepositoryProvider.value(
          value: _authenticationRepository,
        ),
        RepositoryProvider.value(
          value: _userRepository,
        ),
        RepositoryProvider.value(
          value: _metaDataRepository,
        ),
        RepositoryProvider.value(
          value: _vehiclesRepository,
        ),
        RepositoryProvider.value(
          value: _shipmentsRepository,
        ),
        RepositoryProvider.value(
          value: _invoicesRepository,
        ),
        RepositoryProvider.value(
          value: _mixShippingRepository,
        ),
        RepositoryProvider.value(
          value: _mixShippingRatesRepository,
        ),
        RepositoryProvider.value(
          value: _transactionsRepository,
        ),
        RepositoryProvider.value(
          value: _paymentsRepository,
        ),
        RepositoryProvider.value(
          value: _trackingRepository,
        ),
        RepositoryProvider.value(
          value: _towingRatesRepository,
        ),
      ],
      child: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (_) => _connectivityBloc,
          ),
          BlocProvider(
            create: (context) => _dashboardBloc,
          ),
          BlocProvider(
            create: (context) => _vehiclesBloc,
          ),
          BlocProvider(
            create: (context) => _shipmentsBloc,
          ),
          BlocProvider(
            create: (context) => _invoicesBloc,
          ),
          BlocProvider(
            create: (context) => _mixShippingBloc,
          ),
          BlocProvider(
            create: (context) => _announcementsBloc,
          ),
          BlocProvider(
            create: (context) => _notificationsBloc,
          ),
          BlocProvider(
            create: (context) => _globalSearchBloc,
          ),
          BlocProvider(
            create: (context) => _shippingRatesBloc,
          ),
          BlocProvider(
            create: (_) => _authenticationBloc,
          ),
          BlocProvider(
            create: (_) => _transactionsBloc,
          ),
          BlocProvider(
            create: (_) => _paymentsBloc,
          ),
          BlocProvider(
            create: (_) => _auctionPaymentsBloc,
          ),
          BlocProvider(
            create: (_) => _towingRatesBloc,
          ),
        ],
        child: AppView(prefs: widget.prefs),
      ),
    );
  }
}

class AppView extends StatefulWidget {
  const AppView({super.key, required this.prefs});
  final SharedPreferences prefs;
  @override
  State<AppView> createState() => _AppViewState();
}

class _AppViewState extends State<AppView> {
  final SecureStorage secureStorage = SecureStorage();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => LocaleModel(widget.prefs),
      child: Consumer<LocaleModel>(builder: (context, localeModel, child) {
        return GetMaterialApp(
          title: 'PGL',
          debugShowCheckedModeBanner: false,
          // localization config
          localizationsDelegates: AppLocalizations.localizationsDelegates,
          supportedLocales: AppLocalizations.supportedLocales,
          locale: localeModel.locale,
          // theme config
          themeMode: ThemeMode.light,
          theme: getLightTheme(localeModel.locale.languageCode),
          darkTheme: getDarkTheme(localeModel.locale.languageCode),
          // route config
          initialRoute: splash,
          getPages: pages,
          onGenerateRoute: (_) => SplashScreen.route(),
          navigatorObservers: [FirebaseAnalyticsObserver(analytics: FirebaseAnalytics.instance)],
          builder: (context, child) {
            return BlocListener<AuthenticationBloc, AuthenticationState>(
              listenWhen: (previous, current) => previous.status != current.status,
              listener: (context, state) {
                switch (state.status) {
                  case AuthenticationStatus.authenticated:
                    Get.offAllNamed(home);
                  case AuthenticationStatus.unauthenticated:
                  case AuthenticationStatus.unknown:
                    Get.offAllNamed(login);
                }
              },
              child: SkeletonizerConfig(
                data: SkeletonizerConfigData(
                  effect: ShimmerEffect(baseColor: Theme.of(context).colorScheme.primary.withValues(alpha: .1)),
                ),
                child: child ?? Container(),
              ),
            );
          },
        );
      }),
    );
  }
}

class LocaleModel extends ChangeNotifier {
  Locale _locale = AppLocalizations.supportedLocales.contains(Get.deviceLocale)
      ? Get.deviceLocale ?? const Locale('en')
      : const Locale('en');
  final SharedPreferences _prefs;

  LocaleModel(this._prefs) {
    var selectedLocale = _prefs.getString("selectedLocale");
    if (selectedLocale != null) {
      _locale = Locale(selectedLocale);
    }
  }

  Locale get locale => _locale;

  void set(Locale locale) {
    _locale = locale;
    _prefs.setString('selectedLocale', locale.toString());
    notifyListeners();
  }
}
