import 'package:dio_client/dio_client.dart';

final List<int> _imageSizes = [900, 250, 100];
final List<int> _imageSizes2 = [1024, 250, 100];

DioClient dioClient = DioClient();

String getImageSizeUrl({String? url, int size = 900}) {
  if (url == null) return '';
  if (_imageSizes.contains(size)) {
    return '${dioClient.getMinioUrl()}/${url.replaceFirst('/900/', '/$size/')}';
  }
  return '${dioClient.getMinioUrl()}/$url';
}

String getImageSizeUrl2({String? url, int size = 900}) {
  if (url == null) return '';
  if (_imageSizes2.contains(size)) {
    return '${dioClient.getMinioUrl()}${url.replaceFirst('/250/', '/$size/')}';
  }
  return '${dioClient.getMinioUrl()}$url';
}
