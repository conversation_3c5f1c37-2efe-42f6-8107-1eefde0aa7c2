import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_upgrade_version/flutter_upgrade_version.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

class CheckForUpdates {
  final BuildContext context;
  late final AppLocalizations? tr = AppLocalizations.of(context);

  CheckForUpdates.init({required this.context});

  void checkForUpdate() async {
    PackageInfo packageInfo = await PackageManager.getPackageInfo();
    // Android
    if (Platform.isAndroid) {
      InAppUpdateManager manager = InAppUpdateManager();
      AppUpdateInfo? appUpdateInfo = await manager.checkForUpdate();

      if (appUpdateInfo == null) return; //Exception
      if (appUpdateInfo.updateAvailability == UpdateAvailability.developerTriggeredUpdateInProgress) {
        String? message = await manager.startAnUpdate(type: AppUpdateType.immediate);
        if (message != null) {
          showUpdateDialog();
        }
      } else if (appUpdateInfo.updateAvailability == UpdateAvailability.updateAvailable) {
        if (appUpdateInfo.immediateAllowed) {
          String? message = await manager.startAnUpdate(type: AppUpdateType.immediate);
          if (message != null) {
            showUpdateDialog();
          }
        } else if (appUpdateInfo.flexibleAllowed) {
          String? message = await manager.startAnUpdate(type: AppUpdateType.flexible);
          if (message != null) {
            showUpdateDialog();
          }
        } else {
          showUpdateDialog();
        }
      }
    }

    ///iOS
    if (Platform.isIOS) {
      VersionInfo versionInfo2 = await UpgradeVersion.getiOSStoreVersion(
        packageInfo: packageInfo,
      );
      log(versionInfo2.canUpdate.toString());
      log(versionInfo2.toString());
      if (versionInfo2.canUpdate) {
        showUpdateDialog();
      }
    }
  }

  void showUpdateDialog() {
    showDialog<void>(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(tr!.updateAvailable),
          content: Text(tr!.youAreUsingAnOlderVersion),
          actions: <Widget>[
            FilledButton(
              style: TextButton.styleFrom(
                textStyle: Theme.of(context).textTheme.labelLarge,
              ),
              child: Text(tr!.update),
              onPressed: () async {
                if (Platform.isAndroid) {
                  launchUrl(Uri.parse('https://play.google.com/store/apps/details?id=com.pgl.app'));
                }
                if (Platform.isIOS) {
                  launchUrl(Uri.parse('https://apps.apple.com/us/app/peace-global-logistics/id1516078234'));
                }
              },
            )
          ],
        );
      },
    );
  }
}
