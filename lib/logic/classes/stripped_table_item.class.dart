import 'package:flutter/material.dart';

class StrippedTableItem<T> {
  final String label;
  final String value;
  final Widget? valueWidget;
  final void Function()? onTap;
  final T? metaData;
  final bool selectable;
  final TextAlign textAlign;
  StrippedTableItem({
    required this.label,
    required this.value,
    this.valueWidget,
    this.onTap,
    this.metaData,
    this.selectable = false,
    this.textAlign = TextAlign.end,
  });
}
