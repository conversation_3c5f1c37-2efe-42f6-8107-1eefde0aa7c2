part of 'payments_bloc.dart';

sealed class PaymentsEvent extends Equatable {
  const PaymentsEvent();

  @override
  List<Object> get props => [];
}

final class PaymentsFetched extends PaymentsEvent {}

final class PaymentsSetStateInitail extends PaymentsEvent {}

final class PaymentsCountChanged extends PaymentsEvent {
  const PaymentsCountChanged({required this.transactionsCount});

  final int transactionsCount;
}

final class PaymentsFilterChanged extends PaymentsEvent {
  const PaymentsFilterChanged({required this.paymentsFilter});

  final PaymentsFilter paymentsFilter;
}

final class PaymentSingleFetch extends PaymentsEvent {
  const PaymentSingleFetch({required this.payment});

  final PaymentModel payment;
}
