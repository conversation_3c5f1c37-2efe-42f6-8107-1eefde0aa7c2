import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio_client/dio_client.dart';
import 'package:equatable/equatable.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/logic/blocs/connectivity/bloc/connectivity_bloc.dart';
import 'package:stream_transform/stream_transform.dart';

part 'payments_event.dart';
part 'payments_state.dart';

class PaymentsBloc extends HydratedBloc<PaymentsEvent, PaymentsState> {
  PaymentsBloc({
    required this.paymentsRepository,
    required this.connectivityBloc,
  }) : super(const PaymentsState()) {
    on<PaymentsFetched>(_onPaymentsFetched, transformer: (events, mapper) {
      return events.debounce(const Duration(seconds: 1)).asyncExpand(mapper);
    });
    on<PaymentSingleFetch>(_onPaymentSingleFetch);
    on<PaymentsCountChanged>(
      (event, emit) {
        return emit(
          state.copyWith(
            transactionsCount: event.transactionsCount,
          ),
        );
      },
    );
    on<PaymentsSetStateInitail>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: PaymentsDataLoadingStatus.initial,
            payments: [],
            hasReachedMax: false,
            paymentsFilter: state.paymentsFilter.copyWith(
              page: 1,
            ),
          ),
        );
      },
    );
    on<PaymentsFilterChanged>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: PaymentsDataLoadingStatus.initial,
            payments: [],
            hasReachedMax: false,
            paymentsFilter: event.paymentsFilter,
          ),
        );
      },
    );
  }

  final PaymentsRepository paymentsRepository;
  final ConnectivityBloc connectivityBloc;
  final DioClient dioClient = DioClient();

  // transactions list data
  Future<void> _onPaymentsFetched(PaymentsFetched event, emit) async {
    if (state.hasReachedMax) return;
    if (connectivityBloc.state.result == ConnectivityResult.none) return;
    try {
      if (state.status == PaymentsDataLoadingStatus.initial) {
        final List<PaymentModel> payments = await paymentsRepository.getPayments(state.paymentsFilter);
        return emit(
          state.copyWith(
            status: PaymentsDataLoadingStatus.success,
            payments: payments,
            hasReachedMax: payments.length < state.paymentsFilter.perPage,
            paymentsFilter: state.paymentsFilter.copyWith(
              page: state.paymentsFilter.page + 1,
            ),
          ),
        );
      }
      final payments = await paymentsRepository.getPayments(state.paymentsFilter);
      emit(
        state.copyWith(
          status: PaymentsDataLoadingStatus.success,
          payments: List.of(state.payments)..addAll(payments),
          hasReachedMax: payments.length < state.paymentsFilter.perPage,
          paymentsFilter: state.paymentsFilter.copyWith(
            page: state.paymentsFilter.page + 1,
          ),
        ),
      );
    } catch (_) {
      emit(state.copyWith(status: PaymentsDataLoadingStatus.failure));
    }
  }

  Future<void> _onPaymentSingleFetch(PaymentSingleFetch event, emit) async {
    if (event.payment.payments.isNotEmpty) return;
    if (connectivityBloc.state.result == ConnectivityResult.none) return;
    try {
      return emit(
        state.copyWith(
          payments: state.payments.map<PaymentModel>(
            (PaymentModel p) {
              if (p.id == event.payment.id) {
                return event.payment;
              }
              return p;
            },
          ).toList(),
        ),
      );
    } catch (_) {}
  }

  @override
  PaymentsState? fromJson(Map<String, dynamic> json) {
    PaymentsState st = PaymentsState(
      payments: json['payments'].map<PaymentModel>((item) => PaymentModel.fromMap(item)).toList(),
    );
    return st;
  }

  @override
  Map<String, dynamic>? toJson(PaymentsState state) {
    Map<String, dynamic> map = {
      'payments': state.payments.map((e) => e.toMap()).toList(),
    };
    return map;
  }
}
