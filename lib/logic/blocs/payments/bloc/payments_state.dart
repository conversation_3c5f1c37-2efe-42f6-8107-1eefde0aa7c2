part of 'payments_bloc.dart';

enum PaymentsDataLoadingStatus { initial, success, failure }

final class PaymentsState extends Equatable {
  const PaymentsState({
    this.paymentsCount = 0,
    // transactions list data
    this.status = PaymentsDataLoadingStatus.initial,
    this.payments = const <PaymentModel>[],
    this.hasReachedMax = false,
    this.paymentsFilter = const PaymentsFilter(),
  });
  final int paymentsCount;
  // transactions list data
  final PaymentsDataLoadingStatus status;
  final List<PaymentModel> payments;
  final bool hasReachedMax;
  final PaymentsFilter paymentsFilter;

  PaymentsState copyWith({
    int? transactionsCount,
    // transactions list data
    PaymentsDataLoadingStatus? status,
    List<PaymentModel>? payments,
    bool? hasReachedMax,
    PaymentsFilter? paymentsFilter,
  }) {
    return PaymentsState(
      paymentsCount: transactionsCount ?? paymentsCount,
      // transactions list data
      status: status ?? this.status,
      payments: payments ?? this.payments,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      paymentsFilter: paymentsFilter ?? this.paymentsFilter,
    );
  }

  @override
  List<Object> get props => [
        paymentsCount,
        // transactions list data
        status,
        payments,
        hasReachedMax,
        paymentsFilter,
      ];
}
