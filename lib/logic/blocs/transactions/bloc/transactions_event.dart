part of 'transactions_bloc.dart';

sealed class TransactionsEvent extends Equatable {
  const TransactionsEvent();

  @override
  List<Object> get props => [];
}

final class TransactionsFetched extends TransactionsEvent {}

final class TransactionsSetStateInitail extends TransactionsEvent {}

final class TransactionsCountChanged extends TransactionsEvent {
  const TransactionsCountChanged({required this.transactionsCount});

  final int transactionsCount;
}

final class TransactionsFilterChanged extends TransactionsEvent {
  const TransactionsFilterChanged({required this.transactionsFilter});

  final TransactionsFilter transactionsFilter;
}
