part of 'transactions_bloc.dart';

enum TransactionsDataLoadingStatus { initial, success, failure }

final class TransactionsState extends Equatable {
  const TransactionsState({
    this.transactionsCount = 0,
    // transactions list data
    this.status = TransactionsDataLoadingStatus.initial,
    this.transactions = const <TransactionModel>[],
    this.hasReachedMax = false,
    this.transactionsFilter = const TransactionsFilter(),
  });
  final int transactionsCount;
  // transactions list data
  final TransactionsDataLoadingStatus status;
  final List<TransactionModel> transactions;
  final bool hasReachedMax;
  final TransactionsFilter transactionsFilter;

  TransactionsState copyWith({
    int? transactionsCount,
    // transactions list data
    TransactionsDataLoadingStatus? status,
    List<TransactionModel>? transactions,
    bool? hasReachedMax,
    TransactionsFilter? transactionsFilter,
  }) {
    return TransactionsState(
      transactionsCount: transactionsCount ?? this.transactionsCount,
      // transactions list data
      status: status ?? this.status,
      transactions: transactions ?? this.transactions,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      transactionsFilter: transactionsFilter ?? this.transactionsFilter,
    );
  }

  @override
  List<Object> get props => [
        transactionsCount,
        // transactions list data
        status,
        transactions,
        hasReachedMax,
        transactionsFilter,
      ];
}
