import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio_client/dio_client.dart';
import 'package:equatable/equatable.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/logic/blocs/connectivity/bloc/connectivity_bloc.dart';
import 'package:stream_transform/stream_transform.dart';

part 'transactions_event.dart';
part 'transactions_state.dart';

class TransactionsBloc extends HydratedBloc<TransactionsEvent, TransactionsState> {
  TransactionsBloc({
    required this.transactionsRepository,
    required this.connectivityBloc,
  }) : super(const TransactionsState()) {
    on<TransactionsFetched>(_onTransactionsFetched, transformer: (events, mapper) {
      return events.debounce(const Duration(seconds: 1)).asyncExpand(mapper);
    });
    on<TransactionsCountChanged>(
      (event, emit) {
        return emit(
          state.copyWith(
            transactionsCount: event.transactionsCount,
          ),
        );
      },
    );
    on<TransactionsSetStateInitail>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: TransactionsDataLoadingStatus.initial,
            transactions: [],
            hasReachedMax: false,
            transactionsFilter: state.transactionsFilter.copyWith(
              page: 1,
            ),
          ),
        );
      },
    );
    on<TransactionsFilterChanged>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: TransactionsDataLoadingStatus.initial,
            transactions: [],
            hasReachedMax: false,
            transactionsFilter: event.transactionsFilter,
          ),
        );
      },
    );
  }

  final TransactionsRepository transactionsRepository;
  final ConnectivityBloc connectivityBloc;
  final DioClient dioClient = DioClient();

  // transactions list data
  Future<void> _onTransactionsFetched(TransactionsFetched event, emit) async {
    if (state.hasReachedMax) return;
    if (connectivityBloc.state.result == ConnectivityResult.none) return;
    try {
      if (state.status == TransactionsDataLoadingStatus.initial) {
        final List<TransactionModel> transactions =
            await transactionsRepository.getTransactions(state.transactionsFilter);
        return emit(
          state.copyWith(
            status: TransactionsDataLoadingStatus.success,
            transactions: transactions,
            hasReachedMax: transactions.length < state.transactionsFilter.perPage,
            transactionsFilter: state.transactionsFilter.copyWith(
              page: state.transactionsFilter.page + 1,
            ),
          ),
        );
      }
      final transactions = await transactionsRepository.getTransactions(state.transactionsFilter);
      emit(
        state.copyWith(
          status: TransactionsDataLoadingStatus.success,
          transactions: List.of(state.transactions)..addAll(transactions),
          hasReachedMax: transactions.length < state.transactionsFilter.perPage,
          transactionsFilter: state.transactionsFilter.copyWith(
            page: state.transactionsFilter.page + 1,
          ),
        ),
      );
    } catch (_) {
      emit(state.copyWith(status: TransactionsDataLoadingStatus.failure));
    }
  }

  @override
  TransactionsState? fromJson(Map<String, dynamic> json) {
    TransactionsState st = TransactionsState(
      transactions: json['transactions'].map<TransactionModel>((item) => TransactionModel.fromMap(item)).toList(),
    );
    return st;
  }

  @override
  Map<String, dynamic>? toJson(TransactionsState state) {
    Map<String, dynamic> map = {
      'transactions': state.transactions.map((e) => e.toMap()).toList(),
    };
    return map;
  }
}
