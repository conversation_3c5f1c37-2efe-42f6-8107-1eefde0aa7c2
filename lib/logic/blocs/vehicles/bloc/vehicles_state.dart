part of 'vehicles_bloc.dart';

enum VehicleDataLoadingStatus { initial, success, failure }

final class VehiclesState extends Equatable {
  const VehiclesState({
    this.countsInitialLoaded = false,
    this.countsLoading = false,
    this.auctionPaidCount = 0,
    this.auctionUnpaidCount = 0,
    this.onTheWayCount = 0,
    this.onHandNoTitleCount = 0,
    this.onHandWithTitleCount = 0,
    this.onHandWithLoadCount = 0,
    this.shippedCount = 0,
    this.pendingCount = 0,
    this.archivedCount = 0,
    // vehicles list data
    this.status = VehicleDataLoadingStatus.initial,
    this.vehicles = const <Vehicle>[],
    this.hasReachedMax = false,
    this.vehiclesFilter = const VehiclesFilter(),
  });

  final bool countsInitialLoaded;
  final bool countsLoading;
  final int auctionPaidCount;
  final int auctionUnpaidCount;
  final int onTheWayCount;
  final int onHandNoTitleCount;
  final int onHandWithTitleCount;
  final int onHandWithLoadCount;
  final int shippedCount;
  final int pendingCount;
  final int archivedCount;

  // vehicles list data
  // doesn't need to be hydrated
  final VehicleDataLoadingStatus status;
  final List<Vehicle> vehicles;
  final bool hasReachedMax;
  final VehiclesFilter vehiclesFilter;

  int getAllCarsTotal() {
    return auctionPaidCount +
        auctionUnpaidCount +
        onTheWayCount +
        onHandNoTitleCount +
        onHandWithTitleCount +
        onHandWithLoadCount +
        shippedCount +
        pendingCount +
        archivedCount;
  }

  int getAuctionTotal() {
    return auctionPaidCount + auctionUnpaidCount;
  }

  VehiclesState copyWith({
    bool? countsInitialLoaded,
    bool? countsLoading,
    int? auctionPaidCount,
    int? auctionUnpaidCount,
    int? onTheWayCount,
    int? onHandNoTitleCount,
    int? onHandWithTitleCount,
    int? onHandWithLoadCount,
    int? shippedCount,
    int? pendingCount,
    int? archivedCount,
    // vehicles list data
    VehicleDataLoadingStatus? status,
    List<Vehicle>? vehicles,
    bool? hasReachedMax,
    VehiclesFilter? vehiclesFilter,
  }) {
    return VehiclesState(
      countsInitialLoaded: countsInitialLoaded ?? this.countsInitialLoaded,
      countsLoading: countsLoading ?? this.countsLoading,
      auctionPaidCount: auctionPaidCount ?? this.auctionPaidCount,
      auctionUnpaidCount: auctionUnpaidCount ?? this.auctionUnpaidCount,
      onTheWayCount: onTheWayCount ?? this.onTheWayCount,
      onHandNoTitleCount: onHandNoTitleCount ?? this.onHandNoTitleCount,
      onHandWithTitleCount: onHandWithTitleCount ?? this.onHandWithTitleCount,
      onHandWithLoadCount: onHandWithLoadCount ?? this.onHandWithLoadCount,
      shippedCount: shippedCount ?? this.shippedCount,
      pendingCount: pendingCount ?? this.pendingCount,
      archivedCount: archivedCount ?? this.archivedCount,
      // vehicles list data
      status: status ?? this.status,
      vehicles: vehicles ?? this.vehicles,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      vehiclesFilter: vehiclesFilter ?? this.vehiclesFilter,
    );
  }

  @override
  List<Object> get props => [
        countsInitialLoaded,
        countsLoading,
        auctionPaidCount,
        auctionUnpaidCount,
        onTheWayCount,
        onHandNoTitleCount,
        onHandWithTitleCount,
        onHandWithLoadCount,
        shippedCount,
        pendingCount,
        archivedCount,
        // vehicles list data
        status,
        vehicles,
        hasReachedMax,
        vehiclesFilter,
      ];
}
