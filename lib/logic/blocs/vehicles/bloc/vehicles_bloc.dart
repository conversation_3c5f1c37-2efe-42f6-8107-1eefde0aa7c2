import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio_client/dio_client.dart';
import 'package:equatable/equatable.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:meta_data_repository/meta_data_repository.dart';
import 'package:pgl_mobile_app/logic/blocs/connectivity/bloc/connectivity_bloc.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:vehicles_repository/vehicles_repository.dart';

part 'vehicles_event.dart';
part 'vehicles_state.dart';

class VehiclesBloc extends HydratedBloc<VehiclesEvent, VehiclesState> {
  VehiclesBloc({
    required this.metaDataRepository,
    required this.vehiclesRepository,
    required this.connectivityBloc,
  }) : super(const VehiclesState()) {
    on<LoadVehiclesCounts>(_onLoadVehiclesCounts);
    on<VehiclesFetched>(_onVehiclesFetched, transformer: (events, mapper) {
      return events.debounce(const Duration(seconds: 1)).asyncExpand(mapper);
    });
    on<VehiclesSetStateInitail>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: VehicleDataLoadingStatus.initial,
            vehicles: [],
            hasReachedMax: false,
            vehiclesFilter: state.vehiclesFilter.copyWith(
              page: 1,
              filterData: null,
            ),
          ),
        );
      },
    );
    on<VehiclesFilterChanged>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: VehicleDataLoadingStatus.initial,
            vehicles: [],
            hasReachedMax: false,
            vehiclesFilter: event.vehiclesFilter,
          ),
        );
      },
    );
  }

  final MetaDataRepository metaDataRepository;
  final VehiclesRepository vehiclesRepository;
  final ConnectivityBloc connectivityBloc;
  final DioClient dioClient = DioClient();

  Future<void> _onLoadVehiclesCounts(LoadVehiclesCounts event, Emitter<VehiclesState> emit) async {
    emit(
      state.copyWith(
        countsLoading: state.countsInitialLoaded == false ? true : false,
        countsInitialLoaded: true,
      ),
    );
    if (connectivityBloc.state.result != ConnectivityResult.none) {
      await Future.wait([
        _loadCounts(event, emit),
      ]);
    }
    emit(
      state.copyWith(countsLoading: false),
    );
  }

  Future<void> _loadCounts(LoadVehiclesCounts event, Emitter<VehiclesState> emit) async {
    final Map<String, dynamic>? response = await metaDataRepository.getCounts(key: 'vehicles');
    if (response != null) {
      return emit(state.copyWith(
        auctionPaidCount: response['vehicles_auction_paid'],
        auctionUnpaidCount: response['vehicles_auction_unpaid'],
        onTheWayCount: response['vehicles_on_the_way'],
        onHandNoTitleCount: response['vehicles_on_hand_no_title'],
        onHandWithTitleCount: response['vehicles_on_hand_with_title'],
        onHandWithLoadCount: response['vehicles_on_hand_with_load'],
        shippedCount: response['vehicles_shipped'],
        pendingCount: response['vehicles_pending'],
        archivedCount: response['vehicles_archived'],
      ));
    }
  }

  // vehicles list data
  Future<void> _onVehiclesFetched(VehiclesFetched event, emit) async {
    if (state.hasReachedMax) return;
    if (connectivityBloc.state.result == ConnectivityResult.none) return;
    try {
      if (state.status == VehicleDataLoadingStatus.initial) {
        final vehicles = await vehiclesRepository.getVehicles(state.vehiclesFilter);
        return emit(
          state.copyWith(
            status: VehicleDataLoadingStatus.success,
            vehicles: vehicles,
            hasReachedMax: vehicles.length < state.vehiclesFilter.perPage,
            vehiclesFilter: state.vehiclesFilter.copyWith(
              page: state.vehiclesFilter.page + 1,
            ),
          ),
        );
      }
      final vehicles = await vehiclesRepository.getVehicles(state.vehiclesFilter);
      emit(
        state.copyWith(
          status: VehicleDataLoadingStatus.success,
          vehicles: List.of(state.vehicles)..addAll(vehicles),
          hasReachedMax: vehicles.length < state.vehiclesFilter.perPage,
          vehiclesFilter: state.vehiclesFilter.copyWith(
            page: state.vehiclesFilter.page + 1,
          ),
        ),
      );
    } catch (_) {
      emit(state.copyWith(status: VehicleDataLoadingStatus.failure));
    }
  }

  @override
  VehiclesState? fromJson(Map<String, dynamic> json) {
    VehiclesState st = VehiclesState(
      auctionPaidCount: json['vehicles_auction_paid'],
      auctionUnpaidCount: json['vehicles_auction_unpaid'],
      onTheWayCount: json['vehicles_on_the_way'],
      onHandNoTitleCount: json['vehicles_on_hand_no_title'],
      onHandWithTitleCount: json['vehicles_on_hand_with_title'],
      shippedCount: json['vehicles_shipped'],
      pendingCount: json['vehicles_pending'],
      archivedCount: json['vehicles_archived'],
    );
    return st;
  }

  @override
  Map<String, dynamic>? toJson(VehiclesState state) {
    Map<String, dynamic> map = {
      'vehicles_auction_paid': state.auctionPaidCount,
      'vehicles_auction_unpaid': state.auctionUnpaidCount,
      'vehicles_on_the_way': state.onTheWayCount,
      'vehicles_on_hand_no_title': state.onHandNoTitleCount,
      'vehicles_on_hand_with_title': state.onHandWithTitleCount,
      'vehicles_shipped': state.shippedCount,
      'vehicles_pending': state.pendingCount,
      'vehicles_archived': state.archivedCount,
    };
    return map;
  }
}
