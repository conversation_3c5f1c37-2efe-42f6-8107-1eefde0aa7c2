part of 'vehicles_bloc.dart';

sealed class VehiclesEvent extends Equatable {
  const VehiclesEvent();

  @override
  List<Object> get props => [];
}

final class LoadVehiclesCounts extends VehiclesEvent {}

final class VehiclesFetched extends VehiclesEvent {}

final class VehiclesSetStateInitail extends VehiclesEvent {}

final class VehiclesFilterChanged extends VehiclesEvent {
  const VehiclesFilterChanged({required this.vehiclesFilter});

  final VehiclesFilter vehiclesFilter;
}
