part of 'notifications_bloc.dart';

enum NotificationsDataLoadingStatus { initial, success, failure }

final class NotificationsState extends Equatable {
  const NotificationsState({
    // notifications list data
    this.status = NotificationsDataLoadingStatus.initial,
    this.notifications = const <NotificationModel>[],
    this.hasReachedMax = false,
    this.notificationsFilter = const NotificationsFilter(),
    this.announcementUnread = 0,
    this.arrivalNoticeUnread = 0,
    this.shippingRateUnread = 0,
    this.mixShippingRateUnread = 0,
    this.transactionUnread = 0,
    this.activeTab = NotificationType.announcement,
  });
  // notifications list data
  final NotificationsDataLoadingStatus status;
  final List<NotificationModel> notifications;
  final bool hasReachedMax;
  final NotificationsFilter notificationsFilter;
  final int announcementUnread;
  final int arrivalNoticeUnread;
  final int shippingRateUnread;
  final int mixShippingRateUnread;
  final int transactionUnread;
  final NotificationType activeTab;

  NotificationsState copyWith({
    // notifications list data
    NotificationsDataLoadingStatus? status,
    List<NotificationModel>? notifications,
    bool? hasReachedMax,
    NotificationsFilter? notificationsFilter,
    int? announcementUnread,
    int? arrivalNoticeUnread,
    int? shippingRateUnread,
    int? mixShippingRateUnread,
    int? transactionUnread,
    NotificationType? activeTab,
  }) {
    return NotificationsState(
      // notifications list data
      status: status ?? this.status,
      notifications: notifications ?? this.notifications,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      notificationsFilter: notificationsFilter ?? this.notificationsFilter,
      announcementUnread: announcementUnread ?? this.announcementUnread,
      arrivalNoticeUnread: arrivalNoticeUnread ?? this.arrivalNoticeUnread,
      shippingRateUnread: shippingRateUnread ?? this.shippingRateUnread,
      mixShippingRateUnread: mixShippingRateUnread ?? this.mixShippingRateUnread,
      transactionUnread: transactionUnread ?? this.transactionUnread,
      activeTab: activeTab ?? this.activeTab,
    );
  }

  @override
  List<Object> get props => [
        // notifications list data
        status,
        notifications,
        hasReachedMax,
        notificationsFilter,
        announcementUnread,
        arrivalNoticeUnread,
        shippingRateUnread,
        mixShippingRateUnread,
        transactionUnread,
        activeTab,
      ];
}
