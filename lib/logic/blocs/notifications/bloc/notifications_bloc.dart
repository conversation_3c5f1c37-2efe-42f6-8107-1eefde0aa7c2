import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio_client/dio_client.dart';
import 'package:equatable/equatable.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:notifications_repository/notifications_repository.dart';
import 'package:pgl_mobile_app/logic/blocs/connectivity/bloc/connectivity_bloc.dart';
import 'package:pgl_mobile_app/logic/blocs/dashboard/bloc/dashboard_bloc.dart';
import 'package:stream_transform/stream_transform.dart';

part 'notifications_event.dart';
part 'notifications_state.dart';

class NotificationsBloc extends HydratedBloc<NotificationsEvent, NotificationsState> {
  NotificationsBloc({
    required this.notificationsRepository,
    required this.connectivityBloc,
    required this.dashboardBloc,
  }) : super(const NotificationsState()) {
    on<NotificationsFetched>(_onNotificationsFetched, transformer: (events, mapper) {
      return events.debounce(const Duration(seconds: 1)).asyncExpand(mapper);
    });
    on<NotificationsSetStateInitail>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: NotificationsDataLoadingStatus.initial,
            notifications: [],
            hasReachedMax: false,
            notificationsFilter: state.notificationsFilter.copyWith(page: 1, notificationTypes: [
              NotificationType.announcement,
            ]),
            activeTab: NotificationType.announcement,
          ),
        );
      },
    );
    on<NotificationsFilterChanged>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: NotificationsDataLoadingStatus.initial,
            notifications: [],
            hasReachedMax: false,
            notificationsFilter: event.notificationsFilter,
          ),
        );
      },
    );
    on<NotificationsActiveTabChanged>(
      (event, emit) {
        return emit(
          state.copyWith(
            activeTab: event.notificationType,
          ),
        );
      },
    );
    on<NotificationsMarkOneAsRead>(_onNotificationsMarkOneAsRead);
    on<NotificationsMarkAllAsRead>(_onNotificationsMarkAllAsRead);
    on<NotificationReceived>(
      (event, emit) {
        Map<String, int> counts = _getCounts(
          event.notification.notificationType,
        );
        return emit(
          state.copyWith(
            notifications: ([
                          NotificationType.announcement,
                          NotificationType.shippingRate,
                          NotificationType.mixShippingRate
                        ].contains(event.notification.notificationType) &&
                        state.activeTab == NotificationType.announcement) ||
                    (state.activeTab == event.notification.notificationType)
                ? [event.notification, ...state.notifications]
                : state.notifications,
            announcementUnread: counts['announcementUnread'],
            arrivalNoticeUnread: counts['arrivalNoticeUnread'],
            shippingRateUnread: counts['shippingRateUnread'],
            mixShippingRateUnread: counts['mixShippingRateUnread'],
            transactionUnread: counts['transactionUnread'],
          ),
        );
      },
    );
    on<NotificationsCountChanged>(
      (event, emit) {
        return emit(
          state.copyWith(
            announcementUnread: event.counts['announcementUnread'],
            arrivalNoticeUnread: event.counts['arrivalNoticeUnread'],
            shippingRateUnread: event.counts['shippingRateUnread'],
            mixShippingRateUnread: event.counts['mixShippingRateUnread'],
            transactionUnread: event.counts['transactionUnread'],
          ),
        );
      },
    );
    _dashboardState = dashboardBloc.stream.listen((DashboardState state) async {
      add(NotificationsCountChanged(counts: {
        'arrivalNoticeUnread': state.countArrivalNoticesNotifications,
        'shippingRateUnread': state.countShippingtRatesNotifications,
      }));
    });
  }

  late StreamSubscription<DashboardState> _dashboardState;

  final NotificationsRepository notificationsRepository;
  final ConnectivityBloc connectivityBloc;
  final DashboardBloc dashboardBloc;
  final DioClient dioClient = DioClient();

  // notifications list data
  Future<void> _onNotificationsFetched(NotificationsFetched event, emit) async {
    if (state.hasReachedMax) return;
    if (connectivityBloc.state.result == ConnectivityResult.none) return;
    try {
      final Map<String, dynamic> data = await notificationsRepository.getNotifications(
        state.notificationsFilter,
      );
      final List<NotificationModel> notifications = data['items'];
      dashboardBloc.add(
        NotificationCountChanged(
          countNotifications: data['announcement_unread'] +
              data['arrival_notice_unread'] +
              data['shipping_rate_unread'] +
              data['mix_shipping_rate_unread'] +
              data['transaction_unread'],
        ),
      );
      emit(
        state.copyWith(
          status: NotificationsDataLoadingStatus.success,
          notifications: List.of(state.notifications)..addAll(notifications),
          hasReachedMax: notifications.length < state.notificationsFilter.perPage,
          notificationsFilter: state.notificationsFilter.copyWith(
            page: state.notificationsFilter.page + 1,
          ),
          announcementUnread: data['announcement_unread'],
          arrivalNoticeUnread: data['arrival_notice_unread'],
          shippingRateUnread: data['shipping_rate_unread'],
          mixShippingRateUnread: data['mix_shipping_rate_unread'],
          transactionUnread: data['transaction_unread'],
        ),
      );
    } catch (_) {
      emit(state.copyWith(status: NotificationsDataLoadingStatus.failure));
    }
  }

  Future<void> _onNotificationsMarkAllAsRead(NotificationsMarkAllAsRead event, emit) async {
    if (connectivityBloc.state.result == ConnectivityResult.none) return;
    try {
      final bool response = await notificationsRepository.markAllAsRead(
        event.notificationType == NotificationType.announcement
            ? [
                NotificationType.announcement,
                NotificationType.shippingRate,
                NotificationType.mixShippingRate,
              ]
            : [event.notificationType],
      );
      Map<String, int> counts = _getCountsMarkAllasRead(event.notificationType);
      if (response) {
        emit(
          state.copyWith(
            notifications: state.notifications.map<NotificationModel>(
              (item) {
                if (item.notificationType == event.notificationType) {
                  return item.copyWith(
                    seenAt: item.seenAt ?? DateTime.now(),
                  );
                }
                return item;
              },
            ).toList(),
            announcementUnread: counts['announcementUnread'],
            arrivalNoticeUnread: counts['arrivalNoticeUnread'],
            shippingRateUnread: counts['shippingRateUnread'],
            mixShippingRateUnread: counts['mixShippingRateUnread'],
            transactionUnread: counts['transactionUnread'],
          ),
        );
      }
    } catch (_) {}
  }

  Future<void> _onNotificationsMarkOneAsRead(NotificationsMarkOneAsRead event, emit) async {
    if (connectivityBloc.state.result == ConnectivityResult.none) return;
    try {
      final bool response = await notificationsRepository.markOneAsRead(event.notification);
      if (response) {
        dashboardBloc.add(
          NotificationCountChanged(countNotifications: dashboardBloc.state.countNotificationsAll - 1),
        );
        Map<String, int> counts = _getCounts(event.notification.notificationType, increement: false);
        emit(
          state.copyWith(
            notifications: state.notifications.map<NotificationModel>(
              (item) {
                if (item.id == event.notification.id) {
                  return item.copyWith(
                    seenAt: item.seenAt ?? DateTime.now(),
                  );
                }
                return item;
              },
            ).toList(),
            announcementUnread: counts['announcementUnread'],
            arrivalNoticeUnread: counts['arrivalNoticeUnread'],
            shippingRateUnread: counts['shippingRateUnread'],
            mixShippingRateUnread: counts['mixShippingRateUnread'],
            transactionUnread: counts['transactionUnread'],
          ),
        );
      }
    } catch (_) {}
  }

  Map<String, int> _getCounts(NotificationType type, {bool increement = true}) {
    Map<String, int> counts = {
      'announcementUnread': type == NotificationType.announcement
          ? state.announcementUnread + (increement ? 1 : -1)
          : state.announcementUnread,
      'arrivalNoticeUnread': type == NotificationType.arrivalNotice
          ? state.arrivalNoticeUnread + (increement ? 1 : -1)
          : state.arrivalNoticeUnread,
      'shippingRateUnread': type == NotificationType.shippingRate
          ? state.shippingRateUnread + (increement ? 1 : -1)
          : state.shippingRateUnread,
      'mixShippingRateUnread': type == NotificationType.mixShippingRate
          ? state.mixShippingRateUnread + (increement ? 1 : -1)
          : state.mixShippingRateUnread,
      'transactionUnread': type == NotificationType.transaction
          ? state.transactionUnread + (increement ? 1 : -1)
          : state.transactionUnread,
    };
    dashboardBloc.add(
      ShippingRatesUnreadCountChanged(
        countShippingRates: type == NotificationType.shippingRate && increement
            ? dashboardBloc.state.countUnreadShippingtRates + 1
            : dashboardBloc.state.countUnreadShippingtRates,
      ),
    );
    _changeDashboardCounts(counts);
    return counts;
  }

  Map<String, int> _getCountsMarkAllasRead(
    NotificationType type,
  ) {
    Map<String, int> counts = {
      'announcementUnread': type == NotificationType.announcement ? 0 : state.announcementUnread,
      'arrivalNoticeUnread': type == NotificationType.arrivalNotice ? 0 : state.arrivalNoticeUnread,
      'shippingRateUnread': type == NotificationType.shippingRate ? 0 : state.shippingRateUnread,
      'mixShippingRateUnread': type == NotificationType.mixShippingRate ? 0 : state.mixShippingRateUnread,
      'transactionUnread': type == NotificationType.transaction ? 0 : state.transactionUnread,
    };
    _changeDashboardCounts(counts);
    return counts;
  }

  void _changeDashboardCounts(Map<String, int> counts) {
    dashboardBloc.add(
      NotificationCountChanged(
        countNotifications: (counts['announcementUnread'] ?? 0) +
            (counts['arrivalNoticeUnread'] ?? 0) +
            (counts['shippingRateUnread'] ?? 0) +
            (counts['mixShippingRateUnread'] ?? 0) +
            (counts['transactionUnread'] ?? 0),
      ),
    );
    dashboardBloc.add(
      ShippingRatesNotificationsCountChanged(
        countShippingRates: (counts['shippingRateUnread'] ?? 0),
      ),
    );
    dashboardBloc.add(
      ArrivalNoticesNotificationsCountChanged(
        countArrivalNotices: (counts['arrivalNoticeUnread'] ?? 0),
      ),
    );
  }

  @override
  NotificationsState? fromJson(Map<String, dynamic> json) {
    NotificationsState st = NotificationsState(
      notifications: json['notifications'].map<NotificationModel>((item) => NotificationModel.fromMap(item)).toList(),
    );
    return st;
  }

  @override
  Map<String, dynamic>? toJson(NotificationsState state) {
    Map<String, dynamic> map = {
      'notifications': state.notifications.map((e) => e.toMap()).toList(),
    };
    return map;
  }

  @override
  Future<void> close() {
    _dashboardState.cancel();
    return super.close();
  }
}
