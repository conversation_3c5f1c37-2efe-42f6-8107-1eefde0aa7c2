part of 'notifications_bloc.dart';

sealed class NotificationsEvent extends Equatable {
  const NotificationsEvent();

  @override
  List<Object> get props => [];
}

final class NotificationsFetched extends NotificationsEvent {}

final class NotificationsMarkAllAsRead extends NotificationsEvent {
  final NotificationType notificationType;

  const NotificationsMarkAllAsRead({required this.notificationType});
}

final class NotificationsMarkOneAsRead extends NotificationsEvent {
  final NotificationModel notification;

  const NotificationsMarkOneAsRead({required this.notification});
}

final class NotificationsSetStateInitail extends NotificationsEvent {}

final class NotificationReceived extends NotificationsEvent {
  const NotificationReceived({
    required this.notification,
  });

  final NotificationModel notification;
}

final class NotificationsFilterChanged extends NotificationsEvent {
  const NotificationsFilterChanged({required this.notificationsFilter});

  final NotificationsFilter notificationsFilter;
}

final class NotificationsActiveTabChanged extends NotificationsEvent {
  const NotificationsActiveTabChanged({required this.notificationType});

  final NotificationType notificationType;
}

final class NotificationsCountChanged extends NotificationsEvent {
  const NotificationsCountChanged({required this.counts});

  final Map<String, int> counts;
}
