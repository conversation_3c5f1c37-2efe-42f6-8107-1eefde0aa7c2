import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:equatable/equatable.dart';

part 'connectivity_event.dart';
part 'connectivity_state.dart';

class ConnectivityBloc extends Bloc<ConnectivityEvent, ConnectivityState> {
  ConnectivityBloc() : super(const ConnectivityState()) {
    on<_ConnectivityResultChanged>(_onConnectivityResultChanged);
    on<ConnectivityResultInitial>(_onConnectivityResultInitial);
    _subscription = Connectivity().onConnectivityChanged.listen((List<ConnectivityResult> result) {
      if (result.isNotEmpty) {
        add(_ConnectivityResultChanged(result[0]));
      }
    });
  }

  late StreamSubscription _subscription;

  void _onConnectivityResultChanged(_ConnectivityResultChanged event, Emitter<ConnectivityState> emit) {
    emit(state.copyWith(result: event.result));
  }

  Future<void> _onConnectivityResultInitial(ConnectivityResultInitial event, Emitter<ConnectivityState> emit) async {
    final List<ConnectivityResult> result = await (Connectivity().checkConnectivity());
    if (result.isNotEmpty) {
      emit(state.copyWith(result: result[0]));
    }
  }

  @override
  Future<void> close() {
    _subscription.cancel();
    return super.close();
  }
}
