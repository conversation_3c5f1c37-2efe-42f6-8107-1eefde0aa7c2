import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio_client/dio_client.dart';
import 'package:equatable/equatable.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:meta_data_repository/meta_data_repository.dart';
import 'package:pgl_mobile_app/logic/blocs/connectivity/bloc/connectivity_bloc.dart';
import 'package:shipments_repository/shipments_repository.dart';
import 'package:stream_transform/stream_transform.dart';

part 'shipment_event.dart';
part 'shipment_state.dart';

class ShipmentBloc extends HydratedBloc<ShipmentEvent, ShipmentsState> {
  ShipmentBloc({
    required this.metaDataRepository,
    required this.connectivityBloc,
    required this.shipmentRepository,
  }) : super(const ShipmentsState()) {
    on<LoadShipmentCounts>(_onLoadShipmentsCounts);
    on<ShipmentsFetched>(_onShipmentFetched, transformer: (events, mapper) {
      return events.debounce(const Duration(seconds: 1)).asyncExpand(mapper);
    });
    on<ShipmentsSetStateInitail>(
      (event, emit) {
        emit(
          state.copyWith(
            status: ShipmentDataLoadingStatus.initial,
            shipments: [],
            hasReachedMax: false,
            shipmentsFilter: state.shipmentsFilter.copyWith(page: 1),
          ),
        );
      },
    );
    on<ShipmentsFilterChanged>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: ShipmentDataLoadingStatus.initial,
            shipments: [],
            hasReachedMax: false,
            shipmentsFilter: event.shipmentsFilter,
          ),
        );
      },
    );
  }

  final MetaDataRepository metaDataRepository;
  final ShipmentsRepository shipmentRepository;
  final ConnectivityBloc connectivityBloc;
  final DioClient dioClient = DioClient();

  Future<void> _onLoadShipmentsCounts(LoadShipmentCounts event, Emitter<ShipmentsState> emit) async {
    emit(
      state.copyWith(
        countsLoading: state.countsInitialLoaded == false ? true : false,
        countsInitialLoaded: true,
      ),
    );
    if (connectivityBloc.state.result != ConnectivityResult.none) {
      await Future.wait([
        _loadCounts(event, emit),
      ]);
    }
    emit(
      state.copyWith(countsLoading: false),
    );
  }

  Future<void> _loadCounts(LoadShipmentCounts event, Emitter<ShipmentsState> emit) async {
    final Map<String, dynamic>? response = await metaDataRepository.getCounts(key: 'containers');
    if (response != null) {
      return emit(state.copyWith(
        all: response['all'] == null ? 0 : int.parse((response['all'] ?? "0").toString()),
        atLoading: response['at_loading'],
        onTheWay: response['on_the_way'],
        arrived: response['arrived'],
      ));
    }
  }

  Future<void> _onShipmentFetched(ShipmentsFetched event, Emitter<ShipmentsState> emit) async {
    if (state.hasReachedMax) return;
    try {
      if (state.status == ShipmentDataLoadingStatus.initial) {
        final shipment = await shipmentRepository.getShipments(state.shipmentsFilter);
        return emit(
          state.copyWith(
            status: ShipmentDataLoadingStatus.success,
            shipments: shipment,
            hasReachedMax: shipment.length < state.shipmentsFilter.perPage,
            shipmentsFilter: state.shipmentsFilter.copyWith(
              page: state.shipmentsFilter.page + 1,
            ),
          ),
        );
      }
      final shipment = await shipmentRepository.getShipments(state.shipmentsFilter);
      emit(
        state.copyWith(
          status: ShipmentDataLoadingStatus.success,
          shipments: List.of(state.shipments)..addAll(shipment),
          hasReachedMax: shipment.length < state.shipmentsFilter.perPage,
          shipmentsFilter: state.shipmentsFilter.copyWith(
            page: state.shipmentsFilter.page + 1,
          ),
        ),
      );
    } catch (_) {
      emit(state.copyWith(status: ShipmentDataLoadingStatus.failure));
    }
  }

  @override
  ShipmentsState? fromJson(Map<String, dynamic> json) {
    ShipmentsState st = ShipmentsState(
      all: json['all'],
      arrived: json['arrived'],
      atLoading: json['at_loading'],
      atTheDock: json['at_the_dock'],
      checked: json['checked'],
      clearance: json['clearance'],
      finalChecked: json['final_checked'],
      pending: json['pending'],
    );
    return st;
  }

  @override
  Map<String, dynamic>? toJson(ShipmentsState state) {
    Map<String, dynamic> map = {
      'all': state.all,
      'arrived': state.arrived,
      'at_loading': state.atLoading,
      'at_the_dock': state.atTheDock,
      'checked': state.checked,
      'clearance': state.clearance,
      'final_checked': state.finalChecked,
      'pending': state.pending,
    };
    return map;
  }
}
