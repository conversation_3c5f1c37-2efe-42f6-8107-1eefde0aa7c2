part of 'shipment_bloc.dart';

sealed class ShipmentEvent extends Equatable {
  const ShipmentEvent();

  @override
  List<Object> get props => [];
}

final class LoadShipmentCounts extends ShipmentEvent {}

final class ShipmentsFetched extends ShipmentEvent {}

final class ShipmentsSetStateInitail extends ShipmentEvent {}

final class ShipmentsFilterChanged extends ShipmentEvent {
  const ShipmentsFilterChanged({required this.shipmentsFilter});

  final ShipmentsFilter shipmentsFilter;
}
