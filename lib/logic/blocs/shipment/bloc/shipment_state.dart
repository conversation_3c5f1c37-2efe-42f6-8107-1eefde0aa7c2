part of 'shipment_bloc.dart';

enum ShipmentDataLoadingStatus { initial, success, failure }

final class ShipmentsState extends Equatable {
  const ShipmentsState({
    this.countsInitialLoaded = false,
    this.countsLoading = false,
    this.all = 0,
    this.pending = 0,
    this.atLoading = 0,
    this.atTheDock = 0,
    this.checked = 0,
    this.finalChecked = 0,
    this.onTheWay = 0,
    this.arrived = 0,
    this.clearance = 0,
    // shipments list data
    this.status = ShipmentDataLoadingStatus.initial,
    this.shipments = const <Shipment>[],
    this.hasReachedMax = false,
    this.shipmentsFilter = const ShipmentsFilter(),
  });

  final bool countsInitialLoaded;
  final bool countsLoading;
  final int all;
  final int pending;
  final int atLoading;
  final int atTheDock;
  final int checked;
  final int finalChecked;
  final int onTheWay;
  final int arrived;
  final int clearance;

  // shipments list data
  // doesn't need to be hydrated
  final ShipmentDataLoadingStatus status;
  final List<Shipment> shipments;
  final bool hasReachedMax;
  final ShipmentsFilter shipmentsFilter;

  int getShipmentTotal() {
    return (atLoading) + (onTheWay) + (checked) + (finalChecked) + (arrived) + (atTheDock) + (clearance);
  }

  ShipmentsState copyWith({
    bool? countsInitialLoaded,
    bool? countsLoading,
    int? all,
    int? pending,
    int? atLoading,
    int? atTheDock,
    int? checked,
    int? finalChecked,
    int? onTheWay,
    int? arrived,
    int? clearance,
    // shipments list data
    ShipmentDataLoadingStatus? status,
    List<Shipment>? shipments,
    bool? hasReachedMax,
    ShipmentsFilter? shipmentsFilter,
  }) {
    return ShipmentsState(
      countsInitialLoaded: countsInitialLoaded ?? this.countsInitialLoaded,
      countsLoading: countsLoading ?? this.countsLoading,
      all: all ?? this.all,
      pending: pending ?? this.pending,
      atLoading: atLoading ?? this.atLoading,
      atTheDock: atTheDock ?? this.atTheDock,
      checked: checked ?? this.checked,
      finalChecked: finalChecked ?? this.finalChecked,
      onTheWay: onTheWay ?? this.onTheWay,
      arrived: arrived ?? this.arrived,
      clearance: clearance ?? this.clearance,
      status: status ?? this.status,
      shipments: shipments ?? this.shipments,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      shipmentsFilter: shipmentsFilter ?? this.shipmentsFilter,
    );
  }

  @override
  List<Object> get props => [
        countsInitialLoaded,
        countsLoading,
        all,
        pending,
        atLoading,
        atTheDock,
        checked,
        finalChecked,
        onTheWay,
        arrived,
        clearance,
        // shipments list data
        status,
        shipments,
        hasReachedMax,
        shipmentsFilter,
      ];
}
