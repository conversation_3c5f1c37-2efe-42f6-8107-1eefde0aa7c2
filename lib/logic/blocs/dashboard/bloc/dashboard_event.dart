part of 'dashboard_bloc.dart';

sealed class DashboardEvent extends Equatable {
  const DashboardEvent();

  @override
  List<Object> get props => [];
}

final class DashboardDataLoad extends DashboardEvent {}

final class DashboardDataSetStateInitaial extends DashboardEvent {}

final class LoadVehicleSummary extends DashboardEvent {}

final class LoadShipmentSummary extends DashboardEvent {}

final class VehicleSummaryCategoryChanged extends DashboardEvent {
  final String category;

  const VehicleSummaryCategoryChanged({required this.category});
}

final class ShipmentSummaryCategoryChanged extends DashboardEvent {
  final String category;

  const ShipmentSummaryCategoryChanged({required this.category});
}

final class AnnouncementCountChanged extends DashboardEvent {
  final int countAnnouncements;

  const AnnouncementCountChanged({required this.countAnnouncements});
}

final class NotificationCountChanged extends DashboardEvent {
  final int countNotifications;

  const NotificationCountChanged({required this.countNotifications});
}

final class ShippingRatesNotificationsCountChanged extends DashboardEvent {
  final int countShippingRates;

  const ShippingRatesNotificationsCountChanged({required this.countShippingRates});
}

final class ShippingRatesUnreadCountChanged extends DashboardEvent {
  final int countShippingRates;

  const ShippingRatesUnreadCountChanged({required this.countShippingRates});
}

final class ArrivalNoticesNotificationsCountChanged extends DashboardEvent {
  final int countArrivalNotices;

  const ArrivalNoticesNotificationsCountChanged({required this.countArrivalNotices});
}

final class NotificationCountIncreement extends DashboardEvent {}

final class AnnouncementCountIncreement extends DashboardEvent {}
