part of 'dashboard_bloc.dart';

class DashboardState extends Equatable {
  const DashboardState({
    this.initialLoaded = false, // set to false on load from hydrated_state
    this.loading = false,
    this.countVehicles = 0,
    this.countShipments = 0,
    this.countInvoices = 0,
    this.countMixShipping = 0,
    this.countTransactions = 0,
    this.countAnnouncements = 0,
    this.countNotificationsAll = 0,
    this.countArrivalNoticesNotifications = 0,
    this.countShippingtRatesNotifications = 0,
    this.countAnnouncementsNotifications = 0,
    this.countAllShippingtRates = 0,
    this.countUnreadShippingtRates = 0,
    this.countAllPayments = 0,
    this.countApprovedPayments = 0,
    this.countPendingPayments = 0,
    this.countAuctionPayments = 0,
    this.invoicePaymentStatus = const InvoicePaymentStatus(
      all: 0,
      due: 0,
      paid: 0,
      allInvoice: 0,
      paidInvoice: 0,
      dueInvoice: 0,
      allMixshipping: 0,
      paidMixshipping: 0,
      dueMixshipping: 0,
    ),
    this.vehicleSummaryItems = const [],
    this.vehicleSummaryCategory = 'total',
    this.vehicleSummaryLoading = false,
    this.shipmentSummaryItems = const [],
    this.shipmentSummaryCategory = 'total',
    this.shipmentSummaryLoading = false,
  });

  final bool loading;
  final bool initialLoaded;
  final int countVehicles;
  final int countShipments;
  final int countInvoices;
  final int countMixShipping;
  final int countTransactions;
  final int countAnnouncements;
  //notifcations count
  final int countNotificationsAll;
  final int countArrivalNoticesNotifications;
  final int countShippingtRatesNotifications;
  final int countAnnouncementsNotifications;
  // shipping rates count
  final int countUnreadShippingtRates;
  final int countAllShippingtRates;

  // payments count
  final int countAllPayments;
  final int countApprovedPayments;
  final int countPendingPayments;
  final int countAuctionPayments;

  final InvoicePaymentStatus invoicePaymentStatus;

  final List<VehicleSummaryItem> vehicleSummaryItems;
  final String vehicleSummaryCategory;
  final bool vehicleSummaryLoading;

  final List<ShipmentSummaryItem> shipmentSummaryItems;
  final String shipmentSummaryCategory;
  final bool shipmentSummaryLoading;

  num getSelectedVehicleCategoryTotal() {
    num total = 0;
    for (var item in vehicleSummaryItems) {
      total += item.toMap()[vehicleSummaryCategory];
    }
    return total;
  }

  num getSelectedShipmentCategoryTotal() {
    num total = 0;
    for (var item in shipmentSummaryItems) {
      total += item.toMap()[shipmentSummaryCategory];
    }
    return total;
  }

  DashboardState copyWith({
    bool? loading,
    bool? initialLoaded,
    int? countVehicles,
    int? countShipments,
    int? countInvoices,
    int? countMixShipping,
    int? countTransactions,
    int? countAnnouncements,
    //notifcations count
    int? countNotificationsAll,
    int? countArrivalNoticesNotifications,
    int? countShippingtRatesNotifications,
    int? countAnnouncementsNotifications,
    // shipping rates count
    int? countAllShippingtRates,
    int? countUnreadShippingtRates,
    // payments count
    int? countAllPayments,
    int? countApprovedPayments,
    int? countPendingPayments,
    int? countAuctionPayments,
    InvoicePaymentStatus? invoicePaymentStatus,
    List<VehicleSummaryItem>? vehicleSummaryitems,
    String? vehicleSummaryCategory,
    bool? vehicleSummaryLoading,
    List<ShipmentSummaryItem>? shipmentSummaryitems,
    String? shipmentSummaryCategory,
    bool? shipmentSummaryLoading,
  }) {
    return DashboardState(
      loading: loading ?? this.loading,
      initialLoaded: initialLoaded ?? this.initialLoaded,
      countVehicles: countVehicles ?? this.countVehicles,
      countShipments: countShipments ?? this.countShipments,
      countInvoices: countInvoices ?? this.countInvoices,
      countMixShipping: countMixShipping ?? this.countMixShipping,
      countTransactions: countTransactions ?? this.countTransactions,
      countAnnouncements: countAnnouncements ?? this.countAnnouncements,
      //notifcations count
      countNotificationsAll: countNotificationsAll ?? this.countNotificationsAll,
      countArrivalNoticesNotifications: countArrivalNoticesNotifications ?? this.countArrivalNoticesNotifications,
      countShippingtRatesNotifications: countShippingtRatesNotifications ?? this.countShippingtRatesNotifications,
      countAnnouncementsNotifications: countAnnouncementsNotifications ?? this.countAnnouncementsNotifications,
      // shipping rates count
      countAllShippingtRates: countAllShippingtRates ?? this.countAllShippingtRates,
      countUnreadShippingtRates: countUnreadShippingtRates ?? this.countUnreadShippingtRates,
      // payments count
      countAllPayments: countAllPayments ?? this.countAllPayments,
      countApprovedPayments: countApprovedPayments ?? this.countApprovedPayments,
      countPendingPayments: countPendingPayments ?? this.countPendingPayments,
      countAuctionPayments: countAuctionPayments ?? this.countAuctionPayments,
      invoicePaymentStatus: invoicePaymentStatus ?? this.invoicePaymentStatus,
      vehicleSummaryItems: vehicleSummaryitems ?? vehicleSummaryItems,
      vehicleSummaryCategory: vehicleSummaryCategory ?? this.vehicleSummaryCategory,
      vehicleSummaryLoading: vehicleSummaryLoading ?? this.vehicleSummaryLoading,
      shipmentSummaryItems: shipmentSummaryitems ?? shipmentSummaryItems,
      shipmentSummaryCategory: shipmentSummaryCategory ?? this.shipmentSummaryCategory,
      shipmentSummaryLoading: shipmentSummaryLoading ?? this.shipmentSummaryLoading,
    );
  }

  @override
  List<Object> get props => [
        loading,
        initialLoaded,
        countVehicles,
        countShipments,
        countInvoices,
        countMixShipping,
        countTransactions,
        countAnnouncements,
        countNotificationsAll,
        countArrivalNoticesNotifications,
        countShippingtRatesNotifications,
        countAnnouncementsNotifications,
        countAllShippingtRates,
        countUnreadShippingtRates,
        countAllPayments,
        countApprovedPayments,
        countPendingPayments,
        countAuctionPayments,
        invoicePaymentStatus,
        vehicleSummaryItems,
        vehicleSummaryCategory,
        vehicleSummaryLoading,
        shipmentSummaryItems,
        shipmentSummaryCategory,
        shipmentSummaryLoading
      ];
}
