import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:invoices_repository/invoices_repository.dart';
import 'package:meta_data_repository/meta_data_repository.dart';
import 'package:pgl_mobile_app/exports/blocs.exports.dart';
import 'package:pgl_mobile_app/logic/models/shipment/shipment_summary_item.modal.dart';
import 'package:vehicles_repository/vehicles_repository.dart';

part 'dashboard_event.dart';
part 'dashboard_state.dart';

class DashboardBloc extends HydratedBloc<DashboardEvent, DashboardState> {
  DashboardBloc({
    required this.metaDataRepository,
    required this.connectivityBloc,
    required this.context,
  }) : super(const DashboardState()) {
    on<DashboardDataLoad>(_onLoadDashboardData);
    on<DashboardDataSetStateInitaial>(_onDashboardDataSetInitaial);
    on<LoadVehicleSummary>(_onLoadVehicleSummary);
    on<LoadShipmentSummary>(_onLoadShipmentSummary);
    on<ShippingRatesUnreadCountChanged>((event, emit) {
      return emit(state.copyWith(
        countUnreadShippingtRates: event.countShippingRates,
      ));
    });
    on<AnnouncementCountChanged>((event, emit) {
      return emit(state.copyWith(
        countAnnouncements: event.countAnnouncements,
      ));
    });
    on<NotificationCountChanged>((event, emit) {
      return emit(state.copyWith(
        countNotificationsAll: event.countNotifications,
      ));
    });
    on<ShippingRatesNotificationsCountChanged>((event, emit) {
      return emit(state.copyWith(
        countShippingtRatesNotifications: event.countShippingRates,
      ));
    });

    on<ArrivalNoticesNotificationsCountChanged>((event, emit) {
      return emit(state.copyWith(
        countArrivalNoticesNotifications: event.countArrivalNotices,
      ));
    });
    on<NotificationCountIncreement>((event, emit) {
      return emit(state.copyWith(
        countNotificationsAll: state.countNotificationsAll + 1,
      ));
    });
    on<AnnouncementCountIncreement>((event, emit) {
      return emit(state.copyWith(
        countAnnouncements: state.countAnnouncements + 1,
      ));
    });
    on<VehicleSummaryCategoryChanged>((event, emit) {
      return emit(state.copyWith(
        vehicleSummaryCategory: event.category,
      ));
    });
    on<ShipmentSummaryCategoryChanged>((event, emit) {
      return emit(state.copyWith(
        shipmentSummaryCategory: event.category,
      ));
    });
  }

  final MetaDataRepository metaDataRepository;
  final ConnectivityBloc connectivityBloc;
  final BuildContext context;

  Future<void> _onLoadDashboardData(DashboardDataLoad event, Emitter<DashboardState> emit) async {
    emit(state.copyWith(
      loading: state.initialLoaded == false ? true : false,
      initialLoaded: true,
    ));
    if (connectivityBloc.state.result != ConnectivityResult.none) {
      await Future.wait([
        _loadCounts(event, emit),
        _loadPaymentStatus(event, emit),
      ]);
    }
    emit(state.copyWith(loading: false));
  }

  Future<void> _onDashboardDataSetInitaial(DashboardDataSetStateInitaial event, Emitter<DashboardState> emit) async {
    emit(const DashboardState());
  }

  Future<void> _loadCounts(DashboardDataLoad event, Emitter<DashboardState> emit) async {
    final Map<String, dynamic>? response = await metaDataRepository.getCounts(key: 'all');
    if (response != null) {
      return emit(
        state.copyWith(
          countVehicles: response['vehicles'],
          countShipments: response['containers'],
          countInvoices: response['invoices'],
          countMixShipping: response['mix_shipping'],
          countTransactions: response['transactions'],
          countAnnouncements: response['announcements'],
          countNotificationsAll: response['notifications']['all'],
          countShippingtRatesNotifications: response['notifications']['shipping_rate'] ?? 0,
          countArrivalNoticesNotifications: response['notifications']['arrival_notice'] ?? 0,
          countAnnouncementsNotifications: response['notifications']['announcement'] ?? 0,
          countAllShippingtRates: response['shipping_rates_all'] ?? 0,
          countUnreadShippingtRates: response['shipping_rates'] ?? 0,
          countAllPayments: response['customer_payment_transactions'] ?? 0,
          countApprovedPayments: response['customer_payment_transactions_approved'] ?? 0,
          countPendingPayments: response['customer_payment_transactions_pending'] ?? 0,
          countAuctionPayments: response['customer_payment_transactions_auction'] ?? 0,
        ),
      );
    }
  }

  Future<void> _loadPaymentStatus(DashboardDataLoad event, Emitter<DashboardState> emit) async {
    try {
      List<Map<String, dynamic>?> results = await Future.wait<Map<String, dynamic>?>([
        metaDataRepository.getPaymentStatus(),
        metaDataRepository.getInvoicePaymentStatus(),
        metaDataRepository.getMixShippingPaymentStatus()
      ]);

      Map<String, dynamic>? response = results[0];

      Map<String, dynamic>? response2 = results[1];
      Map<String, dynamic>? response3 = results[2];
      response!.addAll(response2!.map((key, value) => MapEntry('invoice_$key', value)));
      response.addAll(response3!.map((key, value) => MapEntry('mix_shipping_$key', value)));

      return emit(state.copyWith(
        invoicePaymentStatus: InvoicePaymentStatus.fromMap(response),
      ));
    } catch (e) {
      return emit(state.copyWith(
        invoicePaymentStatus: null,
      ));
    }
  }

  // vehicle summary
  Future<void> _onLoadVehicleSummary(LoadVehicleSummary event, Emitter<DashboardState> emit) async {
    emit(state.copyWith(
      vehicleSummaryLoading: true,
    ));
    if (connectivityBloc.state.result != ConnectivityResult.none) {
      await Future.wait([
        _loadVehicleSummary(event, emit),
      ]);
    }
    emit(state.copyWith(
      vehicleSummaryLoading: false,
    ));
  }

  Future<void> _loadVehicleSummary(LoadVehicleSummary event, Emitter<DashboardState> emit) async {
    final List<dynamic>? response = await metaDataRepository.getVehicleSummary();
    if (response != null) {
      return emit(state.copyWith(
        vehicleSummaryitems: response.map<VehicleSummaryItem>((e) => VehicleSummaryItem.fromMap(e)).toList(),
      ));
    }
  }

  // shipment summary
  Future<void>? _onLoadShipmentSummary(LoadShipmentSummary event, Emitter<DashboardState> emit) async {
    emit(state.copyWith(
      shipmentSummaryLoading: true,
    ));
    if (connectivityBloc.state.result != ConnectivityResult.none) {
      await Future.wait([
        _loadShipmentSummary(event, emit),
      ]);
    }
    emit(state.copyWith(
      shipmentSummaryLoading: false,
    ));
  }

  Future<void> _loadShipmentSummary(LoadShipmentSummary event, Emitter<DashboardState> emit) async {
    final List<dynamic>? response = await metaDataRepository.getShipmentSummary();
    if (response != null) {
      return emit(state.copyWith(
        shipmentSummaryitems: response.map<ShipmentSummaryItem>((e) => ShipmentSummaryItem.fromMap(e)).toList(),
      ));
    }
  }

  @override
  DashboardState? fromJson(Map<String, dynamic> json) {
    DashboardState st = DashboardState(
      initialLoaded: false,
      countVehicles: json['count_vehicles'],
      countShipments: json['count_shipments'],
      countInvoices: json['count_invoices'],
      countMixShipping: json['count_mix_shippings'],
      countAnnouncements: json['count_announcements'],
      countNotificationsAll: json['count_notifications'],
      invoicePaymentStatus: InvoicePaymentStatus.fromJson(json['invoice_payment_status']),
      vehicleSummaryItems: json['vehicle_summary_items'] != null
          ? json['vehicle_summary_items']
              .map<VehicleSummaryItem>(
                (item) => VehicleSummaryItem.fromJson(item),
              )
              .toList()
          : [],
      shipmentSummaryItems: json['shipment_summary_items'] != null
          ? json['shipment_summary_items']
              .map<ShipmentSummaryItem>(
                (item) => ShipmentSummaryItem.fromJson(item),
              )
              .toList()
          : [],
    );
    return st;
  }

  @override
  Map<String, dynamic>? toJson(DashboardState state) {
    Map<String, dynamic> map = {
      'count_vehicles': state.countVehicles,
      'count_shipments': state.countShipments,
      'count_invoices': state.countInvoices,
      'count_mix_shippings': state.countMixShipping,
      'count_announcements': state.countAnnouncements,
      'count_notifications': state.countNotificationsAll,
      'invoice_payment_status': state.invoicePaymentStatus.toJson(),
      'vehicle_summary_items':
          state.vehicleSummaryItems.isNotEmpty ? state.vehicleSummaryItems.map((item) => item.toJson()).toList() : [],
      'shipment_summary_items':
          state.shipmentSummaryItems.isNotEmpty ? state.shipmentSummaryItems.map((item) => item.toJson()).toList() : [],
    };
    return map;
  }
}
