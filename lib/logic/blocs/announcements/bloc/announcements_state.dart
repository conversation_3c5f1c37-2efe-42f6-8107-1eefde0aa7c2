part of 'announcements_bloc.dart';

enum AnnouncementsDataLoadingStatus { initial, success, failure }

final class AnnouncementsState extends Equatable {
  const AnnouncementsState({
    this.announcementCount = 0,
    // announcements list data
    this.status = AnnouncementsDataLoadingStatus.initial,
    this.announcements = const <Announcement>[],
    this.hasReachedMax = false,
    this.announcementsFilter = const AnnouncementsFilter(),
  });
  final int announcementCount;
  // announcements list data
  final AnnouncementsDataLoadingStatus status;
  final List<Announcement> announcements;
  final bool hasReachedMax;
  final AnnouncementsFilter announcementsFilter;

  AnnouncementsState copyWith({
    int? announcementCount,
    // announcements list data
    AnnouncementsDataLoadingStatus? status,
    List<Announcement>? announcements,
    bool? hasReachedMax,
    AnnouncementsFilter? announcementsFilter,
  }) {
    return AnnouncementsState(
      announcementCount: announcementCount ?? this.announcementCount,
      // announcements list data
      status: status ?? this.status,
      announcements: announcements ?? this.announcements,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      announcementsFilter: announcementsFilter ?? this.announcementsFilter,
    );
  }

  @override
  List<Object> get props => [
        announcementCount,
        // announcements list data
        status,
        announcements,
        hasReachedMax,
        announcementsFilter,
      ];
}
