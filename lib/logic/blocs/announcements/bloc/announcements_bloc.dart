import 'package:announcements_repository/announcements_repository.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio_client/dio_client.dart';
import 'package:equatable/equatable.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:pgl_mobile_app/logic/blocs/connectivity/bloc/connectivity_bloc.dart';
import 'package:pgl_mobile_app/logic/blocs/dashboard/bloc/dashboard_bloc.dart';
import 'package:stream_transform/stream_transform.dart';

part 'announcements_event.dart';
part 'announcements_state.dart';

class AnnouncementsBloc extends HydratedBloc<AnnouncementsEvent, AnnouncementsState> {
  AnnouncementsBloc({
    required this.announcementsRepository,
    required this.connectivityBloc,
    required this.dashboardBloc,
  }) : super(const AnnouncementsState()) {
    on<AnnouncementsFetched>(_onAnnouncementFetched, transformer: (events, mapper) {
      return events.debounce(const Duration(seconds: 1)).asyncExpand(mapper);
    });
    on<AnnouncementsCountChanged>(
      (event, emit) {
        return emit(
          state.copyWith(
            announcementCount: event.announcementCount,
          ),
        );
      },
    );
    on<AnnouncementsSetStateInitail>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: AnnouncementsDataLoadingStatus.initial,
            announcements: [],
            hasReachedMax: false,
            announcementsFilter: state.announcementsFilter.copyWith(
              page: 1,
            ),
          ),
        );
      },
    );
    on<AnnouncementsFilterChanged>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: AnnouncementsDataLoadingStatus.initial,
            announcements: [],
            hasReachedMax: false,
            announcementsFilter: event.announcementsFilter,
          ),
        );
      },
    );
    on<AnnouncementsMarkAllAsRead>(_onAnnouncementsMarkAllAsRead);
    on<AnnouncementReceived>(
      (event, emit) {
        return emit(
          state.copyWith(
            announcements: [event.announcement, ...state.announcements],
          ),
        );
      },
    );
  }

  final AnnouncementsRepository announcementsRepository;
  final ConnectivityBloc connectivityBloc;
  final DashboardBloc dashboardBloc;
  final DioClient dioClient = DioClient();

  // announcements list data
  Future<void> _onAnnouncementFetched(AnnouncementsFetched event, emit) async {
    if (state.hasReachedMax) return;
    if (connectivityBloc.state.result == ConnectivityResult.none) return;
    try {
      if (state.status == AnnouncementsDataLoadingStatus.initial) {
        final List<Announcement> announcements =
            await announcementsRepository.getAnnouncement(state.announcementsFilter);
        return emit(
          state.copyWith(
            status: AnnouncementsDataLoadingStatus.success,
            announcements: announcements,
            hasReachedMax: announcements.length < state.announcementsFilter.perPage,
            announcementsFilter: state.announcementsFilter.copyWith(
              page: state.announcementsFilter.page + 1,
            ),
          ),
        );
      }
      final announcements = await announcementsRepository.getAnnouncement(state.announcementsFilter);
      emit(
        state.copyWith(
          status: AnnouncementsDataLoadingStatus.success,
          announcements: List.of(state.announcements)..addAll(announcements),
          hasReachedMax: announcements.length < state.announcementsFilter.perPage,
          announcementsFilter: state.announcementsFilter.copyWith(
            page: state.announcementsFilter.page + 1,
          ),
        ),
      );
    } catch (_) {
      emit(state.copyWith(status: AnnouncementsDataLoadingStatus.failure));
    }
  }

  Future<void> _onAnnouncementsMarkAllAsRead(AnnouncementsMarkAllAsRead event, emit) async {
    if (connectivityBloc.state.result == ConnectivityResult.none) return;
    try {
      final bool response = await announcementsRepository.markAllAsRead();
      if (response) {
        dashboardBloc.add(
          const AnnouncementCountChanged(countAnnouncements: 0),
        );
        emit(
          state.copyWith(
            announcements: state.announcements
                .map<Announcement>(
                  (item) => item.copyWith(
                    seenAt: item.seenAt ?? DateTime.now(),
                  ),
                )
                .toList(),
          ),
        );
      }
    } catch (_) {}
  }

  @override
  AnnouncementsState? fromJson(Map<String, dynamic> json) {
    AnnouncementsState st = AnnouncementsState(
      announcements: json['announcements'].map<Announcement>((item) => Announcement.fromMap(item)).toList(),
    );
    return st;
  }

  @override
  Map<String, dynamic>? toJson(AnnouncementsState state) {
    Map<String, dynamic> map = {
      'announcements': state.announcements.map((e) => e.toMap()).toList(),
    };
    return map;
  }
}
