part of 'announcements_bloc.dart';

sealed class AnnouncementsEvent extends Equatable {
  const AnnouncementsEvent();

  @override
  List<Object> get props => [];
}

final class AnnouncementsFetched extends AnnouncementsEvent {}

final class AnnouncementsMarkAllAsRead extends AnnouncementsEvent {}

final class AnnouncementsSetStateInitail extends AnnouncementsEvent {}

final class AnnouncementReceived extends AnnouncementsEvent {
  const AnnouncementReceived({
    required this.announcement,
  });

  final Announcement announcement;
}

final class AnnouncementsCountChanged extends AnnouncementsEvent {
  const AnnouncementsCountChanged({required this.announcementCount});

  final int announcementCount;
}

final class AnnouncementsFilterChanged extends AnnouncementsEvent {
  const AnnouncementsFilterChanged({required this.announcementsFilter});

  final AnnouncementsFilter announcementsFilter;
}
