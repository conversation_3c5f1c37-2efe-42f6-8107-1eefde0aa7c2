part of 'global_search_bloc.dart';

enum GlobalSearchLoadingStatus { initial, success, failure }

class GlobalSearchState extends Equatable {
  const GlobalSearchState({
    this.status = GlobalSearchLoadingStatus.initial,
    this.items = const <Object>[],
    this.hasReachedMax = false,
    this.globalSearchFilter = const GlobalSearchFilter(),
  });

  final GlobalSearchLoadingStatus status;
  final List<Object> items;
  final bool hasReachedMax;
  final GlobalSearchFilter globalSearchFilter;

  GlobalSearchState copyWith({
    GlobalSearchLoadingStatus? status,
    List<Object>? items,
    bool? hasReachedMax,
    GlobalSearchFilter? globalSearchFilter,
  }) {
    return GlobalSearchState(
      status: status ?? this.status,
      items: items ?? this.items,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      globalSearchFilter: globalSearchFilter ?? this.globalSearchFilter,
    );
  }

  @override
  List<Object> get props => [
        status,
        items,
        hasReachedMax,
        globalSearchFilter,
      ];
}
