import 'package:bloc/bloc.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:equatable/equatable.dart';
import 'package:invoices_repository/invoices_repository.dart';
import 'package:mix_shipping_repository/mix_shipping_repository.dart';
import 'package:pgl_mobile_app/logic/blocs/connectivity/bloc/connectivity_bloc.dart';
import 'package:pgl_mobile_app/logic/blocs/global_search/models/global_search_filter.dart';
import 'package:shipments_repository/shipments_repository.dart';
import 'package:vehicles_repository/vehicles_repository.dart';
import 'package:stream_transform/stream_transform.dart';

part 'global_search_event.dart';
part 'global_search_state.dart';

class GlobalSearchBloc extends Bloc<GlobalSearchEvent, GlobalSearchState> {
  GlobalSearchBloc({
    required this.vehiclesRepository,
    required this.shipmentsRepository,
    required this.invoicesRepository,
    required this.mixshippingRepository,
    required this.connectivityBloc,
  }) : super(const GlobalSearchState()) {
    on<GlobalSearchFilterChanged>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: GlobalSearchLoadingStatus.initial,
            items: [],
            hasReachedMax: false,
            globalSearchFilter: event.globalSearchFilter,
          ),
        );
      },
    );
    on<ItemsFetched>(_onItemsFetched, transformer: (events, mapper) {
      return events.debounce(const Duration(seconds: 1)).asyncExpand(mapper);
    });
    on<GlobalSearchSetStateInitail>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: GlobalSearchLoadingStatus.initial,
            items: [],
            hasReachedMax: false,
            globalSearchFilter: state.globalSearchFilter.copyWith(
              page: 1,
            ),
          ),
        );
      },
    );
  }

  final VehiclesRepository vehiclesRepository;
  final ShipmentsRepository shipmentsRepository;
  final InvoicesRepository invoicesRepository;
  final MixshippingRepository mixshippingRepository;
  final ConnectivityBloc connectivityBloc;

  Future<void> _onItemsFetched(ItemsFetched event, emit) async {
    if (state.hasReachedMax) return;
    if (connectivityBloc.state.result == ConnectivityResult.none) return;
    try {
      List<List<dynamic>> results = await Future.wait([
        vehiclesRepository.getVehicles(
          VehiclesFilter(
            search: state.globalSearchFilter.search,
            page: state.globalSearchFilter.page,
            perPage: 8,
          ),
        ),
        shipmentsRepository.getShipments(
          ShipmentsFilter(
            search: state.globalSearchFilter.search,
            page: state.globalSearchFilter.page,
            perPage: 4,
          ),
        ),
        invoicesRepository.getInvoices(
          InvoicesFilter(
            search: state.globalSearchFilter.search,
            page: state.globalSearchFilter.page,
            perPage: 4,
          ),
        ),
        mixshippingRepository.getMixshipping(
          MixShippingFilter(
            search: state.globalSearchFilter.search,
            page: state.globalSearchFilter.page,
            perPage: 4,
          ),
        )
      ]);
      final vehicles = results[0];
      final shipments = results[1];
      final invoices = results[2];
      final mixShipping = results[3];
      List<Object> items = [...shipments, ...vehicles, ...invoices, ...mixShipping];
      if (state.status == GlobalSearchLoadingStatus.initial) {
        return emit(
          state.copyWith(
            status: GlobalSearchLoadingStatus.success,
            items: items,
            hasReachedMax: items.isEmpty,
            globalSearchFilter: state.globalSearchFilter.copyWith(
              page: state.globalSearchFilter.page + 1,
            ),
          ),
        );
      } else {
        emit(
          state.copyWith(
            status: GlobalSearchLoadingStatus.success,
            items: List.of(state.items)..addAll(items),
            hasReachedMax: items.isEmpty,
            globalSearchFilter: state.globalSearchFilter.copyWith(
              page: state.globalSearchFilter.page + 1,
            ),
          ),
        );
      }
    } catch (_) {
      emit(state.copyWith(status: GlobalSearchLoadingStatus.failure));
    }
  }
}
