part of 'global_search_bloc.dart';

sealed class GlobalSearchEvent extends Equatable {
  const GlobalSearchEvent();

  @override
  List<Object> get props => [];
}

final class ItemsFetched extends GlobalSearchEvent {}

final class GlobalSearchSetStateInitail extends GlobalSearchEvent {}

final class GlobalSearchFilterChanged extends GlobalSearchEvent {
  const GlobalSearchFilterChanged({required this.globalSearchFilter});

  final GlobalSearchFilter globalSearchFilter;
}
