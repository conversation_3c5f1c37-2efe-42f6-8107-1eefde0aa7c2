import 'dart:convert';

import 'package:equatable/equatable.dart';

class GlobalSearchFilter extends Equatable {
  final int page;
  final int perPage;
  final String search;

  const GlobalSearchFilter({
    this.page = 1,
    this.perPage = 5,
    this.search = '',
  });

  GlobalSearchFilter copyWith({
    int? page,
    int? perPage,
    String? search,
  }) {
    return GlobalSearchFilter(
      page: page ?? this.page,
      perPage: perPage ?? this.perPage,
      search: search ?? this.search,
    );
  }

  factory GlobalSearchFilter.fromMap(Map<String, dynamic> map) {
    return GlobalSearchFilter(
      page: map['page'] ?? 1,
      perPage: map['per_page'] ?? 1,
      search: map['search'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'page': page,
      'per_page': perPage,
      'search': search,
    };
  }

  factory GlobalSearchFilter.fromJson(String json) => GlobalSearchFilter.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        page,
        perPage,
        search,
      ];
}
