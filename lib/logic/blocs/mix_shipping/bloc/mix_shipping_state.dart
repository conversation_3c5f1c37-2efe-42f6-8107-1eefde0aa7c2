part of 'mix_shipping_bloc.dart';

enum MixShippingDataLoadingStatus { initial, success, failure }

final class MixShippingState extends Equatable {
  const MixShippingState({
    this.countsInitialLoaded = false,
    this.countsLoading = false,
    this.openCount = 0,
    this.paidCount = 0,
    this.pastDueCount = 0,
    // mixShipping list data
    this.status = MixShippingDataLoadingStatus.initial,
    this.mixShipping = const <MixShipping>[],
    this.hasReachedMax = false,
    this.mixShippingFilter = const MixShippingFilter(),
  });

  final bool countsInitialLoaded;
  final bool countsLoading;
  final int openCount;
  final int paidCount;
  final int pastDueCount;

  // mixShipping list data
  // doesn't need to be hydrated
  final MixShippingDataLoadingStatus status;
  final List<MixShipping> mixShipping;
  final bool hasReachedMax;
  final MixShippingFilter mixShippingFilter;

  int getallMixShippingTotal() {
    return openCount + paidCount + pastDueCount;
  }

  MixShippingState copyWith({
    bool? countsInitialLoaded,
    bool? countsLoading,
    int? openCount,
    int? paidCount,
    int? pastDueCount,
    // mixShipping list data
    MixShippingDataLoadingStatus? status,
    List<MixShipping>? mixShipping,
    bool? hasReachedMax,
    MixShippingFilter? mixShippingFilter,
  }) {
    return MixShippingState(
      countsInitialLoaded: countsInitialLoaded ?? this.countsInitialLoaded,
      countsLoading: countsLoading ?? this.countsLoading,
      openCount: openCount ?? this.openCount,
      paidCount: paidCount ?? this.paidCount,
      pastDueCount: pastDueCount ?? this.pastDueCount,
      // mixShipping list data

      status: status ?? this.status,
      mixShipping: mixShipping ?? this.mixShipping,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      mixShippingFilter: mixShippingFilter ?? this.mixShippingFilter,
    );
  }

  @override
  List<Object> get props => [
        countsInitialLoaded,
        countsLoading,
        openCount,
        paidCount,
        pastDueCount,

        // mixShipping list data
        status,
        mixShipping,
        hasReachedMax,
        mixShippingFilter,
      ];
}
