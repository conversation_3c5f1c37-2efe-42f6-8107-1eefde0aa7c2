import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio_client/dio_client.dart';
import 'package:equatable/equatable.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:meta_data_repository/meta_data_repository.dart';
import 'package:mix_shipping_repository/mix_shipping_repository.dart';
import 'package:pgl_mobile_app/logic/blocs/connectivity/bloc/connectivity_bloc.dart';
import 'package:stream_transform/stream_transform.dart';

part 'mix_shipping_event.dart';
part 'mix_shipping_state.dart';

class MixShippingBloc extends HydratedBloc<MixShippingEvent, MixShippingState> {
  MixShippingBloc({
    required this.metaDataRepository,
    required this.mixShippingRepository,
    required this.connectivityBloc,
  }) : super(const MixShippingState()) {
    on<LoadMixShippingCounts>(_onLoadMixShippingCounts);
    on<MixShippingFetched>(_onMixShippingFetched, transformer: (events, mapper) {
      return events.debounce(const Duration(seconds: 1)).asyncExpand(mapper);
    });
    on<MixShippingSetStateInitail>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: MixShippingDataLoadingStatus.initial,
            mixShipping: [],
            hasReachedMax: false,
            mixShippingFilter: state.mixShippingFilter.copyWith(
              page: 1,
            ),
          ),
        );
      },
    );
    on<MixShippingFilterChanged>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: MixShippingDataLoadingStatus.initial,
            mixShipping: [],
            hasReachedMax: false,
            mixShippingFilter: event.mixShippingFilter,
          ),
        );
      },
    );
  }

  final MetaDataRepository metaDataRepository;
  final MixshippingRepository mixShippingRepository;
  final ConnectivityBloc connectivityBloc;
  final DioClient dioClient = DioClient();

  Future<void> _onLoadMixShippingCounts(LoadMixShippingCounts event, Emitter<MixShippingState> emit) async {
    emit(
      state.copyWith(
        countsLoading: state.countsInitialLoaded == false ? true : false,
        countsInitialLoaded: true,
      ),
    );
    if (connectivityBloc.state.result != ConnectivityResult.none) {
      await Future.wait([
        _loadCounts(event, emit),
      ]);
    }
    emit(
      state.copyWith(countsLoading: false),
    );
  }

  Future<void> _loadCounts(LoadMixShippingCounts event, Emitter<MixShippingState> emit) async {
    final Map<String, dynamic>? response = await metaDataRepository.getCounts(key: 'mix_shipping');
    if (response != null) {
      return emit(state.copyWith(
        openCount: response['mix_shipping_open'],
        paidCount: response['mix_shipping_paid'],
        pastDueCount: response['mix_shipping_past_due'],
      ));
    }
  }

  // mixShipping list data
  Future<void> _onMixShippingFetched(MixShippingFetched event, Emitter<MixShippingState> emit) async {
    if (state.hasReachedMax) return;
    if (connectivityBloc.state.result == ConnectivityResult.none) return;
    try {
      if (state.status == MixShippingDataLoadingStatus.initial) {
        final List<MixShipping> mixShipping = await mixShippingRepository.getMixshipping(state.mixShippingFilter);
        return emit(
          state.copyWith(
            status: MixShippingDataLoadingStatus.success,
            mixShipping: mixShipping,
            hasReachedMax: mixShipping.length < state.mixShippingFilter.perPage,
            mixShippingFilter: state.mixShippingFilter.copyWith(
              page: state.mixShippingFilter.page + 1,
            ),
          ),
        );
      }
      final mixShipping = await mixShippingRepository.getMixshipping(state.mixShippingFilter);
      emit(
        state.copyWith(
          status: MixShippingDataLoadingStatus.success,
          mixShipping: List.of(state.mixShipping)..addAll(mixShipping),
          hasReachedMax: mixShipping.length < state.mixShippingFilter.perPage,
          mixShippingFilter: state.mixShippingFilter.copyWith(
            page: state.mixShippingFilter.page + 1,
          ),
        ),
      );
    } catch (_) {
      emit(state.copyWith(status: MixShippingDataLoadingStatus.failure));
    }
  }

  @override
  MixShippingState? fromJson(Map<String, dynamic> json) {
    MixShippingState st = MixShippingState(
      openCount: json['mix_shipping_open'],
      paidCount: json['mix_shipping_paid'],
      pastDueCount: json['mix_shipping_past_due'],
    );
    return st;
  }

  @override
  Map<String, dynamic>? toJson(MixShippingState state) {
    Map<String, dynamic> map = {
      'mix_shipping_open': state.openCount,
      'mix_shipping_paid': state.paidCount,
      'mix_shipping_past_due': state.pastDueCount,
    };
    return map;
  }
}
