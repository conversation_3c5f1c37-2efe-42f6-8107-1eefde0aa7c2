part of 'mix_shipping_bloc.dart';

sealed class MixShippingEvent extends Equatable {
  const MixShippingEvent();

  @override
  List<Object> get props => [];
}

final class LoadMixShippingCounts extends MixShippingEvent {}

final class MixShippingFetched extends MixShippingEvent {}

final class MixShippingSetStateInitail extends MixShippingEvent {}

final class MixShippingFilterChanged extends MixShippingEvent {
  const MixShippingFilterChanged({required this.mixShippingFilter});

  final MixShippingFilter mixShippingFilter;
}
