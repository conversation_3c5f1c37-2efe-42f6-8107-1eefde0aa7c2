import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio_client/dio_client.dart';
import 'package:equatable/equatable.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:invoices_repository/invoices_repository.dart';
import 'package:meta_data_repository/meta_data_repository.dart';
import 'package:pgl_mobile_app/logic/blocs/connectivity/bloc/connectivity_bloc.dart';
import 'package:stream_transform/stream_transform.dart';

part 'invoices_event.dart';
part 'invoices_state.dart';

class InvoicesBloc extends HydratedBloc<InvoicesEvent, InvoicesState> {
  InvoicesBloc({
    required this.metaDataRepository,
    required this.invoicesRepository,
    required this.connectivityBloc,
  }) : super(const InvoicesState()) {
    on<LoadInvoicesCounts>(_onLoadInvoicesCounts);
    on<AddInvoicePayments>(_onAddInvoicePayments);
    on<InvoicesFetched>(_onInvoicesFetched, transformer: (events, mapper) {
      return events.debounce(const Duration(seconds: 1)).asyncExpand(mapper);
    });
    on<InvoicesSetStateInitail>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: InvoicesDataLoadingStatus.initial,
            invoices: [],
            hasReachedMax: false,
            invoicesFilter: state.invoicesFilter.copyWith(
              page: 1,
            ),
          ),
        );
      },
    );
    on<InvoicesFilterChanged>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: InvoicesDataLoadingStatus.initial,
            invoices: [],
            hasReachedMax: false,
            invoicesFilter: event.invoicesFilter,
          ),
        );
      },
    );
  }

  final MetaDataRepository metaDataRepository;
  final InvoicesRepository invoicesRepository;
  final ConnectivityBloc connectivityBloc;
  final DioClient dioClient = DioClient();

  Future<void> _onLoadInvoicesCounts(LoadInvoicesCounts event, Emitter<InvoicesState> emit) async {
    emit(
      state.copyWith(
        countsLoading: state.countsInitialLoaded == false ? true : false,
        countsInitialLoaded: true,
      ),
    );
    if (connectivityBloc.state.result != ConnectivityResult.none) {
      await Future.wait([
        _loadCounts(event, emit),
      ]);
    }
    emit(
      state.copyWith(countsLoading: false),
    );
  }

  Future<void> _loadCounts(LoadInvoicesCounts event, Emitter<InvoicesState> emit) async {
    final Map<String, dynamic>? response = await metaDataRepository.getCounts(key: 'invoices');
    if (response != null) {
      return emit(state.copyWith(
        openCount: response['invoice_open'],
        paidCount: response['invoice_paid'],
        pastDueCount: response['invoice_past_due'],
      ));
    }
  }

  // invoices list data
  Future<void> _onInvoicesFetched(InvoicesFetched event, Emitter<InvoicesState> emit) async {
    if (state.hasReachedMax) return;
    if (connectivityBloc.state.result == ConnectivityResult.none) return;
    try {
      if (state.status == InvoicesDataLoadingStatus.initial) {
        final List<Invoice> invoices = await invoicesRepository.getInvoices(state.invoicesFilter);
        return emit(
          state.copyWith(
            status: InvoicesDataLoadingStatus.success,
            invoices: invoices,
            hasReachedMax: invoices.length < state.invoicesFilter.perPage,
            invoicesFilter: state.invoicesFilter.copyWith(
              page: state.invoicesFilter.page + 1,
            ),
          ),
        );
      }
      final invoices = await invoicesRepository.getInvoices(state.invoicesFilter);
      emit(
        state.copyWith(
          status: InvoicesDataLoadingStatus.success,
          invoices: List.of(state.invoices)..addAll(invoices),
          hasReachedMax: invoices.length < state.invoicesFilter.perPage,
          invoicesFilter: state.invoicesFilter.copyWith(
            page: state.invoicesFilter.page + 1,
          ),
        ),
      );
    } catch (_) {
      emit(state.copyWith(status: InvoicesDataLoadingStatus.failure));
    }
  }

  Future<void> _onAddInvoicePayments(AddInvoicePayments event, Emitter<InvoicesState> emit) async {
    if (event.invoice.payments.isNotEmpty) return;
    if (connectivityBloc.state.result == ConnectivityResult.none) return;
    try {
      return emit(
        state.copyWith(
          invoices: state.invoices.map<Invoice>(
            (Invoice p) {
              if (p.id == event.invoice.id) {
                return event.invoice;
              }
              return p;
            },
          ).toList(),
        ),
      );
    } catch (_) {}
  }

  @override
  InvoicesState? fromJson(Map<String, dynamic> json) {
    InvoicesState st = InvoicesState(
      openCount: json['invoice_open'],
      paidCount: json['invoice_paid'],
      pastDueCount: json['invoice_past_due'],
    );
    return st;
  }

  @override
  Map<String, dynamic>? toJson(InvoicesState state) {
    Map<String, dynamic> map = {
      'invoice_open': state.openCount,
      'invoice_paid': state.paidCount,
      'invoice_past_due': state.pastDueCount,
    };
    return map;
  }
}
