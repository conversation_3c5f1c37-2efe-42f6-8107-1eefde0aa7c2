part of 'invoices_bloc.dart';

sealed class InvoicesEvent extends Equatable {
  const InvoicesEvent();

  @override
  List<Object> get props => [];
}

final class LoadInvoicesCounts extends InvoicesEvent {}

final class InvoicesFetched extends InvoicesEvent {}

final class InvoicesSetStateInitail extends InvoicesEvent {}

final class InvoicesFilterChanged extends InvoicesEvent {
  const InvoicesFilterChanged({required this.invoicesFilter});

  final InvoicesFilter invoicesFilter;
}

final class AddInvoicePayments extends InvoicesEvent {
  const AddInvoicePayments({required this.invoice});

  final Invoice invoice;
}
