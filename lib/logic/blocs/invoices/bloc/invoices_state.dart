part of 'invoices_bloc.dart';

enum InvoicesDataLoadingStatus { initial, success, failure }

final class InvoicesState extends Equatable {
  const InvoicesState({
    this.countsInitialLoaded = false,
    this.countsLoading = false,
    this.openCount = 0,
    this.paidCount = 0,
    this.pastDueCount = 0,
    // invoices list data
    this.status = InvoicesDataLoadingStatus.initial,
    this.invoices = const <Invoice>[],
    this.hasReachedMax = false,
    this.invoicesFilter = const InvoicesFilter(),
  });

  final bool countsInitialLoaded;
  final bool countsLoading;
  final int openCount;
  final int paidCount;
  final int pastDueCount;

  // invoices list data
  // doesn't need to be hydrated
  final InvoicesDataLoadingStatus status;
  final List<Invoice> invoices;
  final bool hasReachedMax;
  final InvoicesFilter invoicesFilter;

  int getAllInvoicesTotal() {
    return openCount + paidCount + pastDueCount;
  }

  InvoicesState copyWith({
    bool? countsInitialLoaded,
    bool? countsLoading,
    int? openCount,
    int? paidCount,
    int? pastDueCount,
    // invoices list data
    InvoicesDataLoadingStatus? status,
    List<Invoice>? invoices,
    bool? hasReachedMax,
    InvoicesFilter? invoicesFilter,
  }) {
    return InvoicesState(
      countsInitialLoaded: countsInitialLoaded ?? this.countsInitialLoaded,
      countsLoading: countsLoading ?? this.countsLoading,
      openCount: openCount ?? this.openCount,
      paidCount: paidCount ?? this.paidCount,
      pastDueCount: pastDueCount ?? this.pastDueCount,
      // invoices list data
      status: status ?? this.status,
      invoices: invoices ?? this.invoices,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      invoicesFilter: invoicesFilter ?? this.invoicesFilter,
    );
  }

  @override
  List<Object> get props => [
        countsInitialLoaded,
        countsLoading,
        openCount,
        paidCount,
        pastDueCount,

        // invoices list data
        status,
        invoices,
        hasReachedMax,
        invoicesFilter,
      ];
}
