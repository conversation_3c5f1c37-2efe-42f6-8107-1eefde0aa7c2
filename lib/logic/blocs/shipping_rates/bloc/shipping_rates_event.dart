part of 'shipping_rates_bloc.dart';

sealed class ShippingRatesEvent extends Equatable {
  const ShippingRatesEvent();

  @override
  List<Object> get props => [];
}

final class ShippingRatesFetched extends ShippingRatesEvent {}

final class ShippingRatesSetStateInitail extends ShippingRatesEvent {}

final class ShippingRatesMarkAllAsRead extends ShippingRatesEvent {}

final class ShippingRatesFilterChanged extends ShippingRatesEvent {
  const ShippingRatesFilterChanged({required this.shippingRatesFilter});

  final ShippingRatesFilter shippingRatesFilter;
}
