import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio_client/dio_client.dart';
import 'package:equatable/equatable.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:pgl_mobile_app/logic/blocs/connectivity/bloc/connectivity_bloc.dart';
import 'package:shipping_rates_repository/shipping_rates_repository.dart';
import 'package:stream_transform/stream_transform.dart';

part 'shipping_rates_event.dart';
part 'shipping_rates_state.dart';

class ShippingRatesBloc extends HydratedBloc<ShippingRatesEvent, ShippingRatesState> {
  ShippingRatesBloc({
    required this.shippingRatesRepository,
    required this.connectivityBloc,
  }) : super(const ShippingRatesState()) {
    on<ShippingRatesFetched>(_onShippingRatesFetched, transformer: (events, mapper) {
      return events.debounce(const Duration(seconds: 1)).asyncExpand(mapper);
    });
    on<ShippingRatesSetStateInitail>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: ShippingRatesDataLoadingStatus.initial,
            shippingRates: [],
            hasReachedMax: false,
            shippingRatesFilter: state.shippingRatesFilter.copyWith(
              page: 1,
            ),
          ),
        );
      },
    );
    on<ShippingRatesFilterChanged>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: ShippingRatesDataLoadingStatus.initial,
            shippingRates: [],
            hasReachedMax: false,
            shippingRatesFilter: event.shippingRatesFilter,
          ),
        );
      },
    );
    on<ShippingRatesMarkAllAsRead>(
      (event, emit) async {
        await shippingRatesRepository.markAllAsSeen();
      },
    );
  }

  final ShippingRatesRepository shippingRatesRepository;
  final ConnectivityBloc connectivityBloc;
  final DioClient dioClient = DioClient();

  Future<void> _onShippingRatesFetched(ShippingRatesFetched event, emit) async {
    if (state.hasReachedMax) return;
    if (connectivityBloc.state.result == ConnectivityResult.none) return;
    try {
      if (state.status == ShippingRatesDataLoadingStatus.initial) {
        final Map<String, dynamic> shippingRatesResponse =
            await shippingRatesRepository.getShippingRates(state.shippingRatesFilter);
        final List<ShippingRate> shippingRates = shippingRatesResponse['items'];
        return emit(
          state.copyWith(
            status: ShippingRatesDataLoadingStatus.success,
            shippingRates: shippingRates,
            hasReachedMax: shippingRates.length < state.shippingRatesFilter.perPage,
            shippingRatesFilter: state.shippingRatesFilter.copyWith(
              page: state.shippingRatesFilter.page + 1,
            ),
            note: shippingRatesResponse['note'] ?? '',
          ),
        );
      }
      final Map<String, dynamic> shippingRatesResponse =
          await shippingRatesRepository.getShippingRates(state.shippingRatesFilter);
      final List<ShippingRate> shippingRates = shippingRatesResponse['items'];
      emit(
        state.copyWith(
          status: ShippingRatesDataLoadingStatus.success,
          shippingRates: List.of(state.shippingRates)..addAll(shippingRates),
          hasReachedMax: shippingRates.length < state.shippingRatesFilter.perPage,
          shippingRatesFilter: state.shippingRatesFilter.copyWith(
            page: state.shippingRatesFilter.page + 1,
          ),
          note: shippingRatesResponse['note'] ?? '',
        ),
      );
    } catch (_) {
      emit(state.copyWith(status: ShippingRatesDataLoadingStatus.failure));
    }
  }

  @override
  ShippingRatesState? fromJson(Map<String, dynamic> json) {
    ShippingRatesState st = const ShippingRatesState();
    return st;
  }

  @override
  Map<String, dynamic>? toJson(ShippingRatesState state) {
    Map<String, dynamic> map = {};
    return map;
  }
}
