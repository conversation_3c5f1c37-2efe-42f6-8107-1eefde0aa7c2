part of 'shipping_rates_bloc.dart';

enum ShippingRatesDataLoadingStatus { initial, success, failure }

final class ShippingRatesState extends Equatable {
  const ShippingRatesState({
    this.status = ShippingRatesDataLoadingStatus.initial,
    this.shippingRates = const <ShippingRate>[],
    this.hasReachedMax = false,
    this.shippingRatesFilter = const ShippingRatesFilter(),
    this.note = '',
  });

  // shippingRates list data
  // doesn't need to be hydrated
  final ShippingRatesDataLoadingStatus status;
  final List<ShippingRate> shippingRates;
  final bool hasReachedMax;
  final ShippingRatesFilter shippingRatesFilter;
  final String note;

  ShippingRatesState copyWith({
    ShippingRatesDataLoadingStatus? status,
    List<ShippingRate>? shippingRates,
    bool? hasReachedMax,
    ShippingRatesFilter? shippingRatesFilter,
    String? note,
  }) {
    return ShippingRatesState(
      status: status ?? this.status,
      shippingRates: shippingRates ?? this.shippingRates,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      shippingRatesFilter: shippingRatesFilter ?? this.shippingRatesFilter,
      note: note ?? this.note,
    );
  }

  @override
  List<Object> get props => [
        status,
        shippingRates,
        hasReachedMax,
        shippingRatesFilter,
        note,
      ];
}
