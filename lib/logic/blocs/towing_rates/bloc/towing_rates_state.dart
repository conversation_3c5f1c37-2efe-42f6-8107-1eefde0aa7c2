part of 'towing_rates_bloc.dart';

enum TowingRatesDataLoadingStatus { initial, success, failure }

final class TowingRatesState extends Equatable {
  const TowingRatesState({
    this.status = TowingRatesDataLoadingStatus.initial,
    this.towingRates = const <TowingRate>[],
    this.hasReachedMax = false,
    this.towingRatesFilter = const TowingRatesFilter(),
    this.note = '',
  });

  // shippingRates list data
  // doesn't need to be hydrated
  final TowingRatesDataLoadingStatus status;
  final List<TowingRate> towingRates;
  final bool hasReachedMax;
  final TowingRatesFilter towingRatesFilter;
  final String note;

  TowingRatesState copyWith({
    TowingRatesDataLoadingStatus? status,
    List<TowingRate>? towingRates,
    bool? hasReachedMax,
    TowingRatesFilter? towingRatesFilter,
    String? note,
  }) {
    return TowingRatesState(
      status: status ?? this.status,
      towingRates: towingRates ?? this.towingRates,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      towingRatesFilter: towingRatesFilter ?? this.towingRatesFilter,
      note: note ?? this.note,
    );
  }

  @override
  List<Object> get props => [
        status,
        towingRates,
        hasReachedMax,
        towingRatesFilter,
        note,
      ];
}
