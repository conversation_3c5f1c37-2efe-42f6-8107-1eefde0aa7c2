part of 'towing_rates_bloc.dart';

sealed class TowingRatesEvent extends Equatable {
  const TowingRatesEvent();

  @override
  List<Object> get props => [];
}

final class TowingRatesFetched extends TowingRatesEvent {}

final class TowingRatesSetStateInitail extends TowingRatesEvent {}

final class TowingRatesFilterChanged extends TowingRatesEvent {
  const TowingRatesFilterChanged({required this.towingRatesFilter});

  final TowingRatesFilter towingRatesFilter;
}
