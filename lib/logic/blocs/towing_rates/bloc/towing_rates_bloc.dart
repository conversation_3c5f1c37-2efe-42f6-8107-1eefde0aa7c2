import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio_client/dio_client.dart';
import 'package:equatable/equatable.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:pgl_mobile_app/logic/blocs/connectivity/bloc/connectivity_bloc.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:towing_rates_repository/towing_rates_repository.dart';

part 'towing_rates_event.dart';
part 'towing_rates_state.dart';

class TowingRatesBloc extends HydratedBloc<TowingRatesEvent, TowingRatesState> {
  TowingRatesBloc({
    required this.towingRatesRepository,
    required this.connectivityBloc,
  }) : super(const TowingRatesState()) {
    on<TowingRatesFetched>(_onTowingRatesFetched, transformer: (events, mapper) {
      return events.debounce(const Duration(seconds: 1)).asyncExpand(mapper);
    });
    on<TowingRatesSetStateInitail>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: TowingRatesDataLoadingStatus.initial,
            towingRates: [],
            hasReachedMax: false,
            towingRatesFilter: state.towingRatesFilter.copyWith(
              page: 1,
            ),
          ),
        );
      },
    );
    on<TowingRatesFilterChanged>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: TowingRatesDataLoadingStatus.initial,
            towingRates: [],
            hasReachedMax: false,
            towingRatesFilter: event.towingRatesFilter,
          ),
        );
      },
    );
  }

  final TowingRatesRepository towingRatesRepository;
  final ConnectivityBloc connectivityBloc;
  final DioClient dioClient = DioClient();

  Future<void> _onTowingRatesFetched(TowingRatesFetched event, emit) async {
    if (state.hasReachedMax) return;
    if (connectivityBloc.state.result == ConnectivityResult.none) return;
    try {
      if (state.status == TowingRatesDataLoadingStatus.initial) {
        final List<TowingRate> towingRates = await towingRatesRepository.getTowingRates(state.towingRatesFilter);
        return emit(
          state.copyWith(
            status: TowingRatesDataLoadingStatus.success,
            towingRates: towingRates,
            hasReachedMax: towingRates.length < state.towingRatesFilter.perPage,
            towingRatesFilter: state.towingRatesFilter.copyWith(
              page: state.towingRatesFilter.page + 1,
            ),
          ),
        );
      }
      final List<TowingRate> towingRates = await towingRatesRepository.getTowingRates(state.towingRatesFilter);
      emit(
        state.copyWith(
          status: TowingRatesDataLoadingStatus.success,
          towingRates: List.of(state.towingRates)..addAll(towingRates),
          hasReachedMax: towingRates.length < state.towingRatesFilter.perPage,
          towingRatesFilter: state.towingRatesFilter.copyWith(
            page: state.towingRatesFilter.page + 1,
          ),
        ),
      );
    } catch (_) {
      emit(state.copyWith(status: TowingRatesDataLoadingStatus.failure));
    }
  }

  @override
  TowingRatesState? fromJson(Map<String, dynamic> json) {
    TowingRatesState st = const TowingRatesState();
    return st;
  }

  @override
  Map<String, dynamic>? toJson(TowingRatesState state) {
    Map<String, dynamic> map = {};
    return map;
  }
}
