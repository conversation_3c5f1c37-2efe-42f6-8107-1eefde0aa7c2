part of 'auction_payments_bloc.dart';

enum AuctionPaymentsDataLoadingStatus { initial, success, failure }

final class AuctionPaymentsState extends Equatable {
  const AuctionPaymentsState({
    // Auction Payments list data
    this.status = AuctionPaymentsDataLoadingStatus.initial,
    this.auctionPayments = const <AuctionPaymentModel>[],
    this.hasReachedMax = false,
    this.auctionPaymentsFilter = const AuctionPaymentsFilter(),
  });

  // Auction Payments list data
  // doesn't need to be hydrated
  final AuctionPaymentsDataLoadingStatus status;
  final List<AuctionPaymentModel> auctionPayments;
  final bool hasReachedMax;
  final AuctionPaymentsFilter auctionPaymentsFilter;

  AuctionPaymentsState copyWith({
    // invoices list data
    AuctionPaymentsDataLoadingStatus? status,
    List<AuctionPaymentModel>? auctionPayments,
    bool? hasReachedMax,
    AuctionPaymentsFilter? auctionPaymentsFilter,
  }) {
    return AuctionPaymentsState(
      // invoices list data
      status: status ?? this.status,
      auctionPayments: auctionPayments ?? this.auctionPayments,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      auctionPaymentsFilter: auctionPaymentsFilter ?? this.auctionPaymentsFilter,
    );
  }

  @override
  List<Object> get props => [
        // invoices list data
        status,
        auctionPayments,
        hasReachedMax,
        auctionPaymentsFilter,
      ];
}
