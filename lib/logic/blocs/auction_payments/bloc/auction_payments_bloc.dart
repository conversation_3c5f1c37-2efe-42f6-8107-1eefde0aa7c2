import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio_client/dio_client.dart';
import 'package:equatable/equatable.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/logic/blocs/connectivity/bloc/connectivity_bloc.dart';
import 'package:stream_transform/stream_transform.dart';

part 'auction_payments_event.dart';
part 'auction_payments_state.dart';

class AuctionPaymentsBloc extends HydratedBloc<AuctionPaymentsEvent, AuctionPaymentsState> {
  AuctionPaymentsBloc({
    required this.auctionPaymentsRepository,
    required this.connectivityBloc,
  }) : super(const AuctionPaymentsState()) {
    on<AuctionPaymentsFetched>(_onInvoicesFetched, transformer: (events, mapper) {
      return events.debounce(const Duration(seconds: 1)).asyncExpand(mapper);
    });
    on<AuctionPaymentsSetStateInitail>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: AuctionPaymentsDataLoadingStatus.initial,
            auctionPayments: [],
            hasReachedMax: false,
            auctionPaymentsFilter: state.auctionPaymentsFilter.copyWith(
              page: 1,
            ),
          ),
        );
      },
    );
    on<AuctionPaymentsFilterChanged>(
      (event, emit) {
        return emit(
          state.copyWith(
            status: AuctionPaymentsDataLoadingStatus.initial,
            auctionPayments: [],
            hasReachedMax: false,
            auctionPaymentsFilter: event.auctionPaymentsFilter,
          ),
        );
      },
    );
  }

  final AuctionPaymentsRepository auctionPaymentsRepository;
  final ConnectivityBloc connectivityBloc;
  final DioClient dioClient = DioClient();

  // invoices list data
  Future<void> _onInvoicesFetched(AuctionPaymentsFetched event, emit) async {
    if (state.hasReachedMax) return;
    if (connectivityBloc.state.result == ConnectivityResult.none) return;
    try {
      if (state.status == AuctionPaymentsDataLoadingStatus.initial) {
        final List<AuctionPaymentModel> auctionPayments =
            await auctionPaymentsRepository.getPayments(state.auctionPaymentsFilter);
        return emit(
          state.copyWith(
            status: AuctionPaymentsDataLoadingStatus.success,
            auctionPayments: auctionPayments,
            hasReachedMax: auctionPayments.length < state.auctionPaymentsFilter.perPage,
            auctionPaymentsFilter: state.auctionPaymentsFilter.copyWith(
              page: state.auctionPaymentsFilter.page + 1,
            ),
          ),
        );
      }
      final List<AuctionPaymentModel> auctionPayments =
          await auctionPaymentsRepository.getPayments(state.auctionPaymentsFilter);
      emit(
        state.copyWith(
          status: AuctionPaymentsDataLoadingStatus.success,
          auctionPayments: List.of(state.auctionPayments)..addAll(auctionPayments),
          hasReachedMax: auctionPayments.length < state.auctionPaymentsFilter.perPage,
          auctionPaymentsFilter: state.auctionPaymentsFilter.copyWith(
            page: state.auctionPaymentsFilter.page + 1,
          ),
        ),
      );
    } catch (_) {
      emit(state.copyWith(status: AuctionPaymentsDataLoadingStatus.failure));
    }
  }

  @override
  AuctionPaymentsState? fromJson(Map<String, dynamic> json) {
    AuctionPaymentsState st = AuctionPaymentsState(
      auctionPayments: json['payments'].map<AuctionPaymentModel>((item) => AuctionPaymentModel.fromMap(item)).toList(),
    );
    return st;
  }

  @override
  Map<String, dynamic>? toJson(AuctionPaymentsState state) {
    Map<String, dynamic> map = {
      'payments': state.auctionPayments.map((e) => e.toMap()).toList(),
    };
    return map;
  }
}
