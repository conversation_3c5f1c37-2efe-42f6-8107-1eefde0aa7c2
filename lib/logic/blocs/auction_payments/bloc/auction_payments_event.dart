part of 'auction_payments_bloc.dart';

sealed class AuctionPaymentsEvent extends Equatable {
  const AuctionPaymentsEvent();

  @override
  List<Object> get props => [];
}

final class AuctionPaymentsFetched extends AuctionPaymentsEvent {}

final class AuctionPaymentsSetStateInitail extends AuctionPaymentsEvent {}

final class AuctionPaymentsFilterChanged extends AuctionPaymentsEvent {
  const AuctionPaymentsFilterChanged({required this.auctionPaymentsFilter});

  final AuctionPaymentsFilter auctionPaymentsFilter;
}
