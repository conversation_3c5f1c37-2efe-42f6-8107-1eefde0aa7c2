part of 'authentication_bloc.dart';

class AuthenticationState extends Equatable {
  const AuthenticationState._({
    this.status = AuthenticationStatus.unknown,
    this.error = AuthenticationError.none,
    this.user = User.empty,
  });

  const AuthenticationState.unknown() : this._();

  const AuthenticationState.authenticated(User user)
      : this._(
          status: AuthenticationStatus.authenticated,
          user: user,
        );

  const AuthenticationState.unauthenticated()
      : this._(
          status: AuthenticationStatus.unauthenticated,
        );

  final AuthenticationStatus status;
  final AuthenticationError error;
  final User user;

  AuthenticationState copyWith({
    AuthenticationStatus? status,
    AuthenticationError? error,
    User? user,
  }) {
    return AuthenticationState._(
      status: status ?? this.status,
      error: error ?? this.error,
      user: user ?? this.user,
    );
  }

  @override
  List<Object> get props => [status, error, user];
}
