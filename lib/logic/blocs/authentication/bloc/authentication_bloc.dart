import 'dart:async';

import 'package:authentication_repository/authentication_repository.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:equatable/equatable.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:pgl_mobile_app/logic/blocs/connectivity/bloc/connectivity_bloc.dart';
import 'package:user_repository/user_repository.dart';

part 'authentication_event.dart';
part 'authentication_state.dart';

class AuthenticationBloc extends HydratedBloc<AuthenticationEvent, AuthenticationState> {
  AuthenticationBloc({
    required AuthenticationRepository authenticationRepository,
    required UserRepository userRepository,
    required ConnectivityBloc connectivityBloc,
    required Function onLogout,
  })  : _authenticationRepository = authenticationRepository,
        _userRepository = userRepository,
        _connectivityBloc = connectivityBloc,
        _onLogout = onLogout,
        super(
          const AuthenticationState.unknown(),
        ) {
    on<_AuthenticationStatusChanged>(_onAuthenticationStatusChanged);
    on<_AuthenticationConnectivityChanged>(_onAuthenticationConnectivityChanged);
    on<AuthenticationErrorChanged>(_onAuthenticationErrorChanged);
    on<AuthenticationUserChanged>(_onAuthenticationUserChanged);
    on<AuthenticationLogoutRequested>(_onAuthenticationLogoutRequested);
    _authenticationStatusSubscription = _authenticationRepository.status.listen(
      (status) => add(_AuthenticationStatusChanged(status)),
    );
    _authenticationErrorSubscription = _authenticationRepository.error.listen(
      (error) => add(AuthenticationErrorChanged(error)),
    );
    _connectivityState = _connectivityBloc.stream.listen((ConnectivityState state) async {
      add(_AuthenticationConnectivityChanged(state.result));
    });
  }

  final AuthenticationRepository _authenticationRepository;
  final UserRepository _userRepository;
  final ConnectivityBloc _connectivityBloc;
  final Function _onLogout;

  late StreamSubscription<AuthenticationStatus> _authenticationStatusSubscription;
  late StreamSubscription<AuthenticationError> _authenticationErrorSubscription;
  late StreamSubscription<ConnectivityState> _connectivityState;
  @override
  Future<void> close() {
    _authenticationStatusSubscription.cancel();
    _authenticationErrorSubscription.cancel();
    _connectivityState.cancel();
    return super.close();
  }

  Future<void> _onAuthenticationStatusChanged(
    _AuthenticationStatusChanged event,
    Emitter<AuthenticationState> emit,
  ) async {
    switch (event.status) {
      case AuthenticationStatus.unauthenticated:
        return emit(const AuthenticationState.unauthenticated());
      case AuthenticationStatus.authenticated:
        final user = await _tryGetUser();
        return emit(
          user != null ? AuthenticationState.authenticated(user) : const AuthenticationState.unauthenticated(),
        );
      case AuthenticationStatus.unknown:
        return emit(const AuthenticationState.unknown());
    }
  }

  Future<void> _onAuthenticationConnectivityChanged(
    _AuthenticationConnectivityChanged event,
    Emitter<AuthenticationState> emit,
  ) async {
    if (event.connectivityResult != ConnectivityResult.none && state.status == AuthenticationStatus.authenticated) {
      final user = await _tryGetUser();
      return emit(
        user != null ? AuthenticationState.authenticated(user) : const AuthenticationState.unauthenticated(),
      );
    }
  }

  Future<void> _onAuthenticationErrorChanged(
    AuthenticationErrorChanged event,
    Emitter<AuthenticationState> emit,
  ) async {
    return emit(state.copyWith(error: event.error));
  }

  Future<void> _onAuthenticationUserChanged(
    AuthenticationUserChanged event,
    Emitter<AuthenticationState> emit,
  ) async {
    return emit(
      state.copyWith(
        user: event.user,
      ),
    );
  }

  void _onAuthenticationLogoutRequested(
    AuthenticationLogoutRequested event,
    Emitter<AuthenticationState> emit,
  ) {
    _authenticationRepository.logOut();
    _onLogout();
  }

  Future<User?> _tryGetUser() async {
    try {
      final user = await _userRepository.getUser();
      return user;
    } catch (_) {
      return null;
    }
  }

  @override
  AuthenticationState? fromJson(Map<String, dynamic> json) {
    AuthenticationState st = AuthenticationState._(
      status: AuthenticationStatus.fromJson(json['state']),
      user: User.fromJson(json['user']),
    );
    return st;
  }

  @override
  Map<String, dynamic>? toJson(AuthenticationState state) {
    return {
      'state': state.status.toJson(),
      'user': state.user.toJson(),
    };
  }
}
