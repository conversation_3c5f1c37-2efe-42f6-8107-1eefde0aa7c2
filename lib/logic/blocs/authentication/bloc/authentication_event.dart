part of 'authentication_bloc.dart';

sealed class AuthenticationEvent extends Equatable {
  const AuthenticationEvent();

  @override
  List<Object> get props => [];
}

final class _AuthenticationStatusChanged extends AuthenticationEvent {
  const _AuthenticationStatusChanged(this.status);

  final AuthenticationStatus status;
}

final class _AuthenticationConnectivityChanged extends AuthenticationEvent {
  const _AuthenticationConnectivityChanged(this.connectivityResult);
  final ConnectivityResult connectivityResult;
}

final class AuthenticationErrorChanged extends AuthenticationEvent {
  const AuthenticationErrorChanged(this.error);

  final AuthenticationError error;
}

final class AuthenticationLogoutRequested extends AuthenticationEvent {}

final class AuthenticationUserChanged extends AuthenticationEvent {
  const AuthenticationUserChanged({required this.user});

  final User user;
}
