import 'dart:convert';

import 'package:equatable/equatable.dart';

class ShipmentSummaryItem extends Equatable {
  // info
  final int locationId;
  final String locationName;
  final int atLoading;
  final int onTheWay;
  final int arrived;
  final int total;

  const ShipmentSummaryItem({
    // info
    required this.locationId,
    required this.locationName,
    required this.atLoading,
    required this.onTheWay,
    required this.arrived,
    required this.total,
  });

  factory ShipmentSummaryItem.fromMap(Map<String, dynamic> map) {
    return ShipmentSummaryItem(
      locationId: map['location_id'] ?? 0,
      locationName: map['location_name'] ?? '',
      atLoading: map['at_loading'] ?? 0,
      onTheWay: map['on_the_way'] ?? 0,
      arrived: map['arrived'] ?? 0,
      total: map['total'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'location_id': locationId,
      'location_name': locationName,
      'at_loading': atLoading,
      'on_the_way': onTheWay,
      'arrived': arrived,
      'total': total,
    };
  }

  factory ShipmentSummaryItem.fromJson(String json) => ShipmentSummaryItem.fromMap(jsonDecode(json));

  String toJson() => jsonEncode(toMap());

  @override
  List<Object?> get props => [
        locationId,
        locationName,
        onTheWay,
        atLoading,
        arrived,
        total,
      ];
}
