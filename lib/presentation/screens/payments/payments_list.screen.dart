import 'dart:async';

import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:payments_repository/payments_repository.dart';
import 'package:pgl_mobile_app/exports/blocs.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/payments/payment_item.card.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/payments/payment_item.card.skeleton.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_search_bar.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/no_items_found.widget.dart';

class PaymentsListScreen extends StatefulWidget {
  const PaymentsListScreen({super.key});

  @override
  State<PaymentsListScreen> createState() => _PaymentsListScreenState();
}

class _PaymentsListScreenState extends State<PaymentsListScreen> with SingleTickerProviderStateMixin {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late PaymentsBloc paymentsBloc = context.read<PaymentsBloc>();
  final _scrollController = ScrollController();

  @override
  void initState() {
    paymentsBloc.add(PaymentsFetched());
    _scrollController.addListener(_onScroll);
    super.initState();
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_onScroll)
      ..dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) paymentsBloc.add(PaymentsFetched());
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  Future<void> _onRefresh() async {
    paymentsBloc.add(PaymentsSetStateInitail());
    paymentsBloc.add(PaymentsFetched());
  }

  Timer? _debounce;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) {
          paymentsBloc.add(PaymentsSetStateInitail());
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            tr!.payments,
          ),
          actions: const [],
        ),
        body: Column(
          children: [
            const SizedBox(
              height: 16,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
              ),
              child: BlocBuilder<PaymentsBloc, PaymentsState>(
                builder: (context, state) {
                  return CSearchBar(
                    label: "${tr!.searchItem(tr!.payments.toLowerCase())}...",
                    initailValue: state.paymentsFilter.search,
                    onChanged: (String text) {
                      if (_debounce?.isActive ?? false) _debounce!.cancel();
                      _debounce = Timer(const Duration(seconds: 1), () {
                        paymentsBloc.add(
                          PaymentsFilterChanged(
                            paymentsFilter: state.paymentsFilter.copyWith(search: text),
                          ),
                        );
                        paymentsBloc.add(PaymentsSetStateInitail());
                        paymentsBloc.add(PaymentsFetched());
                      });
                    },
                  );
                },
              ),
            ),
            const SizedBox(
              height: 16,
            ),
            Expanded(
              child: RefreshIndicator(
                onRefresh: _onRefresh,
                child: BlocBuilder<PaymentsBloc, PaymentsState>(
                  builder: (context, state) {
                    switch (state.status) {
                      case PaymentsDataLoadingStatus.failure:
                        return Center(child: Text(tr!.failedToFetchItems(tr!.payments)));
                      case PaymentsDataLoadingStatus.success:
                        if (state.payments.isEmpty) {
                          return NoItemsFound(
                            svgIcon: 'assets/svgs/invoice-open-icon.svg',
                            onRefresh: _onRefresh,
                            itemName: tr!.payments,
                          );
                        }
                        return ListView.builder(
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: state.hasReachedMax ? state.payments.length : state.payments.length + 1,
                          itemBuilder: (context, index) {
                            if (index >= state.payments.length) {
                              return const PaymentsItemCardSkeleton();
                            }
                            PaymentModel payment = state.payments[index];
                            return PaymentItemCard(payment: payment);
                          },
                        );
                      case PaymentsDataLoadingStatus.initial:
                        return ListView.builder(
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: 10,
                          itemBuilder: (context, index) {
                            return const PaymentsItemCardSkeleton();
                          },
                        );
                    }
                  },
                ),
              ),
            ),
            const SizedBox(
              height: 16,
            ),
          ],
        ),
      ),
    );
  }
}
