import 'package:dio_client/dio_client.dart';
import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/exports/blocs.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_textfield.widget.dart';
import 'package:url_launcher/url_launcher.dart';

class ContactUsScreen extends StatefulWidget {
  const ContactUsScreen({super.key});

  @override
  State<ContactUsScreen> createState() => _ContactUsScreenState();
}

class _ContactUsScreenState extends State<ContactUsScreen> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  TextEditingController nameController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController subjectController = TextEditingController();
  TextEditingController messageController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  late final DioClient _dioClient = RepositoryProvider.of<DioClient>(context);
  bool _loading = false;

  String url = 'https://peacegl.com/api/contact';

  String email = '<EMAIL>';
  String phone = '+****************';
  Map<String, String> whatsapp = {
    "text": "+****************",
    "link": "https://wa.me/19122395061",
  };
  Map<String, String> location1 = {
    "text": "2824 Tremont Rd, Savannah, GA 31405",
    "link": "https://www.google.com/maps/place/2824+Tremont+Rd,+Savannah,+GA+31405,+USA",
  };

  Future<void> _submit() async {
    try {
      setState(() {
        _loading = true;
      });
      await _dioClient.post(
        url,
        data: {
          "email": emailController.text,
          "name": nameController.text,
          "number": phoneController.text,
          "subject": subjectController.text,
          "text": messageController.text,
          "type": "contact"
        },
      );
      _showDilog(
        tr!.success,
        tr!.contactMessageSuccess,
      );
      emailController.text = '';
      nameController.text = '';
      phoneController.text = '';
      subjectController.text = '';
      messageController.text = '';
      setState(() {
        _loading = false;
      });
    } catch (e) {
      _showDilog(
        tr!.failure,
        tr!.contactMessageFailure,
      );
      setState(() {
        _loading = false;
      });
    }
  }

  void _showDilog(String title, String content) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: <Widget>[
          FilledButton(
            style: TextButton.styleFrom(
              textStyle: Theme.of(context).textTheme.labelLarge,
            ),
            child: Text(tr!.ok),
            onPressed: () {
              Get.back();
            },
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(tr!.contactUs),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 24, right: 24),
                child: Column(
                  children: [
                    const SizedBox(
                      height: 24,
                    ),
                    SvgPicture.asset(
                      'assets/images/logo.svg',
                      width: MediaQuery.of(context).size.width * .6,
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    Text(
                      tr!.contactText,
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: textField(
                            label: tr!.name,
                            icon: Symbols.badge,
                            controller: nameController,
                            validator: (String? value) {
                              if (value == null) {
                                return tr!.itemMustBeProvided(tr!.name);
                              }
                              if (value.isEmpty) {
                                return tr!.itemMustBeProvided(tr!.name);
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(
                          width: 8,
                        ),
                        Expanded(
                          child: textField(
                            label: tr!.phone,
                            icon: Symbols.call,
                            controller: phoneController,
                            validator: (String? value) {
                              if (value == null) {
                                return tr!.itemMustBeProvided(tr!.phone);
                              }
                              if (value.isEmpty) {
                                return tr!.itemMustBeProvided(tr!.phone);
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    Row(
                      children: [
                        Expanded(
                          child: textField(
                            label: tr!.email,
                            icon: Symbols.alternate_email,
                            controller: emailController,
                            validator: (String? value) {
                              if (value == null) {
                                return tr!.itemMustBeProvided(tr!.email);
                              }
                              if (value.isEmpty) {
                                return tr!.itemMustBeProvided(tr!.email);
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    Row(
                      children: [
                        Expanded(
                          child: textField(
                            label: tr!.subject,
                            icon: Symbols.title,
                            controller: subjectController,
                            validator: (String? value) {
                              if (value == null) {
                                return tr!.itemMustBeProvided(tr!.subject);
                              }
                              if (value.isEmpty) {
                                return tr!.itemMustBeProvided(tr!.subject);
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    textField(
                      label: tr!.message,
                      // icon: Symbols.subject,
                      controller: messageController,
                      minLines: 6,
                      maxLines: 6,
                      borderRadius: 16,
                      validator: (String? value) {
                        if (value == null) {
                          return tr!.itemMustBeProvided(tr!.message);
                        }
                        if (value.isEmpty) {
                          return tr!.itemMustBeProvided(tr!.message);
                        }
                        return null;
                      },
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        FilledButton(
                          onPressed: () {
                            if (_formKey.currentState!.validate()) {
                              _submit();
                            }
                          },
                          child: Row(
                            children: [
                              Text(tr!.submit),
                              const SizedBox(
                                width: 8,
                              ),
                              _loading
                                  ? SizedBox(
                                      height: 18,
                                      width: 18,
                                      child: CircularProgressIndicator(
                                        color: Theme.of(context).colorScheme.onPrimary,
                                        strokeWidth: 2,
                                      ),
                                    )
                                  : const Icon(
                                      Symbols.send,
                                      size: 18,
                                    ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    const Divider(
                      height: 1,
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    GestureDetector(
                      onTap: () {
                        launchUrl(Uri.parse('mailto:$email'));
                      },
                      child: Row(
                        children: [
                          Icon(
                            Symbols.alternate_email,
                            size: 20,
                            color: Theme.of(context).colorScheme.primary,
                            weight: 700,
                          ),
                          const SizedBox(
                            width: 8,
                          ),
                          Text(email)
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 12,
                    ),
                    GestureDetector(
                      onTap: () {
                        launchUrl(Uri.parse('tel:$phone'));
                      },
                      child: Row(
                        children: [
                          Icon(
                            Symbols.call,
                            size: 20,
                            color: Theme.of(context).colorScheme.primary,
                            weight: 700,
                          ),
                          const SizedBox(
                            width: 8,
                          ),
                          Text(phone)
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 12,
                    ),
                    GestureDetector(
                      onTap: () {
                        launchUrl(Uri.parse(whatsapp['link'] ?? ''));
                      },
                      child: Row(
                        children: [
                          SvgPicture.string(
                            '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" fill="currentColor"><!--!Font Awesome Free 6.5.1 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2024 Fonticons, Inc.--><path d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7 .9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"/></svg>',
                            width: 20,
                            theme: SvgTheme(
                              currentColor: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          const SizedBox(
                            width: 8,
                          ),
                          Text(whatsapp['text'] ?? '')
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 12,
                    ),
                    GestureDetector(
                      onTap: () {
                        launchUrl(Uri.parse(location1['link'] ?? ''));
                      },
                      child: Row(
                        children: [
                          Icon(
                            Symbols.location_on,
                            size: 20,
                            color: Theme.of(context).colorScheme.primary,
                            weight: 700,
                          ),
                          const SizedBox(
                            width: 8,
                          ),
                          Expanded(
                            child: Text(location1['text'] ?? ''),
                          )
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        GestureDetector(
                          onTap: () {
                            launchUrl(Uri.parse('https://peacegl.com'));
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: Text(
                              tr!.copyRight,
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.primary,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget textField({
    required String label,
    IconData? icon,
    required TextEditingController controller,
    int? minLines,
    int? maxLines = 1,
    double? borderRadius,
    String? Function(String? value)? validator,
  }) {
    return CTextField(
      label: label,
      icon: icon,
      iconSize: 20,
      controller: controller,
      labelStyle: const TextStyle(
        fontSize: 12,
      ),
      style: const TextStyle(
        fontSize: 12,
      ),
      contentPadding: const EdgeInsets.symmetric(
        horizontal: 20,
        vertical: 12,
      ),
      prefixIconConstraints: const BoxConstraints(
        minWidth: 42,
        maxWidth: 42,
      ),
      minLines: maxLines,
      maxLines: maxLines,
      borderRadius: borderRadius,
      validator: validator,
    );
  }
}
