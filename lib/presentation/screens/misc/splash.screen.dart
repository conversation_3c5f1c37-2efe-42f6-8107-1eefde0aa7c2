import 'package:authentication_repository/authentication_repository.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:pgl_mobile_app/constants/routes.const.dart';
import 'package:pgl_mobile_app/logic/blocs/authentication/authentication.exports.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => const SplashScreen());
  }

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  late Size mqSize = MediaQuery.of(context).size;

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      AuthenticationStatus st = context.read<AuthenticationBloc>().state.status;
      switch (st) {
        case AuthenticationStatus.authenticated:
          Get.offAllNamed(home);
          break;
        case AuthenticationStatus.unauthenticated:
        case AuthenticationStatus.unknown:
          Get.offAllNamed(login);
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/new_launcher_icons/ic_logo_splash.png',
              width: mqSize.width * .7,
            )
            // SvgPicture.asset(
            //   'assets/images/logo.svg',
            //   width: mqSize.width * .7,
            // ),
          ],
        ),
      ),
    );
  }
}
