import 'package:flutter/material.dart';
import 'package:galleryimage/gallery_image_view_wrapper.dart';
import 'package:galleryimage/gallery_item_model.dart';
import 'package:get/route_manager.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ImagesPreviewArguments {
  final List<String> urls;
  final int initialIndex;
  final bool showListInGalley;
  final String? title;
  ImagesPreviewArguments({
    required this.urls,
    required this.initialIndex,
    this.showListInGalley = true,
    this.title,
  });
}

class ImagesPreviewScreen extends StatefulWidget {
  const ImagesPreviewScreen({super.key});

  @override
  State<ImagesPreviewScreen> createState() => _ImagesPreviewScreenState();
}

class _ImagesPreviewScreenState extends State<ImagesPreviewScreen> {
  late ImagesPreviewArguments arguments;
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  void initState() {
    setState(() {
      arguments = Get.arguments;
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(arguments.title ?? tr!.images),
      ),
      body: GalleryImageViewWrapper(
        galleryItems: arguments.urls
            .asMap()
            .map(
              (index, item) => MapEntry(
                index,
                GalleryItemModel(
                  id: '$index',
                  imageUrl: item,
                  index: index,
                ),
              ),
            )
            .values
            .toList(),
        backgroundColor: Theme.of(context).colorScheme.surface,
        closeWhenSwipeDown: false,
        closeWhenSwipeUp: false,
        titleGallery: tr!.images,
        initialIndex: arguments.initialIndex,
        loadingWidget: Skeletonizer(
          child: Skeleton.replace(
            replace: true,
            child: Container(),
          ),
        ),
        errorWidget: Container(),
        minScale: .5,
        maxScale: 2,
        radius: 2,
        reverse: false,
        showAppBar: false,
        showListInGalley: arguments.showListInGalley,
      ),
    );
  }
}
