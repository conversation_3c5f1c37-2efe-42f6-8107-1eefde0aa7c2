import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:open_file/open_file.dart';
import 'package:pgl_mobile_app/constants/constants.dart';
import 'package:pgl_mobile_app/exports/blocs.exports.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/filter/fields/auto_complete_field.widget.dart';
import 'package:skeletonizer/skeletonizer.dart';

class MixShippingCalculator extends StatefulWidget {
  const MixShippingCalculator({super.key});

  @override
  State<MixShippingCalculator> createState() => _MixShippingCalculatorState();
}

class _MixShippingCalculatorState extends State<MixShippingCalculator> {
  final _formKey = GlobalKey<FormState>();
  late AppLocalizations? tr = AppLocalizations.of(context);
  List<StateModel> stateModels = [];
  List<BranchModel> branchModels = [];
  List<CityModel> cityModels = [];
  List<DestinationModel> destinationModels = [];
  StateModel? stateModel;
  BranchModel? branchModel;
  CityModel? cityModel;
  DestinationModel? destinationModel;
  // TextEditingController vehiclePriceController = TextEditingController();
  bool fullSizeSUV = false;
  bool manheimAdesa = false;
  bool majorAccident = false;
  bool _loading = false;
  bool _pdfDownloading = false;
  MixShoppingCalculationModel? calculationModel;
  @override
  initState() {
    _fetchStates();
    _fetchDestinations();
    super.initState();
  }

  Future<void> _downloadPDF() async {
    try {
      setState(() {
        _pdfDownloading = true;
      });
      File invoice = await RepositoryProvider.of<MixShippingRatesRepository>(context).downloadPDF();
      OpenFile.open(invoice.path);
      setState(() {
        _pdfDownloading = false;
      });
    } catch (e) {
      setState(() {
        _pdfDownloading = false;
      });
    }
  }

  Future<void> _submit() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _loading = true;
      });
      MixShoppingCalculationModel? result =
          await RepositoryProvider.of<MixShippingRatesRepository>(context).getCalculation(
        {
          "loading_city_id": cityModel?.id,
          "destination_id": destinationModel?.id,
          // "vehicle_price": destinationModel?.id == 24
          //     ? int.parse(vehiclePriceController.text) * 0.386
          //     : int.parse(vehiclePriceController.text),
          "full_size_SUVs": fullSizeSUV,
          "manheim_adesa": manheimAdesa,
          "major_accident": majorAccident,
        },
      );
      setState(() {
        _loading = false;
        calculationModel = result;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(tr!.mixShippingCalculator),
        actions: const [
          SizedBox(
            width: 8,
          )
        ],
      ),
      body: Form(
        key: _formKey,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      const SizedBox(
                        height: 24,
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ..._state(label: tr!.state, icon: Symbols.tag),
                          const SizedBox(
                            width: 8,
                          ),
                          ..._branch(label: tr!.branch, icon: Symbols.tag),
                        ],
                      ),
                      const SizedBox(
                        height: 16,
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ..._city(label: tr!.city, icon: Symbols.tag),
                          const SizedBox(
                            width: 8,
                          ),
                          ..._destination(
                            label: tr!.destination,
                            icon: Symbols.tag,
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 16,
                      ),
                      // _price(
                      //   label: tr!.vehiclePrice,
                      //   icon: Symbols.attach_money,
                      //   controller: vehiclePriceController,
                      // ),

                      // const SizedBox(
                      //   height: 16,
                      // ),
                      Row(
                        children: [
                          Checkbox(
                            value: fullSizeSUV,
                            semanticLabel: tr!.fullSizeSUVs,
                            onChanged: (bool? value) {
                              setState(() {
                                fullSizeSUV = value ?? false;
                              });
                            },
                          ),
                          Text(tr!.fullSizeSUVs)
                        ],
                      ),
                      Row(
                        children: [
                          Checkbox(
                            value: manheimAdesa,
                            semanticLabel: tr!.manheimAdesa,
                            onChanged: (bool? value) {
                              setState(() {
                                manheimAdesa = value ?? false;
                              });
                            },
                          ),
                          Text(tr!.manheimAdesa)
                        ],
                      ),
                      Row(
                        children: [
                          Checkbox(
                            value: majorAccident,
                            semanticLabel: tr!.majorAccident,
                            onChanged: (bool? value) {
                              setState(() {
                                majorAccident = value ?? false;
                              });
                            },
                          ),
                          Text(tr!.majorAccident)
                        ],
                      ),
                      const SizedBox(
                        height: 24,
                      ),
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                                text: '${tr!.disclaimer}: ',
                                style: const TextStyle(
                                  fontWeight: FontWeight.w600,
                                )),
                            TextSpan(
                              text: '${tr!.anEstimateCustomDutyCharge}.',
                            ),
                          ],
                          style: Theme.of(context).textTheme.titleSmall!.copyWith(
                                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: .8),
                              ),
                        ),
                      ),
                      const SizedBox(
                        height: 24,
                      ),
                      if (calculationModel != null)
                        if (calculationModel!.notSupported)
                          Text(
                            tr!.notSupportedDestination,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.error,
                            ),
                          ),
                      if (calculationModel != null)
                        if (!calculationModel!.notSupported)
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Symbols.payments,
                                size: 20,
                                color: Theme.of(context).colorScheme.primary,
                                weight: 800,
                              ),
                              const SizedBox(
                                width: 6,
                              ),
                              Text(
                                '${tr!.total} : ',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w700,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                              ),
                              if (!_loading)
                                Text(
                                  '${destinationModel?.id == 24 ? 'OMR' : '\$'} ${NumberFormat('###,###,###,###').format(calculationModel!.getTotal())}',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w700,
                                  ),
                                )
                              else
                                const Skeletonizer(
                                    child: Text(
                                  '\$ 11111',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ))
                            ],
                          ),
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            FilledButton(
                              onPressed: _submit,
                              child: Row(
                                children: [
                                  Text(tr!.calculate),
                                  const SizedBox(
                                    width: 8,
                                  ),
                                  _loading
                                      ? SizedBox(
                                          height: 18,
                                          width: 18,
                                          child: CircularProgressIndicator(
                                            color: Theme.of(context).colorScheme.onPrimary,
                                            strokeWidth: 2,
                                          ),
                                        )
                                      : const Icon(
                                          Symbols.send,
                                          size: 18,
                                        ),
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 16,
                      ),
                    ],
                  ),
                ),
              ),
              if (MediaQuery.of(context).viewInsets.bottom == 0) ...[
                const SizedBox(
                  height: 16,
                ),
                RichText(
                  text: TextSpan(
                    text: '${tr!.mixShippingCalculationTerms} ',
                    children: [
                      TextSpan(
                        text: '${tr!.clickHere}.',
                        style: Theme.of(context).textTheme.titleSmall!.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                            ),
                        recognizer: TapGestureRecognizer()..onTap = () => Get.toNamed(mixShippingCalculatorTerms),
                      ),
                    ],
                    style: Theme.of(context).textTheme.titleSmall!.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: .8),
                        ),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(
                  height: 16,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    FilledButton(
                      style: const ButtonStyle(
                        padding: WidgetStatePropertyAll(
                          EdgeInsets.symmetric(horizontal: 12),
                        ),
                      ),
                      onPressed: _downloadPDF,
                      child: Row(
                        children: [
                          _pdfDownloading
                              ? SizedBox(
                                  height: 18,
                                  width: 18,
                                  child: Align(
                                    child: SizedBox(
                                      height: 18,
                                      width: 18,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: Theme.of(context).colorScheme.onPrimary,
                                      ),
                                    ),
                                  ),
                                )
                              : const Icon(
                                  Symbols.download,
                                  size: 20,
                                ),
                          const SizedBox(
                            width: 6,
                          ),
                          Text(
                            tr!.downloadMixShippingRates,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
              const SizedBox(
                height: 24,
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _state({required String label, required IconData icon}) {
    return [
      if (!stateLoading)
        Expanded(
          child: AutoCompleteField<StateModel>(
            label: label,
            icon: icon,
            getItems: filterStates,
            displayStringForOption: (StateModel option) {
              return option.name.trim();
            },
            onChanged: (value) async {
              setState(() {
                stateModel = value;
              });
              _fetchBranches(value.id);
            },
            setController: (cn) {
              cn.addListener(() {
                if (stateModels.where((element) => element.name == cn.text).isEmpty) {
                  setState(() {
                    stateModel = null;
                    branchModel = null;
                    cityModel = null;
                  });
                }
              });
            },
            debounceDuration: const Duration(seconds: 0),
            allowEmpty: true,
            validator: (String? value) {
              if (stateModel == null) {
                return tr!.itemMustBeProvided(label);
              }
              return null;
            },
            loading: stateLoading,
          ),
        )
      else
        Expanded(
          child: placeHolder(
            label: label,
            icon: icon,
            validator: (String? value) {
              if (stateModel == null) {
                return tr!.itemMustBeProvided(label);
              }
              return null;
            },
            loading: stateLoading,
          ),
        ),
    ];
  }

  List<Widget> _branch({required String label, required IconData icon}) {
    return [
      if (stateModel != null && branchModels.isNotEmpty && !branchLoading)
        Expanded(
          child: AutoCompleteField<BranchModel>(
            enabled: true,
            label: label,
            icon: icon,
            getItems: filterBranches,
            displayStringForOption: (BranchModel option) {
              return option.name.trim();
            },
            onChanged: (value) {
              setState(() {
                branchModel = value;
              });
              _fetchCities(value.id);
            },
            setController: (cn) {
              cn.addListener(() {
                if (branchModels.where((element) => element.name == cn.text).isEmpty) {
                  setState(() {
                    branchModel = null;
                    cityModel = null;
                  });
                }
              });
            },
            debounceDuration: const Duration(seconds: 0),
            allowEmpty: true,
            validator: (String? value) {
              if (branchModel == null) {
                return tr!.itemMustBeProvided(label);
              }
              return null;
            },
            loading: branchLoading,
          ),
        )
      else
        Expanded(
          child: placeHolder(
            label: label,
            icon: icon,
            validator: (String? value) {
              if (branchModel == null) {
                return tr!.itemMustBeProvided(label);
              }
              return null;
            },
            loading: branchLoading,
          ),
        ),
    ];
  }

  List<Widget> _city({required String label, required IconData icon}) {
    return [
      if (branchModel != null && cityModels.isNotEmpty && !cityLoading)
        Expanded(
          child: AutoCompleteField<CityModel>(
            enabled: true,
            label: label,
            icon: icon,
            getItems: filterCities,
            displayStringForOption: (CityModel option) {
              return option.name.trim();
            },
            onChanged: (value) {
              setState(() {
                cityModel = value;
              });
            },
            setController: (cn) {
              cn.addListener(() {
                if (cityModels.where((element) => element.name == cn.text).isEmpty) {
                  setState(() {
                    cityModel = null;
                  });
                }
              });
            },
            debounceDuration: const Duration(seconds: 0),
            allowEmpty: true,
            validator: (String? value) {
              if (cityModel == null) {
                return tr!.itemMustBeProvided(label);
              }
              return null;
            },
            loading: cityLoading,
          ),
        )
      else // placeholder
        Expanded(
          child: placeHolder(
            label: label,
            icon: icon,
            validator: (String? value) {
              if (cityModel == null) {
                return tr!.itemMustBeProvided(label);
              }
              return null;
            },
            loading: cityLoading,
          ),
        ),
    ];
  }

  List<Widget> _destination({required String label, required IconData icon}) {
    return [
      if (!destinationsLoading)
        Expanded(
          child: AutoCompleteField<DestinationModel>(
            enabled: true,
            label: label,
            icon: icon,
            getItems: filterDestinations,
            displayStringForOption: (DestinationModel option) {
              return option.name.trim();
            },
            onChanged: (value) {
              setState(() {
                destinationModel = value;
              });
              // if (vehiclePriceController.text.isNotEmpty) {
              _submit();
              // }
            },
            setController: (cn) {
              cn.addListener(() {
                if (destinationModels.where((element) => element.name == cn.text).isEmpty) {
                  setState(() {
                    destinationModel = null;
                  });
                }
              });
            },
            debounceDuration: const Duration(seconds: 0),
            allowEmpty: true,
            validator: (String? value) {
              if (destinationModel == null) {
                return tr!.itemMustBeProvided(label);
              }
              return null;
            },
            initialValue: destinationModel?.name,
            loading: destinationsLoading,
          ),
        )
      else // placeholder
        Expanded(
          child: placeHolder(
            label: label,
            icon: icon,
            validator: (String? value) {
              if (destinationModel == null) {
                return tr!.itemMustBeProvided(label);
              }
              return null;
            },
            loading: destinationsLoading,
          ),
        ),
    ];
  }

  // Widget _price({required String label, required IconData icon, required TextEditingController controller}) {
  //   return FilterTextField(
  //     label: label,
  //     icon: icon,
  //     textEditingController: controller,
  //     isNumber: true,
  //     validator: (String? value) {
  //       if (value == null) {
  //         return tr!.itemMustBeProvided(label);
  //       }
  //       if (value.isEmpty) {
  //         return tr!.itemMustBeProvided(label);
  //       }
  //       return null;
  //     },
  //   );
  // }

  Widget placeHolder({
    required String label,
    required IconData icon,
    String? Function(String? value)? validator,
    bool loading = false,
  }) {
    return AutoCompleteField<String>(
      enabled: false,
      label: label,
      icon: icon,
      getItems: (String s) async {
        return [];
      },
      displayStringForOption: (String option) {
        return option.trim();
      },
      onChanged: (value) {},
      setController: (cn) {},
      validator: validator,
      loading: loading,
    );
  }

  bool stateLoading = false;

  Future<void> _fetchStates() async {
    setState(() {
      stateLoading = true;
    });
    List<StateModel> states = await RepositoryProvider.of<MixShippingRatesRepository>(context).fetchStates();
    setState(() {
      stateModels = states;
      stateLoading = false;
    });
  }

  bool branchLoading = false;

  Future<void> _fetchBranches(int? stateId) async {
    if (stateId != null) {
      setState(() {
        branchLoading = true;
      });
      List<BranchModel> states = await RepositoryProvider.of<MixShippingRatesRepository>(context).fetchBranches(
        stateId,
      );
      setState(() {
        branchModels = states;
        branchLoading = false;
      });
    }
  }

  bool cityLoading = false;

  Future<void> _fetchCities(int? branchId) async {
    if (branchId != null) {
      setState(() {
        cityLoading = true;
      });
      List<CityModel> cities = await RepositoryProvider.of<MixShippingRatesRepository>(context).fetchCities(
        branchId,
      );
      setState(() {
        cityModels = cities;
        cityLoading = false;
      });
    }
  }

  bool destinationsLoading = false;

  Future<void> _fetchDestinations() async {
    setState(() {
      destinationsLoading = true;
    });
    List<DestinationModel> destinations =
        await RepositoryProvider.of<MixShippingRatesRepository>(context).fetchDestinations();
    setState(() {
      destinationModels = destinations;
      destinationModel = destinationModels.isNotEmpty
          ? destinationModels
              .firstWhereOrNull((element) => element.id == context.read<AuthenticationBloc>().state.user.destinationID)
          : null;
      destinationsLoading = false;
    });
  }

  Future<Iterable<StateModel>> filterStates(String search) async {
    if (stateModels.isEmpty) await _fetchStates();
    if (search.isEmpty) {
      return stateModels;
    }
    return stateModels.where((element) => element.name.toLowerCase().contains(search.toLowerCase()));
  }

  Future<Iterable<BranchModel>> filterBranches(String search) async {
    if (search.isEmpty) {
      return branchModels;
    }
    return branchModels.where((element) => element.name.toLowerCase().contains(search.toLowerCase()));
  }

  Future<Iterable<CityModel>> filterCities(String search) async {
    if (search.isEmpty) {
      return cityModels;
    }
    return cityModels.where((element) => element.name.toLowerCase().contains(search.toLowerCase()));
  }

  Future<Iterable<DestinationModel>> filterDestinations(String search) async {
    if (search.isEmpty) {
      return destinationModels;
    }
    return destinationModels.where((element) => element.name.toLowerCase().contains(search.toLowerCase()));
  }
}
