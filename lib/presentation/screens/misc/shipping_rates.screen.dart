import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:get/route_manager.dart';
import 'package:intl/intl.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/constants/date_formats.const.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/logic/blocs/dashboard/bloc/dashboard_bloc.dart';
import 'package:pgl_mobile_app/logic/blocs/notifications/bloc/notifications_bloc.dart';
import 'package:pgl_mobile_app/logic/blocs/shipping_rates/bloc/shipping_rates_bloc.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/shipping_rate_item.card.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/shipping_rate_item.card.skeleton.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_search_bar.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/no_items_found.widget.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:url_launcher/url_launcher.dart';

class ShippingRatesScreen extends StatefulWidget {
  const ShippingRatesScreen({super.key});

  @override
  State<ShippingRatesScreen> createState() => _ShippingRatesScreenState();
}

class _ShippingRatesScreenState extends State<ShippingRatesScreen> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late ShippingRatesBloc shippingRatesBloc = context.read<ShippingRatesBloc>();
  final _scrollController = ScrollController();

  @override
  void initState() {
    shippingRatesBloc.add(ShippingRatesMarkAllAsRead());
    shippingRatesBloc.add(ShippingRatesFetched());
    context.read<DashboardBloc>().add(const ShippingRatesUnreadCountChanged(countShippingRates: 0));
    context.read<NotificationsBloc>().add(
          const NotificationsMarkAllAsRead(
            notificationType: NotificationType.shippingRate,
          ),
        );
    _scrollController.addListener(_onScroll);

    super.initState();
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_onScroll)
      ..dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) shippingRatesBloc.add(ShippingRatesFetched());
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  Future<void> _onRefresh() async {
    shippingRatesBloc.add(ShippingRatesSetStateInitail());
    shippingRatesBloc.add(ShippingRatesFetched());
  }

  Timer? _debounce;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) {
          shippingRatesBloc.add(ShippingRatesSetStateInitail());
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            tr!.shippingRates,
          ),
          actions: [
            BlocBuilder<ShippingRatesBloc, ShippingRatesState>(
              builder: (context, state) {
                return state.note != ''
                    ? IconButton(
                        onPressed: () {
                          showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return AlertDialog(
                                title: Text(tr!.note),
                                content: HtmlWidget(
                                  state.note,
                                  onTapUrl: (url) async {
                                    launchUrl(Uri.parse(url));
                                    return true;
                                  },
                                ),
                                actions: <Widget>[
                                  FilledButton(
                                    style: TextButton.styleFrom(
                                      textStyle: Theme.of(context).textTheme.labelLarge,
                                    ),
                                    child: Text(tr!.close),
                                    onPressed: () async {
                                      Get.back();
                                    },
                                  )
                                ],
                              );
                            },
                          );
                        },
                        icon: const Icon(
                          Symbols.description,
                        ),
                      )
                    : Container();
              },
            ),
            const SizedBox(
              width: 8,
            )
          ],
        ),
        body: Column(
          children: [
            const SizedBox(
              height: 16,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
              ),
              child: BlocBuilder<ShippingRatesBloc, ShippingRatesState>(
                builder: (context, state) {
                  return CSearchBar(
                    label: "${tr!.searchItem(tr!.shippingRates.toLowerCase())}...",
                    initailValue: state.shippingRatesFilter.search,
                    onChanged: (String text) {
                      if (_debounce?.isActive ?? false) _debounce!.cancel();
                      _debounce = Timer(const Duration(seconds: 1), () {
                        shippingRatesBloc.add(
                          ShippingRatesFilterChanged(
                            shippingRatesFilter: state.shippingRatesFilter.copyWith(search: text),
                          ),
                        );
                        shippingRatesBloc.add(ShippingRatesSetStateInitail());
                        shippingRatesBloc.add(ShippingRatesFetched());
                      });
                    },
                  );
                },
              ),
            ),
            BlocBuilder<ShippingRatesBloc, ShippingRatesState>(
              builder: (context, state) {
                return Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 16,
                    horizontal: 16,
                  ),
                  child: Skeletonizer(
                    enabled: state.status == ShippingRatesDataLoadingStatus.initial,
                    child: Row(
                      children: [
                        Icon(
                          Symbols.calendar_month,
                          size: 20,
                          color: Theme.of(context).colorScheme.primary,
                          weight: 600,
                        ),
                        const SizedBox(
                          width: 6,
                        ),
                        Text(
                          '${tr!.effectiveFrom}: ',
                          style: TextStyle(
                            fontSize: 14,
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        Text(
                          DateFormat(cardsDateFormat).format(
                            state.shippingRates.isEmpty ? DateTime.now() : state.shippingRates[0].effectiveDate,
                          ),
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
            Expanded(
              child: RefreshIndicator(
                onRefresh: _onRefresh,
                child: BlocBuilder<ShippingRatesBloc, ShippingRatesState>(
                  builder: (context, state) {
                    switch (state.status) {
                      case ShippingRatesDataLoadingStatus.failure:
                        return Center(child: Text(tr!.failedToFetchItems(tr!.shippingRates)));
                      case ShippingRatesDataLoadingStatus.success:
                        if (state.shippingRates.isEmpty) {
                          return NoItemsFound(
                            svgIcon: 'assets/svgs/invoice-icon.svg',
                            onRefresh: _onRefresh,
                            itemName: tr!.shippingRates,
                          );
                        }
                        return ListView.builder(
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: state.hasReachedMax ? state.shippingRates.length : state.shippingRates.length + 1,
                          itemBuilder: (context, index) {
                            if (index >= state.shippingRates.length) {
                              return const ShippingRateItemCardSkeleton();
                            }
                            ShippingRate shippingRate = state.shippingRates[index];
                            return ShippingRateItemCard(shippingRate: shippingRate);
                          },
                        );
                      case ShippingRatesDataLoadingStatus.initial:
                        return ListView.builder(
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: 10,
                          itemBuilder: (context, index) {
                            return const ShippingRateItemCardSkeleton();
                          },
                        );
                    }
                  },
                ),
              ),
            ),
            const SizedBox(
              height: 16,
            ),
          ],
        ),
      ),
    );
  }
}
