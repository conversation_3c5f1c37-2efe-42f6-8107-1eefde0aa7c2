import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:invoices_repository/invoices_repository.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:mix_shipping_repository/mix_shipping_repository.dart';
import 'package:pgl_mobile_app/constants/constants.dart';
import 'package:pgl_mobile_app/exports/blocs.exports.dart';
import 'package:pgl_mobile_app/presentation/screens/screens.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/invoices/invoice_item.card.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/mix_shipping/mix_shipping_item.card.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/shipment_item.card.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/vehicle_item.card.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/vehicle_item.card.skeleton.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_search_bar.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/no_items_found.widget.dart';
import 'package:shipments_repository/shipments_repository.dart';
import 'package:vehicles_repository/vehicles_repository.dart';

class GlobalSeachScreen extends StatefulWidget {
  const GlobalSeachScreen({super.key});

  @override
  State<GlobalSeachScreen> createState() => _GlobalSeachScreenState();
}

class _GlobalSeachScreenState extends State<GlobalSeachScreen> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late GlobalSearchBloc globalSearchBloc = context.read<GlobalSearchBloc>();
  final _scrollController = ScrollController();

  Timer? _debounce;

  @override
  void initState() {
    globalSearchBloc.add(ItemsFetched());
    _scrollController.addListener(_onScroll);
    super.initState();
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_onScroll)
      ..dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) globalSearchBloc.add(ItemsFetched());
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  Future<void> _onRefresh() async {
    globalSearchBloc.add(GlobalSearchSetStateInitail());
    globalSearchBloc.add(ItemsFetched());
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) {
          globalSearchBloc.add(GlobalSearchSetStateInitail());
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(tr!.searchItem('')),
        ),
        body: Column(
          children: [
            const SizedBox(
              height: 16,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
              ),
              child: BlocBuilder<GlobalSearchBloc, GlobalSearchState>(
                builder: (context, state) {
                  return CSearchBar(
                    label: "${tr!.searchItem(tr!.vehicles.toLowerCase())}...",
                    initailValue: state.globalSearchFilter.search,
                    onChanged: (String text) {
                      if (text.isNotEmpty) {
                        if (_debounce?.isActive ?? false) _debounce!.cancel();
                        _debounce = Timer(const Duration(seconds: 1), () {
                          globalSearchBloc.add(
                            GlobalSearchFilterChanged(
                              globalSearchFilter: state.globalSearchFilter.copyWith(search: text),
                            ),
                          );
                          globalSearchBloc.add(GlobalSearchSetStateInitail());
                          globalSearchBloc.add(ItemsFetched());
                        });
                      }
                    },
                  );
                },
              ),
            ),
            const SizedBox(
              height: 16,
            ),
            Expanded(
              child: RefreshIndicator(
                onRefresh: _onRefresh,
                child: BlocBuilder<GlobalSearchBloc, GlobalSearchState>(
                  builder: (context, state) {
                    switch (state.status) {
                      case GlobalSearchLoadingStatus.failure:
                        return Center(child: Text(tr!.failedToFetchItems(tr!.items)));
                      case GlobalSearchLoadingStatus.success:
                        if (state.items.isEmpty) {
                          return NoItemsFound(
                            svgIcon: 'assets/svgs/cars-icon.svg',
                            onRefresh: _onRefresh,
                            itemName: tr!.vehicles,
                          );
                        }
                        return ListView.builder(
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          // padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: state.hasReachedMax || state.items.length < 5
                              ? state.items.length
                              : state.items.length + 1,
                          itemBuilder: (context, index) {
                            if (index >= state.items.length) {
                              return const VehicleItemCardSkeleton();
                            }
                            if (state.items[index].runtimeType == Vehicle) {
                              Vehicle vehicle = state.items[index] as Vehicle;
                              return _getVehicleCard(
                                index,
                                index - 1 == -1 || state.items[index - 1].runtimeType != Vehicle,
                                vehicle,
                                state.globalSearchFilter.search,
                              );
                            } else if (state.items[index].runtimeType == Shipment) {
                              Shipment shipment = state.items[index] as Shipment;
                              return _getShipmentCard(
                                index,
                                index - 1 == -1 || state.items[index - 1].runtimeType != Shipment,
                                shipment,
                                state.globalSearchFilter.search,
                              );
                            } else if (state.items[index].runtimeType == Invoice) {
                              Invoice invoice = state.items[index] as Invoice;
                              return _getInvoiceCard(
                                index,
                                index - 1 == -1 || state.items[index - 1].runtimeType != Invoice,
                                invoice,
                                state.globalSearchFilter.search,
                              );
                            } else if (state.items[index].runtimeType == MixShipping) {
                              MixShipping mixShipping = state.items[index] as MixShipping;
                              return _getMixShippingCard(
                                index,
                                index - 1 == -1 || state.items[index - 1].runtimeType != MixShipping,
                                mixShipping,
                                state.globalSearchFilter.search,
                              );
                            }
                            return null;
                          },
                        );
                      case GlobalSearchLoadingStatus.initial:
                        return ListView.builder(
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: 10,
                          itemBuilder: (context, index) {
                            return const VehicleItemCardSkeleton();
                          },
                        );
                    }
                  },
                ),
              ),
            ),
            const SizedBox(
              height: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _getVehicleCard(int index, bool showLable, Vehicle vehicle, String search) {
    if (showLable) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(
              left: 16,
              right: 16,
              bottom: 12,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  tr!.vehicles,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    context.read<VehiclesBloc>().add(
                          VehiclesFilterChanged(
                            vehiclesFilter: VehiclesFilter(state: '', search: search),
                          ),
                        );
                    Get.toNamed(
                      vehiclesList,
                      arguments: VehiclesListArguments(
                        title: tr!.searchItem(tr!.vehicles.toLowerCase()),
                      ),
                    );
                  },
                  child: Row(
                    children: [
                      Text(
                        tr!.seeMore,
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      const SizedBox(
                        width: 4,
                      ),
                      Icon(
                        Symbols.keyboard_double_arrow_right,
                        size: 16,
                        weight: 900,
                        color: Theme.of(context).colorScheme.primary,
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: VehicleItemCard(
              vehicle: vehicle,
              showBackgroundIcon: true,
            ),
          ),
        ],
      );
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: VehicleItemCard(
        vehicle: vehicle,
        showBackgroundIcon: true,
      ),
    );
  }

  Widget _getShipmentCard(int index, bool showLable, Shipment shipment, String search) {
    if (showLable) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(
              left: 16,
              right: 16,
              bottom: 12,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  tr!.shipments,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    context.read<ShipmentBloc>().add(
                          ShipmentsFilterChanged(
                            shipmentsFilter: ShipmentsFilter(state: '', search: search),
                          ),
                        );
                    Get.toNamed(
                      shipmentsList,
                      arguments: ShipmentsListArguments(
                        title: tr!.searchItem(tr!.shipments.toLowerCase()),
                      ),
                    );
                  },
                  child: Row(
                    children: [
                      Text(
                        tr!.seeMore,
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      const SizedBox(
                        width: 4,
                      ),
                      Icon(
                        Symbols.keyboard_double_arrow_right,
                        size: 16,
                        weight: 900,
                        color: Theme.of(context).colorScheme.primary,
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ShipmentItemCard(
              shipment: shipment,
              showBackgroundIcon: true,
            ),
          ),
        ],
      );
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ShipmentItemCard(
        shipment: shipment,
        showBackgroundIcon: true,
      ),
    );
  }

  Widget _getInvoiceCard(int index, bool showLable, Invoice invoice, String search) {
    if (showLable) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(
              left: 16,
              right: 16,
              bottom: 12,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  tr!.invoices,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    context.read<InvoicesBloc>().add(
                          InvoicesFilterChanged(
                            invoicesFilter: InvoicesFilter(status: '', search: search),
                          ),
                        );
                    Get.toNamed(
                      invoicesList,
                      arguments: InvoicesListArguments(
                        title: tr!.searchItem(tr!.invoices.toLowerCase()),
                      ),
                    );
                  },
                  child: Row(
                    children: [
                      Text(
                        tr!.seeMore,
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      const SizedBox(
                        width: 4,
                      ),
                      Icon(
                        Symbols.keyboard_double_arrow_right,
                        size: 16,
                        weight: 900,
                        color: Theme.of(context).colorScheme.primary,
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: InvoiceItemCard(invoice: invoice),
          ),
        ],
      );
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: InvoiceItemCard(invoice: invoice),
    );
  }

  Widget _getMixShippingCard(int index, bool showLable, MixShipping mixShipping, String search) {
    if (showLable) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(
              left: 16,
              right: 16,
              bottom: 12,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  tr!.mixShipping,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    context.read<MixShippingBloc>().add(
                          MixShippingFilterChanged(
                            mixShippingFilter: MixShippingFilter(status: '', search: search),
                          ),
                        );
                    Get.toNamed(
                      mixShippingList,
                      arguments: MixShippingListArguments(
                        title: tr!.searchItem(tr!.mixShipping.toLowerCase()),
                      ),
                    );
                  },
                  child: Row(
                    children: [
                      Text(
                        tr!.seeMore,
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      const SizedBox(
                        width: 4,
                      ),
                      Icon(
                        Symbols.keyboard_double_arrow_right,
                        size: 16,
                        weight: 900,
                        color: Theme.of(context).colorScheme.primary,
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: MixshippingItemCard(mixShipping: mixShipping),
          ),
        ],
      );
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: MixshippingItemCard(mixShipping: mixShipping),
    );
  }
}
