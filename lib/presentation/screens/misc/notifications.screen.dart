import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:get/get.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:notifications_repository/notifications_repository.dart';
import 'package:pgl_mobile_app/logic/blocs/notifications/bloc/notifications_bloc.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/announcement_item.skeleton.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/no_items_found.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/notification_item.widget.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> with SingleTickerProviderStateMixin {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  final _scrollController = ScrollController();
  late TabController tabController;
  late final NotificationsBloc notificationsBloc = context.read<NotificationsBloc>();
  late NotificationType activeTab;

  void _onScroll() {
    if (_isBottom) notificationsBloc.add(NotificationsFetched());
  }

  int _getDefaultTabInitail(NotificationType type) {
    switch (type) {
      case NotificationType.announcement:
        return 0;
      case NotificationType.arrivalNotice:
        return 1;
      case NotificationType.transaction:
        return 2;
      default:
        return 0;
    }
  }

  @override
  void initState() {
    setState(() {
      activeTab = Get.arguments ?? NotificationType.announcement;
    });
    _onTabChange(activeTab);
    notificationsBloc.add(NotificationsFetched());
    tabController = TabController(
      length: 3,
      vsync: this,
      initialIndex: _getDefaultTabInitail(activeTab),
    );
    tabController.addListener(() {
      switch (tabController.index) {
        case 0:
          _onTabChange(NotificationType.announcement);
          break;
        case 1:
          _onTabChange(NotificationType.arrivalNotice);
          break;
        case 2:
          _onTabChange(NotificationType.transaction);
          break;
      }
      notificationsBloc.add(NotificationsFetched());
    });
    _scrollController.addListener(_onScroll);
    super.initState();
  }

  void _onTabChange(NotificationType notificationType) {
    notificationsBloc.add(
      NotificationsFilterChanged(
        notificationsFilter: NotificationsFilter(notificationTypes: [notificationType]),
      ),
    );
    notificationsBloc.add(
      NotificationsMarkAllAsRead(
        notificationType: notificationType,
      ),
    );
    notificationsBloc.add(
      NotificationsActiveTabChanged(
        notificationType: notificationType,
      ),
    );
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_onScroll)
      ..dispose();
    super.dispose();
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  Future<void> _onRefresh() async {
    _onTabChange(notificationsBloc.state.activeTab);
    notificationsBloc.add(NotificationsFetched());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          tr!.notifications,
        ),
      ),
      body: Column(
        children: [
          BlocBuilder<NotificationsBloc, NotificationsState>(
            builder: (context, state) {
              return TabBar(
                tabAlignment: TabAlignment.center,
                isScrollable: true,
                controller: tabController,
                tabs: <Widget>[
                  Tab(
                    child: Text(
                      tr!.announcements +
                          (state.announcementUnread + state.shippingRateUnread + state.mixShippingRateUnread > 0
                              ? " (${state.announcementUnread + state.shippingRateUnread + state.mixShippingRateUnread})"
                              : ''),
                    ),
                  ),
                  Tab(
                    child: Text(
                      tr!.arrivalNotices + (state.arrivalNoticeUnread > 0 ? " (${state.arrivalNoticeUnread})" : ''),
                    ),
                  ),
                  Tab(
                    child: Text(
                      tr!.payments + (state.transactionUnread > 0 ? " (${state.transactionUnread})" : ''),
                    ),
                  ),
                ],
              );
            },
          ),
          Expanded(
            child: RefreshIndicator(
              onRefresh: _onRefresh,
              child: BlocBuilder<NotificationsBloc, NotificationsState>(
                builder: (context, state) {
                  switch (state.status) {
                    case NotificationsDataLoadingStatus.failure:
                      return Center(child: Text(tr!.failedToFetchItems(tr!.notifications)));
                    case NotificationsDataLoadingStatus.success:
                      if (state.notifications.isEmpty) {
                        return NoItemsFound(
                          svgIcon: '',
                          iconData: Symbols.campaign,
                          onRefresh: _onRefresh,
                          itemName: tr!.notifications,
                        );
                      }
                      return ListView.builder(
                        controller: _scrollController,
                        physics: const AlwaysScrollableScrollPhysics(),
                        itemCount: state.hasReachedMax ? state.notifications.length : state.notifications.length + 1,
                        itemBuilder: (context, index) {
                          if (index >= state.notifications.length) {
                            return const AnnouncementItemSkeleton();
                          }
                          NotificationModel notification = state.notifications[index];
                          return NotificationItem(
                            notification: notification,
                          );
                        },
                      );
                    case NotificationsDataLoadingStatus.initial:
                      return ListView.builder(
                        controller: _scrollController,
                        physics: const AlwaysScrollableScrollPhysics(),
                        itemCount: 10,
                        itemBuilder: (context, index) {
                          return const AnnouncementItemSkeleton();
                        },
                      );
                  }
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
