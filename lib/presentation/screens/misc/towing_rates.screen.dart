import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:open_file/open_file.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/exports/blocs.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/shipping_rate_item.card.skeleton.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/towing_rate_item.card.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_search_bar.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/no_items_found.widget.dart';
import 'package:collection/collection.dart';

class TowingRatesScreen extends StatefulWidget {
  const TowingRatesScreen({super.key});

  @override
  State<TowingRatesScreen> createState() => _TowingRatesScreenState();
}

class _TowingRatesScreenState extends State<TowingRatesScreen> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late TowingRatesBloc towingRatesBloc = context.read<TowingRatesBloc>();
  final _scrollController = ScrollController();
  bool _downloading = false;

  @override
  void initState() {
    towingRatesBloc.add(TowingRatesFetched());
    _scrollController.addListener(_onScroll);
    super.initState();
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_onScroll)
      ..dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) towingRatesBloc.add(TowingRatesFetched());
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  Future<void> _onRefresh() async {
    towingRatesBloc.add(TowingRatesSetStateInitail());
    towingRatesBloc.add(TowingRatesFetched());
  }

  Timer? _debounce;

  Future<void> _downloadTowingRate() async {
    try {
      setState(() {
        _downloading = true;
      });
      File towingRate = await RepositoryProvider.of<TowingRatesRepository>(context).getTowingRatePDF();
      OpenFile.open(towingRate.path);
      setState(() {
        _downloading = false;
      });
    } catch (e) {
      setState(() {
        _downloading = false;
      });
    }
  }

  Future<void> _downloadHalfcutRate() async {
    try {
      setState(() {
        _downloading = true;
      });
      File towingRate = await RepositoryProvider.of<TowingRatesRepository>(context).getHalfcutRatePDF();
      OpenFile.open(towingRate.path);
      setState(() {
        _downloading = false;
      });
    } catch (e) {
      setState(() {
        _downloading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) {
          towingRatesBloc.add(TowingRatesSetStateInitail());
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: BlocBuilder<TowingRatesBloc, TowingRatesState>(
            builder: (context, state) {
              return Text(
                '${tr!.towingRates} | ${state.towingRatesFilter.tab == 'complete' ? tr!.complete : tr!.halfcut}',
              );
            },
          ),
          actions: [
            // SizedBox(
            //   height: 20,
            //   width: 20,
            //   child: CircularProgressIndicator(
            //     color: Theme.of(context).colorScheme.onPrimary,
            //     strokeWidth: 2,
            //   ),
            // ),
            BlocBuilder<TowingRatesBloc, TowingRatesState>(
              builder: (context, state) {
                return _downloading
                    ? Container(
                        padding: const EdgeInsets.all(14),
                        height: 48,
                        width: 48,
                        child: CircularProgressIndicator(
                          color: Theme.of(context).colorScheme.onPrimary,
                          strokeWidth: 2,
                        ),
                      )
                    : IconButton(
                        onPressed: () async {
                          if (state.towingRatesFilter.tab == 'complete') {
                            await _downloadTowingRate();
                          } else if (state.towingRatesFilter.tab == 'halfcut') {
                            await _downloadHalfcutRate();
                          }
                        },
                        icon: Icon(
                          Symbols.download,
                          weight: 600,
                        ),
                      );
              },
            ),
            const SizedBox(
              width: 8,
            )
          ],
        ),
        body: Column(
          children: [
            const SizedBox(
              height: 16,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
              ),
              child: BlocBuilder<TowingRatesBloc, TowingRatesState>(
                builder: (context, state) {
                  return CSearchBar(
                    label: "${tr!.searchItem(tr!.towingRates.toLowerCase())}...",
                    initailValue: state.towingRatesFilter.search,
                    onChanged: (String text) {
                      if (_debounce?.isActive ?? false) _debounce!.cancel();
                      _debounce = Timer(const Duration(seconds: 1), () {
                        towingRatesBloc.add(
                          TowingRatesFilterChanged(
                            towingRatesFilter: state.towingRatesFilter.copyWith(search: text),
                          ),
                        );
                        towingRatesBloc.add(TowingRatesSetStateInitail());
                        towingRatesBloc.add(TowingRatesFetched());
                      });
                    },
                  );
                },
              ),
            ),
            const SizedBox(
              height: 16,
            ),
            Expanded(
              child: RefreshIndicator(
                onRefresh: _onRefresh,
                child: BlocBuilder<TowingRatesBloc, TowingRatesState>(
                  builder: (context, state) {
                    switch (state.status) {
                      case TowingRatesDataLoadingStatus.failure:
                        return Center(child: Text(tr!.failedToFetchItems(tr!.towingRates)));
                      case TowingRatesDataLoadingStatus.success:
                        if (state.towingRates.isEmpty) {
                          return NoItemsFound(
                            svgIcon: 'assets/svgs/invoice-icon.svg',
                            onRefresh: _onRefresh,
                            itemName: tr!.towingRates,
                          );
                        }
                        Map<String, List<TowingRate>> groupedByCity =
                            groupBy(state.towingRates, (TowingRate rate) => rate.stateName);

                        final List items = [];

                        groupedByCity.forEach((stateName, rates) {
                          items.add(stateName); // Add the state name as a header
                          items.addAll(rates); // Add all towing rates for that state
                        });

                        return ListView.builder(
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: state.hasReachedMax ? items.length : items.length + 1,
                          itemBuilder: (context, index) {
                            if (index >= state.towingRates.length) {
                              return const ShippingRateItemCardSkeleton();
                            }
                            final item = items[index];
                            if (item is String) {
                              // Header (state name)
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 8, left: 4, right: 4),
                                child: Text(
                                  item, // The state name
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context).colorScheme.primary,
                                  ),
                                ),
                              );
                            } else if (item is TowingRate) {
                              // TowingRate item
                              return TowingRateItemCard(towingRate: item);
                            } else {
                              return const SizedBox.shrink(); // Just in case of unexpected type
                            }
                          },
                        );
                      case TowingRatesDataLoadingStatus.initial:
                        return ListView.builder(
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: 10,
                          itemBuilder: (context, index) {
                            return const ShippingRateItemCardSkeleton();
                          },
                        );
                    }
                  },
                ),
              ),
            ),
            const SizedBox(
              height: 16,
            ),
          ],
        ),
      ),
    );
  }
}
