import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/logic/blocs/announcements/announcements.exports.dart';
import 'package:pgl_mobile_app/logic/blocs/notifications/bloc/notifications_bloc.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/announcement_item.skeleton.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/announcement_item.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/no_items_found.widget.dart';

class AnnouncementsScreen extends StatefulWidget {
  const AnnouncementsScreen({super.key});

  @override
  State<AnnouncementsScreen> createState() => _AnnouncementsScreenState();
}

class _AnnouncementsScreenState extends State<AnnouncementsScreen> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  final _scrollController = ScrollController();

  void _onScroll() {
    if (_isBottom) context.read<AnnouncementsBloc>().add(AnnouncementsFetched());
  }

  @override
  void initState() {
    context.read<AnnouncementsBloc>().add(AnnouncementsFetched());
    context.read<AnnouncementsBloc>().add(AnnouncementsMarkAllAsRead());
    context.read<NotificationsBloc>().add(
          const NotificationsMarkAllAsRead(
            notificationType: NotificationType.announcement,
          ),
        );
    _scrollController.addListener(_onScroll);
    super.initState();
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_onScroll)
      ..dispose();
    super.dispose();
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  Future<void> _onRefresh() async {
    context.read<AnnouncementsBloc>().add(AnnouncementsSetStateInitail());
    context.read<AnnouncementsBloc>().add(AnnouncementsFetched());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          tr!.announcements,
        ),
      ),
      body: RefreshIndicator(
        onRefresh: _onRefresh,
        child: BlocBuilder<AnnouncementsBloc, AnnouncementsState>(
          builder: (context, state) {
            switch (state.status) {
              case AnnouncementsDataLoadingStatus.failure:
                return Center(child: Text(tr!.failedToFetchItems(tr!.announcements)));
              case AnnouncementsDataLoadingStatus.success:
                if (state.announcements.isEmpty) {
                  return NoItemsFound(
                    svgIcon: '',
                    iconData: Symbols.campaign,
                    onRefresh: _onRefresh,
                    itemName: tr!.announcements,
                  );
                }
                return ListView.builder(
                  controller: _scrollController,
                  physics: const AlwaysScrollableScrollPhysics(),
                  itemCount: state.hasReachedMax ? state.announcements.length : state.announcements.length + 1,
                  itemBuilder: (context, index) {
                    if (index >= state.announcements.length) {
                      return const AnnouncementItemSkeleton();
                    }
                    Announcement announcement = state.announcements[index];
                    return AnnouncementItem(
                      announcement: announcement,
                    );
                  },
                );
              case AnnouncementsDataLoadingStatus.initial:
                return ListView.builder(
                  controller: _scrollController,
                  physics: const AlwaysScrollableScrollPhysics(),
                  itemCount: 10,
                  itemBuilder: (context, index) {
                    return const AnnouncementItemSkeleton();
                  },
                );
            }
          },
        ),
      ),
    );
  }
}
