import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:get/get.dart';
import 'package:pgl_mobile_app/configs/themes/colors.theme.dart';
import 'package:pgl_mobile_app/constants/constants.dart';
import 'package:pgl_mobile_app/logic/helpers/images.helper.dart';
import 'package:pgl_mobile_app/presentation/screens/screens.exports.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ImageGalleryArguments {
  final List<String> urls;
  final String? title;

  ImageGalleryArguments({
    required this.urls,
    this.title,
  });
}

class ImageGalleryScreen extends StatefulWidget {
  const ImageGalleryScreen({
    super.key,
  });

  @override
  State<ImageGalleryScreen> createState() => _ImageGalleryScreenState();
}

class _ImageGalleryScreenState extends State<ImageGalleryScreen> {
  late AppLocalizations? tr = AppLocalizations.of(context);

  late ImageGalleryArguments arguments;

  @override
  void initState() {
    setState(() {
      arguments = Get.arguments;
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(arguments.title ?? tr!.images),
      ),
      body: GridView.builder(
        padding: EdgeInsets.all(16),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3, // 2 columns
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: arguments.urls.length, // Example item count
        itemBuilder: (context, index) {
          String imageUrl = arguments.urls[index];
          return Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).brightness == Brightness.light ? Colors.grey.shade300 : Colors.grey.shade800,
                width: 1,
              ),
            ),
            clipBehavior: Clip.hardEdge,
            child: Material(
              color: Theme.of(context).brightness == Brightness.light ? cardBgColorLight : cardBgColorDark,
              borderRadius: BorderRadius.circular(12),
              child: InkWell(
                onTap: () => {
                  Get.toNamed(
                    imagePreview,
                    arguments: ImagesPreviewArguments(
                      urls: arguments.urls.map((url) => getImageSizeUrl2(url: url, size: 1024)).toList(),
                      initialIndex: index,
                      title: arguments.title,
                    ),
                  )
                },
                customBorder: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: CachedNetworkImage(
                  imageUrl: getImageSizeUrl2(url: imageUrl, size: 100),
                  fit: BoxFit.cover,
                  fadeInCurve: Curves.easeIn,
                  fadeOutCurve: Curves.easeIn,
                  fadeInDuration: const Duration(milliseconds: 200),
                  fadeOutDuration: const Duration(milliseconds: 200),
                  placeholder: (context, url) => Skeletonizer(
                    child: Skeleton.replace(
                      replace: true,
                      child: Container(),
                    ),
                  ),
                  errorWidget: (context, url, error) {
                    return const Icon(Icons.error);
                  },
                  httpHeaders: {},
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
