import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/bullet_list.widget.dart';

class MixShippingCalculatorTerms extends StatefulWidget {
  const MixShippingCalculatorTerms({super.key});

  @override
  State<MixShippingCalculatorTerms> createState() => _MixShippingCalculatorTermsState();
}

class _MixShippingCalculatorTermsState extends State<MixShippingCalculatorTerms> {
  late AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(tr!.shippingRatesTerms),
      ),
      body: const SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              Text(
                'IMPORTANT NOTICE',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                ),
              ),
              Text(
                'Rates will be calculated from loading date!',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                'PGL is not responsible for delay for any vessels.',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                ),
              ),
              BulletList([
                'The Ministry of Foreign Affairs and International Cooperation shall collect, through its own systems, a fixed service fee of \$50.00 for attestation fee of each commercial Invoice of imported goods.',
                'Avoid purchasing vehicles from Honolulu.',
                'PGL is not responsible for Hard Location Auction storage fee.',
                'PGL is not responsible for missing Catalytic Converter (If a customer has a photo from Auction to prove the existence of Catalytic Converter, then PGL is responsible).',
                'PGL is not responsible for auctioning photos (PGL has yard pictures and is responsible for their pictures).',
                'Exit papers must be submitted within 75 days otherwise PGL will charge 5% Vat and 5% Duty.',
                'Tax and Duty will return to the customer when PGL receives from Customs.',
                'Customers have 5 days free parking at PGL yard otherwise it will charge 50 AED Per day.',
                'Broker Fee will be \$50.00 Per Car (Broker Account).',
                'Local Cars will charge 5% Vat and 5% Duty.',
                'All cars in mix Container will be cleared by PGLC Company and consignee will be PGL Company.',
                'PGL is not responsible for storages (( Difficult area )).',
                'Vehicles condition should be checked and compared with Auction Photos as soon as your vehicle reaches to PGL yard if you find any discrepancies, customer has to report it to PGL within 2 days for further investigation and check.',
              ]),
              Text(
                'From AUCTION to LOADING POINT',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                ),
              ),
              BulletList([
                '\$50 is added to the below-mentioned prices for Full-Size SUVs such as the Suburban, Tahue, Tundra, Takoma, GMC, and Others of comparable size.',
                '\$50 added to all Manheim and ADESA vehicles that wish to be picked up by the Peace Global Logistics.',
                '\$100 is added to the below-mentioned prices for the vehicles that have been in a major accident, have fallen components, and require additional services at the location of pick up and drop-off subject to towing capabilities.',
                'All automobiles valued more than 10,000 AED are subject to an additional \$49 attestation fee.',
              ]),
            ],
          ),
        ),
      ),
    );
  }
}
