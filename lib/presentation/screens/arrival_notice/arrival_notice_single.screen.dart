import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/constants/date_formats.const.dart';
import 'package:pgl_mobile_app/exports/blocs.exports.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/logic/classes/stripped_table_item.class.dart';
import 'package:pgl_mobile_app/presentation/widgets/bottom_sheets/shipment_detail.bottom_sheet.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/card_style.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_stripped_table.widget.dart';

class ArrivalNoticeSingleScreen extends StatefulWidget {
  const ArrivalNoticeSingleScreen({super.key});

  @override
  State<ArrivalNoticeSingleScreen> createState() => _ArrivalNoticeSingleScreenState();
}

class _ArrivalNoticeSingleScreenState extends State<ArrivalNoticeSingleScreen> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late final NotificationModel notification;
  List<Shipment> shipments = [];

  @override
  void initState() {
    setState(() {
      notification = Get.arguments;
      _fetchShipments();
    });
    context.read<NotificationsBloc>().add(NotificationsMarkOneAsRead(notification: notification));

    super.initState();
  }

  void _fetchShipments() async {
    if (notification.data != null) {
      List<Shipment> temp = await RepositoryProvider.of<ShipmentsRepository>(context).getShipments(
        ShipmentsFilter(
          filterData: ShipmentFilterData(
            containerIds: notification.data?['containers'].map((item) => item['id']).toList(),
          ),
        ),
      );
      setState(() {
        shipments = temp;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          // title: Text(tr!.arrivalNotice),
          ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              notification.title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w700,
              ),
              textAlign: TextAlign.start,
            ),
            const SizedBox(
              height: 8,
            ),
            Row(
              children: [
                Icon(
                  Symbols.schedule,
                  color: Theme.of(context).colorScheme.primary,
                  size: 18,
                ),
                const SizedBox(
                  width: 4,
                ),
                Text(
                  notification.getTimeAgo(),
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: .7),
                  ),
                )
              ],
            ),
            const Divider(),
            const SizedBox(
              height: 8,
            ),
            HtmlWidget(
              notification.description,
              textStyle: const TextStyle(fontSize: 14),
            ),
            const SizedBox(
              height: 16,
            ),
            const Divider(),
            const SizedBox(
              height: 8,
            ),
            ...notification.data?['containers'].map(
              (item) {
                return containerItemCard(item);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget containerItemCard(Map<String, dynamic> item) {
    return CardStyle(
      onTap: () async {
        try {
          Shipment shipment = shipments.firstWhere((element) => element.id == item['id']);
          await showModalBottomSheet(
            isScrollControlled: true,
            useSafeArea: true,
            context: context,
            showDragHandle: true,
            constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * .8),
            builder: (context) => ShipmentDetailBottomSheet(
              shipment: shipment,
            ),
          );
        } catch (e) {
          //
        }
      },
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Row(
                          children: [
                            Icon(
                              Symbols.tag,
                              size: 18,
                              color: Theme.of(context).colorScheme.primary,
                              weight: 700,
                            ),
                            const SizedBox(
                              width: 2,
                            ),
                            Flexible(
                              flex: 1,
                              child: SelectableText(
                                item['container_number'],
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w700,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                                maxLines: 1,
                              ),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                  // container no
                  const SizedBox(
                    height: 16,
                  ),
                  // ammounts
                  CStrippedTable(
                    data: [
                      StrippedTableItem(
                        label: tr!.bookingNo,
                        value: item['bookings']['booking_number'],
                        selectable: true,
                      ),
                      StrippedTableItem(
                        label: tr!.company,
                        value: item['companies']['name'],
                      ),
                      StrippedTableItem(
                        label: tr!.portOfLoading,
                        value: item['bookings']['vessels']['locations']['name'],
                      ),
                      StrippedTableItem(
                        label: tr!.eta,
                        value: DateFormat(cardsDateFormat).format(
                          DateTime.parse(item['bookings']['eta']),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
