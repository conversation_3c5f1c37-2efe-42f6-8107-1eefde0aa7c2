import 'dart:async';

import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/exports/blocs.exports.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/transaction_item.card.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/transaction_item.card.skeleton.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_search_bar.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/no_items_found.widget.dart';

class StatementsListScreen extends StatefulWidget {
  const StatementsListScreen({super.key});

  @override
  State<StatementsListScreen> createState() => _StatementsListScreenState();
}

class _StatementsListScreenState extends State<StatementsListScreen> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late TransactionsBloc transactionsBloc = context.read<TransactionsBloc>();
  final _scrollController = ScrollController();

  @override
  void initState() {
    transactionsBloc.add(TransactionsFetched());
    _scrollController.addListener(_onScroll);
    super.initState();
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_onScroll)
      ..dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) transactionsBloc.add(TransactionsFetched());
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  Future<void> _onRefresh() async {
    transactionsBloc.add(TransactionsSetStateInitail());
    transactionsBloc.add(TransactionsFetched());
  }

  Timer? _debounce;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) {
          transactionsBloc.add(TransactionsSetStateInitail());
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            tr!.statements,
          ),
          actions: const [],
        ),
        body: Column(
          children: [
            const SizedBox(
              height: 16,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
              ),
              child: BlocBuilder<TransactionsBloc, TransactionsState>(
                builder: (context, state) {
                  return CSearchBar(
                    label: "${tr!.searchItem(tr!.statements.toLowerCase())}...",
                    initailValue: state.transactionsFilter.search,
                    onChanged: (String text) {
                      if (_debounce?.isActive ?? false) _debounce!.cancel();
                      _debounce = Timer(const Duration(seconds: 1), () {
                        transactionsBloc.add(
                          TransactionsFilterChanged(
                            transactionsFilter: state.transactionsFilter.copyWith(search: text),
                          ),
                        );
                        transactionsBloc.add(TransactionsSetStateInitail());
                        transactionsBloc.add(TransactionsFetched());
                      });
                    },
                  );
                },
              ),
            ),
            const SizedBox(
              height: 16,
            ),
            Expanded(
              child: RefreshIndicator(
                onRefresh: _onRefresh,
                child: BlocBuilder<TransactionsBloc, TransactionsState>(
                  builder: (context, state) {
                    switch (state.status) {
                      case TransactionsDataLoadingStatus.failure:
                        return Center(child: Text(tr!.failedToFetchItems(tr!.statements)));
                      case TransactionsDataLoadingStatus.success:
                        if (state.transactions.isEmpty) {
                          return NoItemsFound(
                            svgIcon: 'assets/svgs/invoice-open-icon.svg',
                            onRefresh: _onRefresh,
                            itemName: tr!.statements,
                          );
                        }
                        return ListView.builder(
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: state.hasReachedMax ? state.transactions.length : state.transactions.length + 1,
                          itemBuilder: (context, index) {
                            if (index >= state.transactions.length) {
                              return const TransactionItemCardSkeleton();
                            }
                            TransactionModel transaction = state.transactions[index];
                            return TransactionItemCard(transaction: transaction);
                            // return ShippingRateItemCard(shippingRate: shippingRate);
                          },
                        );
                      case TransactionsDataLoadingStatus.initial:
                        return ListView.builder(
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: 10,
                          itemBuilder: (context, index) {
                            return const TransactionItemCardSkeleton();
                          },
                        );
                    }
                  },
                ),
              ),
            ),
            const SizedBox(
              height: 16,
            ),
          ],
        ),
      ),
    );
  }
}
