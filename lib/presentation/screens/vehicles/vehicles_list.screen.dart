import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/route_manager.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/logic/blocs/vehicles/vehicles.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/bottom_sheets/filters/vehicle_filter.bottom_sheet.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/vehicle_item.card.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/vehicle_item.card.skeleton.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_search_bar.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/no_items_found.widget.dart';
import 'package:vehicles_repository/vehicles_repository.dart';

class VehiclesListArguments {
  final String title;
  final bool isAuction;
  VehiclesListArguments({
    required this.title,
    this.isAuction = false,
  });
}

class VehiclesListScreen extends StatefulWidget {
  const VehiclesListScreen({super.key});

  @override
  State<VehiclesListScreen> createState() => _VehiclesListScreenState();
}

class _VehiclesListScreenState extends State<VehiclesListScreen> with SingleTickerProviderStateMixin {
  late VehiclesListArguments arguments;
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late VehiclesBloc vehiclesBloc = context.read<VehiclesBloc>();
  late TabController tabController;
  final _scrollController = ScrollController();

  @override
  void initState() {
    vehiclesBloc.add(VehiclesFetched());
    _scrollController.addListener(_onScroll);
    setState(() {
      arguments = Get.arguments;
    });
    tabController = TabController(
      length: 2,
      vsync: this,
    );
    tabController.animation!.addListener(() {
      if (tabController.index == 0) {
        vehiclesBloc.add(
          const VehiclesFilterChanged(
            vehiclesFilter: VehiclesFilter(state: 'auction_unpaid'),
          ),
        );
      } else {
        vehiclesBloc.add(
          const VehiclesFilterChanged(
            vehiclesFilter: VehiclesFilter(state: 'auction_paid'),
          ),
        );
      }
      vehiclesBloc.add(VehiclesFetched());
    });
    super.initState();
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_onScroll)
      ..dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) vehiclesBloc.add(VehiclesFetched());
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  Future<void> _onRefresh() async {
    vehiclesBloc.add(VehiclesSetStateInitail());
    vehiclesBloc.add(VehiclesFetched());
  }

  Timer? _debounce;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) {
          vehiclesBloc.add(VehiclesSetStateInitail());
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(arguments.title),
          actions: [
            BlocBuilder<VehiclesBloc, VehiclesState>(
              builder: (context, state) {
                return Badge(
                  alignment: Directionality.of(context) == TextDirection.ltr ? Alignment.topLeft : Alignment.topRight,
                  isLabelVisible: state.vehiclesFilter.filterData != null
                      ? state.vehiclesFilter.filterData!.toMap().entries.isNotEmpty
                      : false,
                  label: Text(
                    state.vehiclesFilter.filterData != null
                        ? state.vehiclesFilter.filterData!.toMap().entries.length.toString()
                        : '0',
                  ),
                  child: IconButton(
                    onPressed: () async {
                      await showModalBottomSheet(
                        isScrollControlled: true,
                        useSafeArea: true,
                        context: context,
                        showDragHandle: true,
                        constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * .8),
                        builder: (context) => Padding(
                          padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
                          child: const VehicleFilterBottomSheet(),
                        ),
                      );
                    },
                    icon: const Icon(
                      Symbols.tune,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(
              width: 8,
            )
          ],
          // bottom: ,
        ),
        body: Column(
          children: [
            arguments.isAuction
                ? TabBar(
                    controller: tabController,
                    tabs: <Widget>[
                      BlocBuilder<VehiclesBloc, VehiclesState>(
                        builder: (context, state) {
                          return Tab(
                            child: Text(
                              "${tr!.auctionUnpaid} (${state.auctionUnpaidCount})",
                            ),
                          );
                        },
                      ),
                      BlocBuilder<VehiclesBloc, VehiclesState>(
                        builder: (context, state) {
                          return Tab(
                            child: Text(
                              "${tr!.auctionPaid} (${state.auctionPaidCount})",
                            ),
                          );
                        },
                      ),
                    ],
                  )
                : Container(),
            const SizedBox(
              height: 16,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
              ),
              child: BlocBuilder<VehiclesBloc, VehiclesState>(
                builder: (context, state) {
                  return CSearchBar(
                    label: "${tr!.searchItem(tr!.vehicles.toLowerCase())}...",
                    initailValue: state.vehiclesFilter.search,
                    onChanged: (String text) {
                      if (_debounce?.isActive ?? false) _debounce!.cancel();
                      _debounce = Timer(const Duration(seconds: 1), () {
                        vehiclesBloc.add(
                          VehiclesFilterChanged(
                            vehiclesFilter: state.vehiclesFilter.copyWith(search: text),
                          ),
                        );
                        vehiclesBloc.add(VehiclesSetStateInitail());
                        vehiclesBloc.add(VehiclesFetched());
                      });
                    },
                  );
                },
              ),
            ),
            const SizedBox(
              height: 16,
            ),
            Expanded(
              child: RefreshIndicator(
                onRefresh: _onRefresh,
                child: BlocBuilder<VehiclesBloc, VehiclesState>(
                  builder: (context, state) {
                    switch (state.status) {
                      case VehicleDataLoadingStatus.failure:
                        return Center(child: Text(tr!.failedToFetchItems(tr!.vehicles)));
                      case VehicleDataLoadingStatus.success:
                        if (state.vehicles.isEmpty) {
                          return NoItemsFound(
                            svgIcon: 'assets/svgs/cars-icon.svg',
                            onRefresh: _onRefresh,
                            itemName: tr!.vehicles,
                          );
                        }
                        return ListView.builder(
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: state.hasReachedMax ? state.vehicles.length : state.vehicles.length + 1,
                          itemBuilder: (context, index) {
                            if (index >= state.vehicles.length) {
                              return const VehicleItemCardSkeleton();
                            }
                            Vehicle vehicle = state.vehicles[index];
                            return VehicleItemCard(vehicle: vehicle);
                          },
                        );
                      case VehicleDataLoadingStatus.initial:
                        return ListView.builder(
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: 10,
                          itemBuilder: (context, index) {
                            return const VehicleItemCardSkeleton();
                          },
                        );
                    }
                  },
                ),
              ),
            ),
            const SizedBox(
              height: 16,
            ),
          ],
        ),
      ),
    );
  }
}
