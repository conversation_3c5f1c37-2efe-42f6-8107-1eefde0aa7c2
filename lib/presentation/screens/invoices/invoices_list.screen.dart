import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/route_manager.dart';
import 'package:invoices_repository/invoices_repository.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/logic/blocs/invoices/bloc/invoices_bloc.dart';
import 'package:pgl_mobile_app/presentation/widgets/bottom_sheets/filters/invoice_filter.bottom_sheet.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/invoices/invoice_item.card.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/invoices/invoice_item.card.skeleton.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_search_bar.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/no_items_found.widget.dart';

class InvoicesListArguments {
  final String title;

  InvoicesListArguments({
    required this.title,
  });
}

class InvoicesListScreen extends StatefulWidget {
  const InvoicesListScreen({super.key});

  @override
  State<InvoicesListScreen> createState() => _InvoicesListScreenState();
}

class _InvoicesListScreenState extends State<InvoicesListScreen> {
  late InvoicesListArguments arguments;
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late InvoicesBloc invoicesBloc = context.read<InvoicesBloc>();
  final _scrollController = ScrollController();

  @override
  void initState() {
    invoicesBloc.add(InvoicesFetched());
    _scrollController.addListener(_onScroll);
    setState(() {
      arguments = Get.arguments;
    });
    super.initState();
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_onScroll)
      ..dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) invoicesBloc.add(InvoicesFetched());
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  Future<void> _onRefresh() async {
    invoicesBloc.add(InvoicesSetStateInitail());
    invoicesBloc.add(InvoicesFetched());
  }

  Timer? _debounce;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) {
          invoicesBloc.add(InvoicesSetStateInitail());
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(arguments.title),
          actions: [
            BlocBuilder<InvoicesBloc, InvoicesState>(
              builder: (context, state) {
                return Badge(
                  alignment: Directionality.of(context) == TextDirection.ltr ? Alignment.topLeft : Alignment.topRight,
                  isLabelVisible: state.invoicesFilter.filterData != null
                      ? state.invoicesFilter.filterData!.toMap().entries.isNotEmpty
                      : false,
                  label: Text(
                    state.invoicesFilter.filterData != null
                        ? state.invoicesFilter.filterData!.toMap().entries.length.toString()
                        : '0',
                  ),
                  child: IconButton(
                    onPressed: () async {
                      await showModalBottomSheet(
                        isScrollControlled: true,
                        useSafeArea: true,
                        context: context,
                        showDragHandle: true,
                        constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * .8),
                        builder: (context) => Padding(
                          padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
                          child: const InvoiceFilterBottomSheet(),
                        ),
                      );
                    },
                    icon: const Icon(
                      Symbols.tune,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(
              width: 8,
            )
          ],
        ),
        body: Column(
          children: [
            const SizedBox(
              height: 16,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
              ),
              child: BlocBuilder<InvoicesBloc, InvoicesState>(
                builder: (context, state) {
                  return CSearchBar(
                    label: "${tr!.searchItem(tr!.invoices.toLowerCase())}...",
                    initailValue: state.invoicesFilter.search,
                    onChanged: (String text) {
                      if (_debounce?.isActive ?? false) _debounce!.cancel();
                      _debounce = Timer(const Duration(seconds: 1), () {
                        invoicesBloc.add(
                          InvoicesFilterChanged(
                            invoicesFilter: state.invoicesFilter.copyWith(search: text),
                          ),
                        );
                        invoicesBloc.add(InvoicesSetStateInitail());
                        invoicesBloc.add(InvoicesFetched());
                      });
                    },
                  );
                },
              ),
            ),
            const SizedBox(
              height: 16,
            ),
            Expanded(
              child: RefreshIndicator(
                onRefresh: _onRefresh,
                child: BlocBuilder<InvoicesBloc, InvoicesState>(
                  builder: (context, state) {
                    switch (state.status) {
                      case InvoicesDataLoadingStatus.failure:
                        return Center(child: Text(tr!.failedToFetchItems(tr!.invoices)));
                      case InvoicesDataLoadingStatus.success:
                        if (state.invoices.isEmpty) {
                          return NoItemsFound(
                            svgIcon: 'assets/svgs/invoice-icon.svg',
                            onRefresh: _onRefresh,
                            itemName: tr!.invoices,
                          );
                        }
                        return ListView.builder(
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: state.hasReachedMax ? state.invoices.length : state.invoices.length + 1,
                          itemBuilder: (context, index) {
                            if (index >= state.invoices.length) {
                              return const InvoiceItemCardSkeleton();
                            }
                            Invoice invoice = state.invoices[index];
                            return InvoiceItemCard(invoice: invoice);
                          },
                        );
                      case InvoicesDataLoadingStatus.initial:
                        return ListView.builder(
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: 10,
                          itemBuilder: (context, index) {
                            return const InvoiceItemCardSkeleton();
                          },
                        );
                    }
                  },
                ),
              ),
            ),
            const SizedBox(
              height: 16,
            ),
          ],
        ),
      ),
    );
  }
}
