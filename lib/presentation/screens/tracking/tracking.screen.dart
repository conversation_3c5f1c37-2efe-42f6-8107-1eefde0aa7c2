import 'dart:async';

import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:get/get.dart';
import 'package:pgl_mobile_app/exports/blocs.exports.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_search_bar.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/tracking/tracking_screen_body.widget.dart';
import 'package:tracking_repository/tracking_repository.dart';

class TrackingArguments {
  final String? query;
  TrackingArguments({
    this.query,
  });
}

class TrackingScreen extends StatefulWidget {
  const TrackingScreen({super.key});

  @override
  State<TrackingScreen> createState() => _TrackingScreenState();
}

class _TrackingScreenState extends State<TrackingScreen> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late TrackingArguments? arguments;
  late TrackingRepository trackingRepository = RepositoryProvider.of<TrackingRepository>(context);
  String _query = '';

  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    setState(() {
      arguments = Get.arguments;
      _query = arguments?.query ?? '';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(tr!.vehicleAndContainerTracking),
      ),
      body: Column(
        children: [
          const SizedBox(
            height: 16,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
            ),
            child: CSearchBar(
              label: tr!.vinLotContainerNumber,
              initailValue: arguments?.query ?? '',
              onChanged: (String text) {
                if (_debounce?.isActive ?? false) _debounce!.cancel();
                _debounce = Timer(const Duration(seconds: 1), () {
                  setState(() {
                    _query = text;
                  });
                });
              },
            ),
          ),
          Expanded(child: TrackingScreenBody(query: _query))
        ],
      ),
    );
  }
}
