import 'package:d_chart/d_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/route_manager.dart';
import 'package:mix_shipping_repository/mix_shipping_repository.dart';
import 'package:pgl_mobile_app/constants/routes.const.dart';
import 'package:pgl_mobile_app/logic/blocs/dashboard/bloc/dashboard_bloc.dart';
import 'package:pgl_mobile_app/logic/blocs/mix_shipping/bloc/mix_shipping_bloc.dart';
import 'package:pgl_mobile_app/logic/blocs/vehicles/bloc/vehicles_bloc.dart';
import 'package:pgl_mobile_app/presentation/screens/screens.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/category_item.card.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_donut_chart.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_search_bar.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

class MixShippingTab extends StatefulWidget {
  const MixShippingTab({super.key});

  @override
  State<MixShippingTab> createState() => _MixShippingTabState();
}

class _MixShippingTabState extends State<MixShippingTab> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late MixShippingBloc mixShippingBloc = context.read<MixShippingBloc>();

  @override
  void initState() {
    mixShippingBloc.add(LoadMixShippingCounts());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: 16,
        horizontal: 24,
      ),
      child: Column(
        children: [
          CSearchBar(
            label: "${tr!.searchItem(tr!.mixShipping.toLowerCase())}...",
            clearable: false,
            onSubmitted: (String text) {
              if (text.isNotEmpty) {
                mixShippingBloc.add(
                  MixShippingFilterChanged(
                    mixShippingFilter: MixShippingFilter(status: '', search: text),
                  ),
                );
                Get.toNamed(
                  mixShippingList,
                  arguments: MixShippingListArguments(
                    title: tr!.searchItem(tr!.invoices.toLowerCase()),
                  ),
                );
              }
            },
          ),
          const SizedBox(
            height: 16,
          ),
          BlocBuilder<DashboardBloc, DashboardState>(
            builder: (context, state) {
              return CDountChart(
                graphData: [
                  OrdinalData(
                    domain: tr!.allMixShipping,
                    measure: state.invoicePaymentStatus.allMixshipping,
                  ),
                  OrdinalData(
                    domain: tr!.paid,
                    measure: state.invoicePaymentStatus.paidMixshipping,
                  ),
                  OrdinalData(
                    domain: tr!.due,
                    measure: state.invoicePaymentStatus.dueMixshipping,
                  ),
                ],
              );
            },
          ),
          const SizedBox(
            height: 16,
          ),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                context.read<VehiclesBloc>().add(LoadVehiclesCounts());
              },
              child: Scrollbar(
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: BlocBuilder<MixShippingBloc, MixShippingState>(
                    builder: (context, state) {
                      return Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CategoryItemCard(
                                loading: state.countsLoading,
                                iconAssetName: 'assets/svgs/invoice-icon.svg',
                                title: tr!.all,
                                count: state.getallMixShippingTotal(),
                                onTap: () {
                                  mixShippingBloc.add(
                                    const MixShippingFilterChanged(
                                      mixShippingFilter: MixShippingFilter(status: ''),
                                    ),
                                  );
                                  Get.toNamed(
                                    mixShippingList,
                                    arguments: MixShippingListArguments(
                                      title: tr!.allMixShipping,
                                    ),
                                  );
                                },
                              ),
                              const SizedBox(
                                width: 16,
                              ),
                              CategoryItemCard(
                                loading: state.countsLoading,
                                iconAssetName: 'assets/svgs/invoice-open-icon.svg',
                                title: tr!.open,
                                count: state.openCount,
                                onTap: () {
                                  mixShippingBloc.add(
                                    const MixShippingFilterChanged(
                                      mixShippingFilter: MixShippingFilter(status: 'open'),
                                    ),
                                  );
                                  Get.toNamed(
                                    mixShippingList,
                                    arguments: MixShippingListArguments(
                                      title: tr!.open,
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                          const SizedBox(
                            height: 16,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CategoryItemCard(
                                loading: state.countsLoading,
                                iconAssetName: 'assets/svgs/invoice-past-due-icon.svg',
                                title: tr!.pastDue,
                                count: state.pastDueCount,
                                onTap: () {
                                  mixShippingBloc.add(
                                    const MixShippingFilterChanged(
                                      mixShippingFilter: MixShippingFilter(status: 'past_due'),
                                    ),
                                  );
                                  Get.toNamed(
                                    mixShippingList,
                                    arguments: MixShippingListArguments(
                                      title: tr!.pastDue,
                                    ),
                                  );
                                },
                              ),
                              const SizedBox(
                                width: 16,
                              ),
                              CategoryItemCard(
                                loading: state.countsLoading,
                                iconAssetName: 'assets/svgs/invoice-paid-icon.svg',
                                title: tr!.paid,
                                count: state.paidCount,
                                onTap: () {
                                  mixShippingBloc.add(
                                    const MixShippingFilterChanged(
                                      mixShippingFilter: MixShippingFilter(status: 'paid'),
                                    ),
                                  );
                                  Get.toNamed(
                                    mixShippingList,
                                    arguments: MixShippingListArguments(
                                      title: tr!.paid,
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
