import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/route_manager.dart';
import 'package:pgl_mobile_app/constants/routes.const.dart';
import 'package:pgl_mobile_app/logic/blocs/authentication/authentication.exports.dart';
import 'package:pgl_mobile_app/logic/blocs/dashboard/bloc/dashboard_bloc.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/logic/blocs/global_search/bloc/global_search_bloc.dart';
import 'package:pgl_mobile_app/logic/blocs/global_search/models/global_search_filter.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_search_bar.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/dashboard/dashboard_cards.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/dashboard/dashboard_notifications_summary.dart';

class DefaultTab extends StatefulWidget {
  const DefaultTab({
    super.key,
    required this.changeTab,
  });

  final void Function(int index) changeTab;

  @override
  State<DefaultTab> createState() => _DefaultTabState();
}

class _DefaultTabState extends State<DefaultTab> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  void initState() {
    context.read<DashboardBloc>().add(DashboardDataLoad());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: 16,
        horizontal: 24,
      ),
      child: Column(
        children: [
          CSearchBar(
            label: "${tr!.searchItem(tr!.all.toLowerCase())}...",
            onTap: () async {},
            clearable: false,
            onSubmitted: (String text) {
              if (text.isNotEmpty) {
                context.read<GlobalSearchBloc>().add(
                      GlobalSearchFilterChanged(
                        globalSearchFilter: GlobalSearchFilter(search: text),
                      ),
                    );
                Get.toNamed(
                  globalSearch,
                );
              }
            },
          ),
          const SizedBox(
            height: 16,
          ),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                context.read<DashboardBloc>().add(DashboardDataLoad());
              },
              child: Scrollbar(
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Column(
                    children: [
                      const DashBoardNotificationSummary(),
                      const SizedBox(
                        height: 16,
                      ),
                      DashboardVehiclesCard(changeTab: widget.changeTab),
                      DahsboardShipmentsCard(changeTab: widget.changeTab),
                      DashboardInvoicesCard(changeTab: widget.changeTab),
                      if (context.read<AuthenticationBloc>().state.user.mixShipping)
                        BlocBuilder<DashboardBloc, DashboardState>(
                          builder: (context, state) {
                            return state.countMixShipping > 0
                                ? DashboardMixShippingCard(changeTab: widget.changeTab)
                                : Container();
                          },
                        ),
                      // const DashboardStatementsCard(),
                      const DashboardInvoicesGraph(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
