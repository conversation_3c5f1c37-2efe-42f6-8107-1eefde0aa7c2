import 'package:d_chart/d_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/route_manager.dart';
import 'package:invoices_repository/invoices_repository.dart';
import 'package:pgl_mobile_app/constants/routes.const.dart';
import 'package:pgl_mobile_app/logic/blocs/dashboard/bloc/dashboard_bloc.dart';
import 'package:pgl_mobile_app/logic/blocs/invoices/bloc/invoices_bloc.dart';
import 'package:pgl_mobile_app/presentation/screens/screens.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/category_item.card.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_donut_chart.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_search_bar.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

class InvoicesTab extends StatefulWidget {
  const InvoicesTab({super.key});

  @override
  State<InvoicesTab> createState() => _InvoicesTabState();
}

class _InvoicesTabState extends State<InvoicesTab> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late InvoicesBloc invoicesBloc = context.read<InvoicesBloc>();

  @override
  void initState() {
    invoicesBloc.add(LoadInvoicesCounts());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: 16,
        horizontal: 24,
      ),
      child: Column(
        children: [
          CSearchBar(
            label: "${tr!.searchItem(tr!.invoices.toLowerCase())}...",
            clearable: false,
            onSubmitted: (String text) {
              if (text.isNotEmpty) {
                invoicesBloc.add(
                  InvoicesFilterChanged(
                    invoicesFilter: InvoicesFilter(status: '', search: text),
                  ),
                );
                Get.toNamed(
                  invoicesList,
                  arguments: InvoicesListArguments(
                    title: tr!.searchItem(tr!.invoices.toLowerCase()),
                  ),
                );
              }
            },
          ),
          const SizedBox(
            height: 16,
          ),
          BlocBuilder<DashboardBloc, DashboardState>(
            builder: (context, state) {
              return CDountChart(
                graphData: [
                  OrdinalData(
                    domain: tr!.allInvoices,
                    measure: state.invoicePaymentStatus.allInvoice,
                  ),
                  OrdinalData(
                    domain: tr!.paid,
                    measure: state.invoicePaymentStatus.paidInvoice,
                  ),
                  OrdinalData(
                    domain: tr!.due,
                    measure: state.invoicePaymentStatus.dueInvoice,
                  ),
                ],
              );
            },
          ),
          const SizedBox(
            height: 16,
          ),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                invoicesBloc.add(LoadInvoicesCounts());
              },
              child: Scrollbar(
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: BlocBuilder<InvoicesBloc, InvoicesState>(
                    builder: (context, state) {
                      return Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CategoryItemCard(
                                loading: state.countsLoading,
                                iconAssetName: 'assets/svgs/invoice-icon.svg',
                                title: tr!.all,
                                count: state.getAllInvoicesTotal(),
                                onTap: () {
                                  invoicesBloc.add(
                                    const InvoicesFilterChanged(
                                      invoicesFilter: InvoicesFilter(status: ''),
                                    ),
                                  );
                                  Get.toNamed(
                                    invoicesList,
                                    arguments: InvoicesListArguments(
                                      title: tr!.allInvoices,
                                    ),
                                  );
                                },
                              ),
                              const SizedBox(
                                width: 16,
                              ),
                              CategoryItemCard(
                                loading: state.countsLoading,
                                iconAssetName: 'assets/svgs/invoice-open-icon.svg',
                                title: tr!.open,
                                count: state.openCount,
                                onTap: () {
                                  invoicesBloc.add(
                                    const InvoicesFilterChanged(
                                      invoicesFilter: InvoicesFilter(status: 'open'),
                                    ),
                                  );
                                  Get.toNamed(
                                    invoicesList,
                                    arguments: InvoicesListArguments(
                                      title: tr!.open,
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                          const SizedBox(
                            height: 16,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CategoryItemCard(
                                loading: state.countsLoading,
                                iconAssetName: 'assets/svgs/invoice-past-due-icon.svg',
                                title: tr!.pastDue,
                                count: state.pastDueCount,
                                onTap: () {
                                  invoicesBloc.add(
                                    const InvoicesFilterChanged(
                                      invoicesFilter: InvoicesFilter(status: 'past_due'),
                                    ),
                                  );
                                  Get.toNamed(
                                    invoicesList,
                                    arguments: InvoicesListArguments(
                                      title: tr!.pastDue,
                                    ),
                                  );
                                },
                              ),
                              const SizedBox(
                                width: 16,
                              ),
                              CategoryItemCard(
                                loading: state.countsLoading,
                                iconAssetName: 'assets/svgs/invoice-paid-icon.svg',
                                title: tr!.paid,
                                count: state.paidCount,
                                onTap: () {
                                  invoicesBloc.add(
                                    const InvoicesFilterChanged(
                                      invoicesFilter: InvoicesFilter(status: 'paid'),
                                    ),
                                  );
                                  Get.toNamed(
                                    invoicesList,
                                    arguments: InvoicesListArguments(
                                      title: tr!.paid,
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
