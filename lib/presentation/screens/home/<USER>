import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/route_manager.dart';
import 'package:pgl_mobile_app/constants/routes.const.dart';
import 'package:pgl_mobile_app/logic/blocs/vehicles/bloc/vehicles_bloc.dart';
import 'package:pgl_mobile_app/presentation/screens/screens.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/category_item.card.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_search_bar.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:vehicles_repository/vehicles_repository.dart';

class VehiclesTab extends StatefulWidget {
  const VehiclesTab({super.key});

  @override
  State<VehiclesTab> createState() => _VehiclesTabState();
}

class _VehiclesTabState extends State<VehiclesTab> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late VehiclesBloc vehiclesBloc = context.read<VehiclesBloc>();

  @override
  void initState() {
    vehiclesBloc.add(LoadVehiclesCounts());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
      child: Column(
        children: [
          CSearchBar(
            label: "${tr!.searchItem(tr!.vehicles.toLowerCase())}...",
            clearable: false,
            onSubmitted: (String text) {
              if (text.isNotEmpty) {
                vehiclesBloc.add(
                  VehiclesFilterChanged(
                    vehiclesFilter: VehiclesFilter(state: '', search: text),
                  ),
                );
                Get.toNamed(
                  vehiclesList,
                  arguments: VehiclesListArguments(
                    title: tr!.searchItem(tr!.vehicles.toLowerCase()),
                  ),
                );
              }
            },
          ),
          const SizedBox(
            height: 16,
          ),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                vehiclesBloc.add(LoadVehiclesCounts());
              },
              child: Scrollbar(
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: BlocBuilder<VehiclesBloc, VehiclesState>(
                    builder: (context, state) {
                      return Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CategoryItemCard(
                                loading: state.countsLoading,
                                iconAssetName: 'assets/svgs/cars-icon.svg',
                                title: tr!.allVehicles,
                                count: state.getAllCarsTotal(),
                                maxLines: 1,
                                onTap: () {
                                  vehiclesBloc.add(
                                    const VehiclesFilterChanged(
                                      vehiclesFilter: VehiclesFilter(state: ''),
                                    ),
                                  );
                                  Get.toNamed(
                                    vehiclesList,
                                    arguments: VehiclesListArguments(
                                      title: tr!.allVehicles,
                                    ),
                                  );
                                },
                              ),
                              const SizedBox(
                                width: 16,
                              ),
                              CategoryItemCard(
                                loading: state.countsLoading,
                                iconAssetName: 'assets/svgs/invoice-icon.svg',
                                title: tr!.auction,
                                count: state.getAuctionTotal(),
                                maxLines: 1,
                                onTap: () {
                                  vehiclesBloc.add(
                                    const VehiclesFilterChanged(
                                      vehiclesFilter: VehiclesFilter(state: 'auction_unpaid'),
                                    ),
                                  );
                                  Get.toNamed(
                                    vehiclesList,
                                    arguments: VehiclesListArguments(
                                      title: tr!.auction,
                                      isAuction: true,
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                          const SizedBox(
                            height: 16,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CategoryItemCard(
                                loading: state.countsLoading,
                                iconAssetName: 'assets/svgs/truck-icon.svg',
                                title: tr!.onTheWay,
                                count: state.onTheWayCount,
                                maxLines: 1,
                                onTap: () {
                                  vehiclesBloc.add(
                                    const VehiclesFilterChanged(
                                      vehiclesFilter: VehiclesFilter(state: 'on_the_way'),
                                    ),
                                  );
                                  Get.toNamed(
                                    vehiclesList,
                                    arguments: VehiclesListArguments(
                                      title: tr!.onTheWay,
                                    ),
                                  );
                                },
                              ),
                              const SizedBox(
                                width: 16,
                              ),
                              CategoryItemCard(
                                loading: state.countsLoading,
                                iconAssetName: 'assets/svgs/car-building-icon.svg',
                                title: tr!.onHandNoTitle,
                                count: state.onHandNoTitleCount,
                                maxLines: 1,
                                onTap: () {
                                  vehiclesBloc.add(
                                    const VehiclesFilterChanged(
                                      vehiclesFilter: VehiclesFilter(state: 'on_hand_no_title'),
                                    ),
                                  );
                                  Get.toNamed(
                                    vehiclesList,
                                    arguments: VehiclesListArguments(
                                      title: tr!.onHandNoTitle,
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                          const SizedBox(
                            height: 16,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CategoryItemCard(
                                loading: state.countsLoading,
                                iconAssetName: 'assets/svgs/car-icon.svg',
                                title: tr!.onHandWithTitle,
                                count: state.onHandWithTitleCount,
                                maxLines: 1,
                                onTap: () {
                                  vehiclesBloc.add(
                                    const VehiclesFilterChanged(
                                      vehiclesFilter: VehiclesFilter(state: 'on_hand_with_title'),
                                    ),
                                  );
                                  Get.toNamed(
                                    vehiclesList,
                                    arguments: VehiclesListArguments(
                                      title: tr!.onHandWithTitle,
                                    ),
                                  );
                                },
                              ),
                              const SizedBox(
                                width: 16,
                              ),
                              CategoryItemCard(
                                loading: state.countsLoading,
                                iconAssetName: 'assets/svgs/car-shipped-icon.svg',
                                title: tr!.onHandWithLoad,
                                count: state.onHandWithLoadCount,
                                maxLines: 1,
                                onTap: () {
                                  vehiclesBloc.add(
                                    const VehiclesFilterChanged(
                                      vehiclesFilter: VehiclesFilter(state: 'on_hand_with_load'),
                                    ),
                                  );
                                  Get.toNamed(
                                    vehiclesList,
                                    arguments: VehiclesListArguments(
                                      title: tr!.onHandWithLoad,
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                          const SizedBox(
                            height: 16,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CategoryItemCard(
                                loading: state.countsLoading,
                                iconAssetName: 'assets/svgs/car-shipped-icon.svg',
                                title: tr!.shipped,
                                count: state.shippedCount,
                                maxLines: 1,
                                onTap: () {
                                  vehiclesBloc.add(
                                    const VehiclesFilterChanged(
                                      vehiclesFilter: VehiclesFilter(state: 'shipped'),
                                    ),
                                  );
                                  Get.toNamed(
                                    vehiclesList,
                                    arguments: VehiclesListArguments(
                                      title: tr!.shipped,
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
