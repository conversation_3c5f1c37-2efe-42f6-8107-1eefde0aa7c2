import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:pgl_mobile_app/constants/routes.const.dart';
import 'package:pgl_mobile_app/logic/blocs/shipment/bloc/shipment_bloc.dart';
import 'package:pgl_mobile_app/presentation/screens/screens.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/category_item.card.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_search_bar.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:shipments_repository/shipments_repository.dart';

class ShipmentsTab extends StatefulWidget {
  const ShipmentsTab({super.key});

  @override
  State<ShipmentsTab> createState() => _ShipmentsTabState();
}

class _ShipmentsTabState extends State<ShipmentsTab> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late ShipmentBloc shipmentBloc = context.read<ShipmentBloc>();

  @override
  void initState() {
    shipmentBloc.add(LoadShipmentCounts());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: 16,
        horizontal: 24,
      ),
      child: Column(
        children: [
          CSearchBar(
            label: "${tr!.searchItem(tr!.shipments.toLowerCase())}...",
            clearable: false,
            onSubmitted: (String text) {
              if (text.isNotEmpty) {
                shipmentBloc.add(
                  ShipmentsFilterChanged(
                    shipmentsFilter: ShipmentsFilter(state: '', search: text),
                  ),
                );
                Get.toNamed(
                  shipmentsList,
                  arguments: ShipmentsListArguments(
                    title: tr!.searchItem(tr!.shipments.toLowerCase()),
                  ),
                );
              }
            },
          ),
          const SizedBox(
            height: 16,
          ),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                shipmentBloc.add(LoadShipmentCounts());
              },
              child: Scrollbar(
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: BlocBuilder<ShipmentBloc, ShipmentsState>(builder: (context, state) {
                    return Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CategoryItemCard(
                              iconAssetName: 'assets/svgs/cars-icon.svg',
                              title: tr!.all,
                              count: state.getShipmentTotal(),
                              loading: state.countsLoading,
                              onTap: () {
                                shipmentBloc.add(
                                  const ShipmentsFilterChanged(
                                    shipmentsFilter: ShipmentsFilter(state: ''),
                                  ),
                                );
                                Get.toNamed(
                                  shipmentsList,
                                  arguments: ShipmentsListArguments(
                                    title: tr!.allShipments,
                                  ),
                                );
                              },
                            ),
                            const SizedBox(
                              width: 16,
                            ),
                            CategoryItemCard(
                              iconAssetName: 'assets/svgs/car-2-icon.svg',
                              title: tr!.atLoading,
                              count: state.atLoading,
                              loading: state.countsLoading,
                              onTap: () {
                                shipmentBloc.add(
                                  const ShipmentsFilterChanged(
                                    shipmentsFilter: ShipmentsFilter(state: 'at_loading'),
                                  ),
                                );
                                Get.toNamed(
                                  shipmentsList,
                                  arguments: ShipmentsListArguments(
                                    title: tr!.atLoading,
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                        const SizedBox(
                          height: 16,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CategoryItemCard(
                              iconAssetName: 'assets/svgs/truck-icon.svg',
                              title: tr!.onTheWay,
                              count: state.onTheWay,
                              loading: state.countsLoading,
                              onTap: () {
                                shipmentBloc.add(
                                  const ShipmentsFilterChanged(
                                    shipmentsFilter: ShipmentsFilter(state: 'on_the_way'),
                                  ),
                                );
                                Get.toNamed(
                                  shipmentsList,
                                  arguments: ShipmentsListArguments(
                                    title: tr!.onTheWay,
                                  ),
                                );
                              },
                            ),
                            const SizedBox(
                              width: 16,
                            ),
                            CategoryItemCard(
                              iconAssetName: 'assets/svgs/car-shipped-icon.svg',
                              title: tr!.arrived,
                              count: state.arrived,
                              loading: state.countsLoading,
                              onTap: () {
                                shipmentBloc.add(
                                  const ShipmentsFilterChanged(
                                    shipmentsFilter: ShipmentsFilter(state: 'arrived'),
                                  ),
                                );
                                Get.toNamed(
                                  shipmentsList,
                                  arguments: ShipmentsListArguments(
                                    title: tr!.arrived,
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ],
                    );
                  }),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
