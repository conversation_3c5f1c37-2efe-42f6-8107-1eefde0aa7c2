import 'package:authentication_repository/authentication_repository.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:pgl_mobile_app/logic/blocs/authentication/authentication.exports.dart';
import 'package:pgl_mobile_app/logic/blocs/login/bloc/login_bloc.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/auth/login_form.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/auth/login_top.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_toast.widget.dart';
import 'package:url_launcher/url_launcher.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late FToast fToast;
  late CheckForUpdates checkForUpdates;
  @override
  initState() {
    fToast = FToast();
    fToast.init(context);
    checkForUpdates = CheckForUpdates.init(context: context)..checkForUpdate();
    super.initState();
  }

  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: Theme.of(context).colorScheme.errorContainer,
        content: Text(
          message,
          style: TextStyle(
            color: Theme.of(context).colorScheme.onErrorContainer,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  DateTime? currentBackPressTime;

  void _onPopInvoked() {
    DateTime now = DateTime.now();
    if (currentBackPressTime == null || now.difference(currentBackPressTime ?? now) > const Duration(seconds: 2)) {
      currentBackPressTime = now;
      fToast.showToast(
        child: CToast(
          text: tr!.tapBackAgainToLeave,
        ),
        gravity: ToastGravity.BOTTOM,
        toastDuration: const Duration(seconds: 2),
      );
    } else {
      SystemChannels.platform.invokeMethod('SystemNavigator.pop');
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) {
        _onPopInvoked();
      },
      child: BlocListener<AuthenticationBloc, AuthenticationState>(
        listenWhen: (previous, current) => previous.error != current.error,
        listener: (context, state) async {
          switch (state.error) {
            case AuthenticationError.unexpectedError:
              _showErrorSnackbar(tr!.unExpectedErrorOccured);
              context.read<AuthenticationBloc>().add(const AuthenticationErrorChanged(AuthenticationError.none));
              break;
            case AuthenticationError.invalidCreds:
              _showErrorSnackbar(tr!.invalidCreds);
              context.read<AuthenticationBloc>().add(const AuthenticationErrorChanged(AuthenticationError.none));
              break;
            case AuthenticationError.none:
              context.read<AuthenticationBloc>().add(const AuthenticationErrorChanged(AuthenticationError.none));
              break;
          }
        },
        child: Scaffold(
          resizeToAvoidBottomInset: true,
          body: BlocProvider(
            create: (context) => LoginBloc(
              authenticationRepository: RepositoryProvider.of<AuthenticationRepository>(context),
            ),
            child: SingleChildScrollView(
              child: SizedBox(
                height: MediaQuery.of(context).size.height,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Column(
                      children: [
                        LoginTop(),
                        LoginForm(),
                      ],
                    ),
                    const Spacer(),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 28),
                      child: GestureDetector(
                        onTap: () {
                          launchUrl(Uri.parse('https://peacegl.com'));
                        },
                        child: Text(
                          tr!.copyRight,
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.primary,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
