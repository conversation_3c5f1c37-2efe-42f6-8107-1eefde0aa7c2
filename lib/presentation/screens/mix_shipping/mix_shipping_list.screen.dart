import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/route_manager.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:mix_shipping_repository/mix_shipping_repository.dart';
import 'package:pgl_mobile_app/logic/blocs/mix_shipping/bloc/mix_shipping_bloc.dart';
import 'package:pgl_mobile_app/presentation/widgets/bottom_sheets/filters/mix_shipping_filter.bottom_sheet.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/invoices/invoice_item.card.skeleton.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/mix_shipping/mix_shipping_item.card.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_search_bar.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/no_items_found.widget.dart';

class MixShippingListArguments {
  final String title;

  MixShippingListArguments({
    required this.title,
  });
}

class MixShippingListScreen extends StatefulWidget {
  const MixShippingListScreen({super.key});

  @override
  State<MixShippingListScreen> createState() => _MixShippingListScreenState();
}

class _MixShippingListScreenState extends State<MixShippingListScreen> {
  late MixShippingListArguments arguments;
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late MixShippingBloc mixShippingBloc = context.read<MixShippingBloc>();
  final _scrollController = ScrollController();

  @override
  void initState() {
    mixShippingBloc.add(MixShippingFetched());
    _scrollController.addListener(_onScroll);
    setState(() {
      arguments = Get.arguments;
    });
    super.initState();
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_onScroll)
      ..dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) mixShippingBloc.add(MixShippingFetched());
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  Future<void> _onRefresh() async {
    mixShippingBloc.add(MixShippingSetStateInitail());
    mixShippingBloc.add(MixShippingFetched());
  }

  Timer? _debounce;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) {
          mixShippingBloc.add(MixShippingSetStateInitail());
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(arguments.title),
          actions: [
            BlocBuilder<MixShippingBloc, MixShippingState>(
              builder: (context, state) {
                return Badge(
                  alignment: Directionality.of(context) == TextDirection.ltr ? Alignment.topLeft : Alignment.topRight,
                  isLabelVisible: state.mixShippingFilter.filterData != null
                      ? state.mixShippingFilter.filterData!.toMap().entries.isNotEmpty
                      : false,
                  label: Text(
                    state.mixShippingFilter.filterData != null
                        ? state.mixShippingFilter.filterData!.toMap().entries.length.toString()
                        : '0',
                  ),
                  child: IconButton(
                    onPressed: () async {
                      await showModalBottomSheet(
                        isScrollControlled: true,
                        useSafeArea: true,
                        context: context,
                        showDragHandle: true,
                        constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * .8),
                        builder: (context) => Padding(
                          padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
                          child: const MixShippingFilterBottomSheet(),
                        ),
                      );
                    },
                    icon: const Icon(
                      Symbols.tune,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(
              width: 8,
            )
          ],
        ),
        body: Column(
          children: [
            const SizedBox(
              height: 16,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
              ),
              child: BlocBuilder<MixShippingBloc, MixShippingState>(
                builder: (context, state) {
                  return CSearchBar(
                    label: "${tr!.searchItem(tr!.mixShipping.toLowerCase())}...",
                    initailValue: state.mixShippingFilter.search,
                    onChanged: (String text) {
                      if (_debounce?.isActive ?? false) _debounce!.cancel();
                      _debounce = Timer(const Duration(seconds: 1), () {
                        mixShippingBloc.add(
                          MixShippingFilterChanged(
                            mixShippingFilter: state.mixShippingFilter.copyWith(search: text),
                          ),
                        );
                        mixShippingBloc.add(MixShippingSetStateInitail());
                        mixShippingBloc.add(MixShippingFetched());
                      });
                    },
                  );
                },
              ),
            ),
            const SizedBox(
              height: 16,
            ),
            Expanded(
              child: RefreshIndicator(
                onRefresh: _onRefresh,
                child: BlocBuilder<MixShippingBloc, MixShippingState>(
                  builder: (context, state) {
                    switch (state.status) {
                      case MixShippingDataLoadingStatus.failure:
                        return Center(child: Text(tr!.failedToFetchItems(tr!.mixShipping)));
                      case MixShippingDataLoadingStatus.success:
                        if (state.mixShipping.isEmpty) {
                          return NoItemsFound(
                            svgIcon: 'assets/svgs/invoice-icon.svg',
                            onRefresh: _onRefresh,
                            itemName: tr!.mixShipping,
                          );
                        }
                        return ListView.builder(
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: state.hasReachedMax ? state.mixShipping.length : state.mixShipping.length + 1,
                          itemBuilder: (context, index) {
                            if (index >= state.mixShipping.length) {
                              return const InvoiceItemCardSkeleton();
                            }
                            MixShipping mixShipping = state.mixShipping[index];
                            return MixshippingItemCard(mixShipping: mixShipping);
                          },
                        );
                      case MixShippingDataLoadingStatus.initial:
                        return ListView.builder(
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: 10,
                          itemBuilder: (context, index) {
                            return const InvoiceItemCardSkeleton();
                          },
                        );
                    }
                  },
                ),
              ),
            ),
            const SizedBox(
              height: 16,
            ),
          ],
        ),
      ),
    );
  }
}
