import 'dart:async';

import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/exports/blocs.exports.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/auction_payments/auction_payment_item.card.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/payments/payment_item.card.skeleton.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_search_bar.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/no_items_found.widget.dart';

class AuctionPaymentsListScreen extends StatefulWidget {
  const AuctionPaymentsListScreen({super.key});

  @override
  State<AuctionPaymentsListScreen> createState() => _AuctionPaymentsListScreenState();
}

class _AuctionPaymentsListScreenState extends State<AuctionPaymentsListScreen> with SingleTickerProviderStateMixin {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late AuctionPaymentsBloc auctionPaymentsBloc = context.read<AuctionPaymentsBloc>();
  final _scrollController = ScrollController();

  @override
  void initState() {
    auctionPaymentsBloc.add(AuctionPaymentsFetched());
    _scrollController.addListener(_onScroll);
    super.initState();
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_onScroll)
      ..dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) auctionPaymentsBloc.add(AuctionPaymentsFetched());
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  Future<void> _onRefresh() async {
    auctionPaymentsBloc.add(AuctionPaymentsSetStateInitail());
    auctionPaymentsBloc.add(AuctionPaymentsFetched());
  }

  Timer? _debounce;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) {
          auctionPaymentsBloc.add(AuctionPaymentsSetStateInitail());
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            tr!.auctionPayments,
          ),
          actions: const [],
        ),
        body: Column(
          children: [
            const SizedBox(
              height: 16,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
              ),
              child: BlocBuilder<AuctionPaymentsBloc, AuctionPaymentsState>(
                builder: (context, state) {
                  return CSearchBar(
                    label: "${tr!.searchItem(tr!.auctionPayments.toLowerCase())}...",
                    initailValue: state.auctionPaymentsFilter.search,
                    onChanged: (String text) {
                      if (_debounce?.isActive ?? false) _debounce!.cancel();
                      _debounce = Timer(const Duration(seconds: 1), () {
                        auctionPaymentsBloc.add(
                          AuctionPaymentsFilterChanged(
                            auctionPaymentsFilter: state.auctionPaymentsFilter.copyWith(search: text),
                          ),
                        );
                        auctionPaymentsBloc.add(AuctionPaymentsSetStateInitail());
                        auctionPaymentsBloc.add(AuctionPaymentsFetched());
                      });
                    },
                  );
                },
              ),
            ),
            const SizedBox(
              height: 16,
            ),
            Expanded(
              child: RefreshIndicator(
                onRefresh: _onRefresh,
                child: BlocBuilder<AuctionPaymentsBloc, AuctionPaymentsState>(
                  builder: (context, state) {
                    switch (state.status) {
                      case AuctionPaymentsDataLoadingStatus.failure:
                        return Center(child: Text(tr!.failedToFetchItems(tr!.auctionPayments)));
                      case AuctionPaymentsDataLoadingStatus.success:
                        if (state.auctionPayments.isEmpty) {
                          return NoItemsFound(
                            svgIcon: 'assets/svgs/invoice-open-icon.svg',
                            onRefresh: _onRefresh,
                            itemName: tr!.auctionPayments,
                          );
                        }
                        return ListView.builder(
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount:
                              state.hasReachedMax ? state.auctionPayments.length : state.auctionPayments.length + 1,
                          itemBuilder: (context, index) {
                            if (index >= state.auctionPayments.length) {
                              return const PaymentsItemCardSkeleton();
                            }
                            AuctionPaymentModel auctionPayment = state.auctionPayments[index];
                            return AuctionPaymentItemCard(auctionPayment: auctionPayment);
                          },
                        );
                      case AuctionPaymentsDataLoadingStatus.initial:
                        return ListView.builder(
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: 10,
                          itemBuilder: (context, index) {
                            return const PaymentsItemCardSkeleton();
                          },
                        );
                    }
                  },
                ),
              ),
            ),
            const SizedBox(
              height: 16,
            ),
          ],
        ),
      ),
    );
  }
}
