import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/constants/routes.const.dart';
import 'package:pgl_mobile_app/firebase_messaging_config.dart';
import 'package:pgl_mobile_app/logic/blocs/authentication/authentication.exports.dart';
import 'package:pgl_mobile_app/logic/blocs/connectivity/bloc/connectivity_bloc.dart';
import 'package:pgl_mobile_app/logic/blocs/dashboard/bloc/dashboard_bloc.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/presentation/screens/home/<USER>';
import 'package:pgl_mobile_app/presentation/widgets/common/c_toast.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/layout/c_bottom_navigation.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/layout/c_navigation_drawer.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/widgets/settings/home_langauge.widget.dart';

class TabItem {
  final Widget tab;
  final String title;

  TabItem({
    required this.tab,
    required this.title,
  });
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({
    super.key,
  });

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  final GlobalKey<ScaffoldState> _key = GlobalKey<ScaffoldState>();
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late TabController _tabController;
  late FirebaseMessagingConfig _firebaseMessagingConfig;
  late FToast fToast;
  late CheckForUpdates checkForUpdates;

  int _selectedTab = 0;
  int _selectedNavigation = 1;

  late final List<TabItem> _tabItems = [
    TabItem(
      tab: DefaultTab(
        changeTab: _changeCurrentTab,
      ),
      title: tr!.home,
    ),
    TabItem(tab: const VehiclesTab(), title: tr!.vehicles),
    TabItem(tab: const ShipmentsTab(), title: tr!.shipments),
    TabItem(tab: const InvoicesTab(), title: tr!.invoices),
    if (context.read<AuthenticationBloc>().state.user.mixShipping)
      TabItem(tab: const MixShippingTab(), title: tr!.mixShipping),
  ];

  @override
  void initState() {
    super.initState();
    checkForUpdates = CheckForUpdates.init(context: context)..checkForUpdate();
    _firebaseMessagingConfig = FirebaseMessagingConfig.init(context: context);
    context.read<ConnectivityBloc>().add(ConnectivityResultInitial());
    _tabController = TabController(
      vsync: this,
      length: context.read<AuthenticationBloc>().state.user.mixShipping ? 5 : 4,
    );
    _tabController.animation!.addListener(() {
      setState(() {
        _selectedTab =
            (_tabController.indexIsChanging) ? _tabController.index : _tabController.animation!.value.round();
        _selectedNavigation =
            ((_tabController.indexIsChanging) ? _tabController.index : _tabController.animation!.value.round()) + 1;
      });
    });
    fToast = FToast();
    fToast.init(context);
  }

  void _changeCurrentTab(int value) {
    _tabController.animateTo(value);
    setState(() {
      _selectedTab = value;
    });
  }

  void _changeSelectedNavigation(int value) {
    setState(() {
      _selectedNavigation = value;
    });
  }

  void _closeDrawer() {
    if (_key.currentState != null) {
      if (_key.currentState!.isDrawerOpen) {
        _key.currentState!.closeDrawer();
        return;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) {
        _onPopInvoked();
      },
      child: BlocConsumer<ConnectivityBloc, ConnectivityState>(listener: (context, state) {
        switch (state.result) {
          case ConnectivityResult.none:
            _showNoConnectionBanner();
            break;
          default:
            _showConnectionBanner();
            break;
        }
      }, builder: (context, state) {
        return Scaffold(
          key: _key,
          appBar: AppBar(
            title: Text(_tabItems[_selectedTab].title),
            actions: [
              const HomeLanguage(),
              BlocBuilder<DashboardBloc, DashboardState>(
                builder: (context, state) {
                  return Badge(
                    alignment: Directionality.of(context) == TextDirection.ltr ? Alignment.topLeft : Alignment.topRight,
                    label: Text("${state.countNotificationsAll > 10 ? '9+' : state.countNotificationsAll}"),
                    isLabelVisible: state.countNotificationsAll > 0,
                    child: IconButton(
                      onPressed: () {
                        Get.toNamed(notifications);
                      },
                      icon: const Icon(
                        Symbols.notifications_active,
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(
                width: 8,
              )
            ],
          ),
          drawer: CNavigationDrawer(
            changeTab: _changeCurrentTab,
            selectedNavigation: _selectedNavigation,
            changeSelectedNavigation: _changeSelectedNavigation,
            firebaseMessagingConfig: _firebaseMessagingConfig,
            closeDrawer: _closeDrawer,
          ),
          body: TabBarView(
            controller: _tabController,
            children: _tabItems.map<Widget>((TabItem e) => e.tab).toList(),
          ),
          bottomNavigationBar: CBottomNavigation(
            currentTab: _selectedTab,
            changeTab: _changeCurrentTab,
          ),
        );
      }),
    );
  }

  DateTime? currentBackPressTime;

  void _onPopInvoked() {
    // close the drawer if open
    _closeDrawer();
    // close the app if clicked twice
    DateTime now = DateTime.now();
    if (currentBackPressTime == null || now.difference(currentBackPressTime ?? now) > const Duration(seconds: 2)) {
      currentBackPressTime = now;
      fToast.showToast(
        child: CToast(
          text: tr!.tapBackAgainToLeave,
        ),
        gravity: ToastGravity.BOTTOM,
        toastDuration: const Duration(seconds: 2),
      );
    } else {
      SystemChannels.platform.invokeMethod('SystemNavigator.pop');
    }
  }

  void _showNoConnectionBanner() {
    fToast.showToast(
      child: CToast(
        text: tr!.noConnectionInfoMightBeOutdated,
        before: const Icon(
          Symbols.wifi_off,
          weight: 700,
          size: 18,
          color: Colors.white,
        ),
      ),
      gravity: ToastGravity.BOTTOM,
      toastDuration: const Duration(seconds: 4),
    );
  }

  void _showConnectionBanner() {
    fToast.showToast(
      child: CToast(
        text: tr!.connectionReestablished,
        before: const Icon(
          Symbols.wifi,
          weight: 700,
          size: 18,
          color: Colors.white,
        ),
      ),
      gravity: ToastGravity.BOTTOM,
      toastDuration: const Duration(seconds: 4),
    );
  }

  @override
  void dispose() {
    _firebaseMessagingConfig.dispose();
    _tabController.dispose();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;
}
