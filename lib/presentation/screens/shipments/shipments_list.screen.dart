import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:get/route_manager.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/logic/blocs/shipment/bloc/shipment_bloc.dart';
import 'package:pgl_mobile_app/presentation/widgets/bottom_sheets/filters/shipment_filter.bottom_sheet.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/shipment_item.card.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/vehicle_item.card.skeleton.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_search_bar.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/no_items_found.widget.dart';
import 'package:shipments_repository/shipments_repository.dart';

class ShipmentsListArguments {
  final String title;

  ShipmentsListArguments({
    required this.title,
  });
}

class ShipmentsListScreen extends StatefulWidget {
  const ShipmentsListScreen({super.key});

  @override
  State<ShipmentsListScreen> createState() => _ShipmentsListScreenState();
}

class _ShipmentsListScreenState extends State<ShipmentsListScreen> {
  late ShipmentsListArguments arguments;
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late ShipmentBloc shipmentBloc = context.read<ShipmentBloc>();
  final _scrollController = ScrollController();

  @override
  void initState() {
    shipmentBloc.add(ShipmentsFetched());
    _scrollController.addListener(_onScroll);
    setState(() {
      arguments = Get.arguments;
    });
    super.initState();
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_onScroll)
      ..dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) shipmentBloc.add(ShipmentsFetched());
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  Future<void> _onRefresh() async {
    shipmentBloc.add(ShipmentsSetStateInitail());
    shipmentBloc.add(ShipmentsFetched());
  }

  Timer? _debounce;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(arguments.title),
        actions: [
          BlocBuilder<ShipmentBloc, ShipmentsState>(
            builder: (context, state) {
              return Badge(
                alignment: Directionality.of(context) == TextDirection.ltr ? Alignment.topLeft : Alignment.topRight,
                isLabelVisible: state.shipmentsFilter.filterData != null
                    ? state.shipmentsFilter.filterData!.toMap().entries.isNotEmpty
                    : false,
                label: Text(
                  state.shipmentsFilter.filterData != null
                      ? state.shipmentsFilter.filterData!.toMap().entries.length.toString()
                      : '0',
                ),
                child: IconButton(
                  onPressed: () async {
                    await showModalBottomSheet(
                      isScrollControlled: true,
                      useSafeArea: true,
                      context: context,
                      showDragHandle: true,
                      constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * .8),
                      builder: (context) => Padding(
                        padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
                        child: const ShipmentFilterBottomSheet(),
                      ),
                    );
                  },
                  icon: const Icon(
                    Symbols.tune,
                  ),
                ),
              );
            },
          ),
          const SizedBox(
            width: 8,
          )
        ],
      ),
      body: Column(
        children: [
          const SizedBox(
            height: 16,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
            ),
            child: BlocBuilder<ShipmentBloc, ShipmentsState>(
              builder: (context, state) {
                return CSearchBar(
                  label: "${tr!.searchItem(tr!.shipments.toLowerCase())}...",
                  initailValue: state.shipmentsFilter.search,
                  onChanged: (String text) {
                    if (_debounce?.isActive ?? false) _debounce!.cancel();
                    _debounce = Timer(const Duration(seconds: 1), () {
                      shipmentBloc.add(
                        ShipmentsFilterChanged(
                          shipmentsFilter: state.shipmentsFilter.copyWith(search: text),
                        ),
                      );
                      shipmentBloc.add(ShipmentsSetStateInitail());
                      shipmentBloc.add(ShipmentsFetched());
                    });
                  },
                );
              },
            ),
          ),
          const SizedBox(
            height: 16,
          ),
          Expanded(
            child: RefreshIndicator(
              onRefresh: _onRefresh,
              child: BlocBuilder<ShipmentBloc, ShipmentsState>(
                builder: (context, state) {
                  switch (state.status) {
                    case ShipmentDataLoadingStatus.failure:
                      return Center(child: Text(tr!.failedToFetchItems(tr!.shipments)));
                    case ShipmentDataLoadingStatus.success:
                      if (state.shipments.isEmpty) {
                        return NoItemsFound(
                          svgIcon: 'assets/svgs/truck-icon.svg',
                          onRefresh: _onRefresh,
                          itemName: tr!.shipments,
                        );
                      }
                      return ListView.builder(
                        // shrinkWrap: true,
                        controller: _scrollController,
                        physics: const AlwaysScrollableScrollPhysics(),
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: state.hasReachedMax ? state.shipments.length : state.shipments.length + 1,
                        itemBuilder: (context, index) {
                          if (index >= state.shipments.length) {
                            return const VehicleItemCardSkeleton();
                          }
                          Shipment shipment = state.shipments[index];
                          return ShipmentItemCard(shipment: shipment);
                        },
                      );
                    case ShipmentDataLoadingStatus.initial:
                      return ListView.builder(
                        controller: _scrollController,
                        physics: const AlwaysScrollableScrollPhysics(),
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: 10,
                        itemBuilder: (context, index) {
                          return const VehicleItemCardSkeleton();
                        },
                      );
                  }
                },
              ),
            ),
          ),
          const SizedBox(
            height: 16,
          ),
        ],
      ),
    );
  }
}
