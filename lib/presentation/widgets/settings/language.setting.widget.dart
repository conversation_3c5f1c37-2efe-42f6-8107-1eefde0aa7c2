import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/app.dart';
import 'package:pgl_mobile_app/configs/themes/theme.dart';
import 'package:pgl_mobile_app/constants/constants.dart';
import 'package:pgl_mobile_app/logic/classes/select_item.class.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_select_bottom_sheet.widget.dart';
import 'package:provider/provider.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

class LanguageSettingTile extends StatefulWidget {
  const LanguageSettingTile({super.key});

  @override
  State<LanguageSettingTile> createState() => _LanguageSettingTileState();
}

Map<String, Map<String, String>> languageNames = {
  'en': {
    'en': 'English',
    'native': 'English',
  },
  'ru': {
    'en': 'Russian',
    'native': 'Русский',
  },
  'ka': {
    'en': 'Georgian',
    'native': 'ქართული',
  },
  'ar': {
    'en': 'Arabic',
    'native': 'العربية',
  },
};

List<Locale> locals = [
  const Locale('en'),
  const Locale('ru'),
  const Locale('ka'),
  const Locale('ar'),
];

List<SelectItem> langSelectItems = locals
    .map<SelectItem>(
      (Locale locale) => SelectItem(
          value: locale.languageCode,
          label: languageNames[locale.languageCode]?['native'] ?? '',
          labelWidget: Row(
            children: [
              SvgPicture.asset(
                'assets/flags/${locale.languageCode}.svg',
                width: 20,
              ),
              const SizedBox(
                width: 8,
              ),
              Text(
                languageNames[locale.languageCode]?['native'] ?? '',
                style: const TextStyle(fontSize: 14),
              )
            ],
          )),
    )
    .toList();

class _LanguageSettingTileState extends State<LanguageSettingTile> {
  late AppLocalizations? tr = AppLocalizations.of(context);
  late String currentLocale = Get.deviceLocale?.languageCode ?? 'en';
  late int _selectedFilterIndex;
  late LocaleModel localeModel = Provider.of<LocaleModel>(context, listen: false);

  Future<void> changeSelectedFilterIndex(int index) async {
    localeModel.set(locals[index]);
    Get.updateLocale(locals[index]);
    Get.changeTheme(getLightTheme(locals[index].languageCode));
    setState(() {
      _selectedFilterIndex = index;
    });
    Get.offAllNamed(home);
  }

  @override
  void initState() {
    super.initState();
    _getCurrentLocale();
  }

  Future<void> _getCurrentLocale() async {
    String st = localeModel.locale.languageCode;
    setState(() {
      currentLocale = st != '' ? st : Get.deviceLocale?.languageCode ?? 'en';
      _selectedFilterIndex = locals.indexWhere((element) => element.languageCode == currentLocale);
    });
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      shape: Border(
        bottom: BorderSide(color: Colors.grey.withValues(alpha: .4)),
      ),
      onTap: () async {
        await showModalBottomSheet(
          isScrollControlled: false,
          useSafeArea: true,
          showDragHandle: true,
          context: context,
          builder: (context) => CSelectBottomSheet(
            selectedIndex: _selectedFilterIndex,
            changeSelectedIndex: changeSelectedFilterIndex,
            items: langSelectItems,
            title: tr!.selectLanguage,
          ),
        );
      },
      leading: const Icon(Symbols.translate),
      title: Text(tr!.language),
      trailing: SizedBox(
        width: MediaQuery.of(context).size.width / 2,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            SvgPicture.asset(
              'assets/flags/$currentLocale.svg',
              width: 20,
            ),
            const SizedBox(
              width: 8,
            ),
            Text(
              languageNames[currentLocale]?['native'] ?? '',
              style: const TextStyle(fontSize: 14),
            )
          ],
        ),
      ),
    );
  }
}
