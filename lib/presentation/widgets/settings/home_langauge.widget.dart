import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:get/get.dart';
import 'package:pgl_mobile_app/app.dart';
import 'package:pgl_mobile_app/configs/themes/theme.dart';
import 'package:pgl_mobile_app/constants/constants.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_select_bottom_sheet.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/settings/language.setting.widget.dart';
import 'package:provider/provider.dart';

class HomeLanguage extends StatefulWidget {
  const HomeLanguage({super.key});

  @override
  State<HomeLanguage> createState() => _HomeLanguageState();
}

class _HomeLanguageState extends State<HomeLanguage> {
  late AppLocalizations? tr = AppLocalizations.of(context);
  late String currentLocale = Get.deviceLocale?.languageCode ?? 'en';
  late int _selectedFilterIndex;
  late LocaleModel localeModel = Provider.of<LocaleModel>(context, listen: false);

  Future<void> changeSelectedFilterIndex(int index) async {
    localeModel.set(locals[index]);
    Get.updateLocale(locals[index]);
    Get.changeTheme(getLightTheme(locals[index].languageCode));
    setState(() {
      _selectedFilterIndex = index;
    });
    Get.offAllNamed(home);
  }

  @override
  void initState() {
    super.initState();
    _getCurrentLocale();
  }

  Future<void> _getCurrentLocale() async {
    String st = localeModel.locale.languageCode;
    setState(() {
      currentLocale = st != '' ? st : Get.deviceLocale?.languageCode ?? 'en';
      _selectedFilterIndex = locals.indexWhere((element) => element.languageCode == currentLocale);
    });
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        await showModalBottomSheet(
          isScrollControlled: false,
          useSafeArea: true,
          showDragHandle: true,
          context: context,
          builder: (context) => CSelectBottomSheet(
            selectedIndex: _selectedFilterIndex,
            changeSelectedIndex: changeSelectedFilterIndex,
            items: langSelectItems,
            title: tr!.selectLanguage,
          ),
        );
      },
      borderRadius: BorderRadius.circular(30),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        child: Row(
          children: [
            Container(
              height: 20,
              width: 20,
              clipBehavior: Clip.hardEdge,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                color: Colors.red,
              ),
              child: SvgPicture.asset(
                'assets/flags/square/${localeModel.locale.languageCode}.svg',
                height: 20,
              ),
            ),
            const SizedBox(
              width: 8,
            ),
            Text(
              localeModel.locale.languageCode.toUpperCase(),
              style: const TextStyle(
                fontWeight: FontWeight.w700,
              ),
            )
          ],
        ),
      ),
    );
  }
}
