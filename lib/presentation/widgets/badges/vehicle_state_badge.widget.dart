import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/configs/themes/colors.theme.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:vehicles_repository/vehicles_repository.dart';

class VehicleStateBadge extends StatefulWidget {
  const VehicleStateBadge({super.key, required this.state});

  final VehicleState state;
  @override
  State<VehicleStateBadge> createState() => _VehicleStateBadgeState();
}

class _VehicleStateBadgeState extends State<VehicleStateBadge> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 18,
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
      ),
      decoration: BoxDecoration(
        color: _getStatusBadgeColor(widget.state).withValues(alpha: .15),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Center(
        child: Text(
          _getStatusBadgeText(widget.state),
          style: TextStyle(
            color: _getStatusBadgeColor(widget.state),
            fontSize: 8,
            fontWeight: FontWeight.w900,
          ),
        ),
      ),
    );
  }

  Color _getStatusBadgeColor(VehicleState state) {
    switch (state) {
      case VehicleState.onTheWay:
        return Theme.of(context).colorScheme.primary;
      case VehicleState.onHandNoTitle:
        return dashBoardRedColor;
      case VehicleState.onHandWithTitle:
        return dashBoardPurpleColor;
      case VehicleState.onHandWithLoad:
        return dashBoardCyanColor;
      case VehicleState.shipped:
        return dashBoardYellowColor;
      case VehicleState.auctionPaid:
        return dashBoardBlueColor;
      case VehicleState.auctionUnpaid:
        return dashBoardPinkColor;
    }
  }

  String _getStatusBadgeText(VehicleState state) {
    switch (state) {
      case VehicleState.onTheWay:
        return tr!.onTheWay.toUpperCase();
      case VehicleState.onHandNoTitle:
        return tr!.onHandNoTitle.toUpperCase();
      case VehicleState.onHandWithTitle:
        return tr!.onHandWithTitle.toUpperCase();
      case VehicleState.onHandWithLoad:
        return tr!.onHandWithLoad.toUpperCase();
      case VehicleState.shipped:
        return tr!.shipped.toUpperCase();
      case VehicleState.auctionPaid:
        return tr!.auctionPaid.toUpperCase();
      case VehicleState.auctionUnpaid:
        return tr!.auctionUnpaid.toUpperCase();
    }
  }
}
