import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/configs/themes/colors.theme.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';

class ShippingRateTypeBadge extends StatefulWidget {
  const ShippingRateTypeBadge({super.key, required this.type});

  final ShippingRateType type;
  @override
  State<ShippingRateTypeBadge> createState() => _ShippingRateTypeBadgeState();
}

class _ShippingRateTypeBadgeState extends State<ShippingRateTypeBadge> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 18,
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
      ),
      decoration: BoxDecoration(
        color: _getStatusBadgeColor(widget.type).withValues(alpha: .15),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Center(
        child: Text(
          _getStatusBadgeText(widget.type),
          style: TextStyle(
            color: _getStatusBadgeColor(widget.type),
            fontSize: 8,
            fontWeight: FontWeight.w900,
          ),
        ),
      ),
    );
  }

  Color _getStatusBadgeColor(ShippingRateType type) {
    switch (type) {
      case ShippingRateType.complete:
        return Theme.of(context).colorScheme.primary;
      case ShippingRateType.halfcut:
        return dashBoardYellowColor;
      case ShippingRateType.booking:
        return dashBoardPurpleColor;
    }
  }

  String _getStatusBadgeText(ShippingRateType type) {
    switch (type) {
      case ShippingRateType.complete:
        return tr!.complete.toUpperCase();
      case ShippingRateType.halfcut:
        return tr!.halfcut.toUpperCase();
      case ShippingRateType.booking:
        return tr!.booking.toUpperCase();
    }
  }
}
