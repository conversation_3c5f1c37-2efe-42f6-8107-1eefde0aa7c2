import 'package:flutter/material.dart';
import 'package:mix_shipping_repository/mix_shipping_repository.dart';
import 'package:pgl_mobile_app/configs/themes/colors.theme.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

class MixShippingStateBadge extends StatefulWidget {
  const MixShippingStateBadge({super.key, required this.state});

  final MixShippingStatus state;
  @override
  State<MixShippingStateBadge> createState() => _MixShippingStateBadgeState();
}

class _MixShippingStateBadgeState extends State<MixShippingStateBadge> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 18,
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
      ),
      decoration: BoxDecoration(
        color: _getStatusBadgeColor(widget.state).withValues(alpha: .15),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Center(
        child: Text(
          _getStatusBadgeText(widget.state),
          style: TextStyle(
            color: _getStatusBadgeColor(widget.state),
            fontSize: 8,
            fontWeight: FontWeight.w900,
          ),
        ),
      ),
    );
  }

  Color _getStatusBadgeColor(MixShippingStatus state) {
    switch (state) {
      case MixShippingStatus.open:
        return dashBoardYellowColor;
      case MixShippingStatus.paid:
        return Theme.of(context).colorScheme.primary;
      case MixShippingStatus.pastDue:
        return dashBoardRedColor;
    }
  }

  String _getStatusBadgeText(MixShippingStatus state) {
    switch (state) {
      case MixShippingStatus.open:
        return tr!.open.toUpperCase();
      case MixShippingStatus.paid:
        return tr!.paid.toUpperCase();
      case MixShippingStatus.pastDue:
        return tr!.pastDue.toUpperCase();
    }
  }
}
