import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/configs/themes/colors.theme.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';

class TransactionStateBadge extends StatefulWidget {
  const TransactionStateBadge({super.key, required this.type});

  final TransactionStatus type;
  @override
  State<TransactionStateBadge> createState() => _TransactionStateBadgeState();
}

class _TransactionStateBadgeState extends State<TransactionStateBadge> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 18,
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
      ),
      decoration: BoxDecoration(
        color: _getStatusBadgeColor(widget.type).withValues(alpha: .15),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Center(
        child: Text(
          _getStatusBadgeText(widget.type),
          style: TextStyle(
            color: _getStatusBadgeColor(widget.type),
            fontSize: 8,
            fontWeight: FontWeight.w900,
          ),
        ),
      ),
    );
  }

  Color _getStatusBadgeColor(TransactionStatus type) {
    switch (type) {
      case TransactionStatus.reviewed:
        return Theme.of(context).colorScheme.primary;
      case TransactionStatus.pending:
        return dashBoardYellowColor;
      case TransactionStatus.canceled:
        return dashBoardRedColor;
    }
  }

  String _getStatusBadgeText(TransactionStatus type) {
    switch (type) {
      case TransactionStatus.reviewed:
        return tr!.reviewed.toUpperCase();
      case TransactionStatus.pending:
        return tr!.pending.toUpperCase();
      case TransactionStatus.canceled:
        return tr!.canceled.toUpperCase();
    }
  }
}
