import 'package:flutter/material.dart';
import 'package:payments_repository/payments_repository.dart';
import 'package:pgl_mobile_app/configs/themes/colors.theme.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

class PaymentReceivedStateBadge extends StatefulWidget {
  const PaymentReceivedStateBadge({
    super.key,
    required this.state,
  });

  final PaymentReceivedState state;
  @override
  State<PaymentReceivedStateBadge> createState() => _PaymentReceivedStateBadgeState();
}

class _PaymentReceivedStateBadgeState extends State<PaymentReceivedStateBadge> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 18,
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
      ),
      decoration: BoxDecoration(
        color: _getStatusBadgeColor(widget.state).withValues(alpha: .15),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Center(
        child: Text(
          _getStatusBadgeText(widget.state),
          style: TextStyle(
            color: _getStatusBadgeColor(widget.state),
            fontSize: 8,
            fontWeight: FontWeight.w900,
          ),
        ),
      ),
    );
  }

  Color _getStatusBadgeColor(PaymentReceivedState state) {
    switch (state) {
      case PaymentReceivedState.approved:
        return Theme.of(context).colorScheme.primary;
      case PaymentReceivedState.pending:
        return dashBoardYellowColor;
    }
  }

  String _getStatusBadgeText(PaymentReceivedState state) {
    switch (state) {
      case PaymentReceivedState.approved:
        return tr!.approved.toUpperCase();
      case PaymentReceivedState.pending:
        return tr!.pending.toUpperCase();
    }
  }
}
