import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/configs/themes/colors.theme.dart';
import 'package:shipments_repository/shipments_repository.dart';

class ShipmentStateBadge extends StatefulWidget {
  const ShipmentStateBadge({super.key, required this.state});

  final ShipmentState state;
  @override
  State<ShipmentStateBadge> createState() => _ShipmentStateBadgeState();
}

class _ShipmentStateBadgeState extends State<ShipmentStateBadge> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 18,
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
      ),
      decoration: BoxDecoration(
        color: _getStatusBadgeColor(widget.state).withValues(alpha: .15),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Center(
        child: Text(
          _getStatusBadgeText(widget.state),
          style: TextStyle(
            color: _getStatusBadgeColor(widget.state),
            fontSize: 8,
            fontWeight: FontWeight.w900,
          ),
        ),
      ),
    );
  }

  Color _getStatusBadgeColor(ShipmentState state) {
    switch (state) {
      case ShipmentState.atLoading:
        return dashBoardRedColor;
      case ShipmentState.onTheWay:
        return Theme.of(context).colorScheme.primary;
      case ShipmentState.arrived:
        return dashBoardPurpleColor;
    }
  }

  String _getStatusBadgeText(ShipmentState state) {
    switch (state) {
      case ShipmentState.atLoading:
        return tr!.atLoading.toUpperCase();
      case ShipmentState.onTheWay:
        return tr!.onTheWay.toUpperCase();
      case ShipmentState.arrived:
        return tr!.arrived.toUpperCase();
    }
  }
}
