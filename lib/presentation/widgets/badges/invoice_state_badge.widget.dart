import 'package:flutter/material.dart';
import 'package:invoices_repository/invoices_repository.dart';
import 'package:pgl_mobile_app/configs/themes/colors.theme.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

class InvoiceStateBadge extends StatefulWidget {
  const InvoiceStateBadge({super.key, required this.state});

  final InvoiceState state;
  @override
  State<InvoiceStateBadge> createState() => _InvoiceStateBadgeState();
}

class _InvoiceStateBadgeState extends State<InvoiceStateBadge> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 18,
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
      ),
      decoration: BoxDecoration(
        color: _getStatusBadgeColor(widget.state).withValues(alpha: .15),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Center(
        child: Text(
          _getStatusBadgeText(widget.state),
          style: TextStyle(
            color: _getStatusBadgeColor(widget.state),
            fontSize: 8,
            fontWeight: FontWeight.w900,
          ),
        ),
      ),
    );
  }

  Color _getStatusBadgeColor(InvoiceState state) {
    switch (state) {
      case InvoiceState.open:
        return dashBoardYellowColor;
      case InvoiceState.paid:
        return Theme.of(context).colorScheme.primary;
      case InvoiceState.pastDue:
        return dashBoardRedColor;
    }
  }

  String _getStatusBadgeText(InvoiceState state) {
    switch (state) {
      case InvoiceState.open:
        return tr!.open.toUpperCase();
      case InvoiceState.paid:
        return tr!.paid.toUpperCase();
      case InvoiceState.pastDue:
        return tr!.pastDue.toUpperCase();
    }
  }
}
