import 'package:flutter/material.dart';
import 'package:payments_repository/payments_repository.dart';
import 'package:pgl_mobile_app/configs/themes/colors.theme.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

class PaymentStateBadge extends StatefulWidget {
  const PaymentStateBadge({
    super.key,
    required this.state,
  });

  final PaymentState state;
  @override
  State<PaymentStateBadge> createState() => _PaymentStateBadgeState();
}

class _PaymentStateBadgeState extends State<PaymentStateBadge> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 18,
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
      ),
      decoration: BoxDecoration(
        color: _getStatusBadgeColor(widget.state).withValues(alpha: .15),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Center(
        child: Text(
          _getStatusBadgeText(widget.state),
          style: TextStyle(
            color: _getStatusBadgeColor(widget.state),
            fontSize: 8,
            fontWeight: FontWeight.w900,
          ),
        ),
      ),
    );
  }

  Color _getStatusBadgeColor(PaymentState state) {
    switch (state) {
      case PaymentState.approved:
        return Theme.of(context).colorScheme.primary;
      case PaymentState.pending:
        return dashBoardYellowColor;
    }
  }

  String _getStatusBadgeText(PaymentState state) {
    switch (state) {
      case PaymentState.approved:
        return tr!.approved.toUpperCase();
      case PaymentState.pending:
        return tr!.pending.toUpperCase();
    }
  }
}
