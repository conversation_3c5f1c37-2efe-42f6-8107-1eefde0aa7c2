import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:pgl_mobile_app/configs/themes/colors.theme.dart';
import 'package:skeletonizer/skeletonizer.dart';

class CategoryItemCard extends StatefulWidget {
  const CategoryItemCard({
    super.key,
    this.loading = false,
    this.onTap,
    required this.iconAssetName,
    required this.title,
    required this.count,
    this.maxLines = 1,
  });

  final bool loading;
  final void Function()? onTap;
  final String iconAssetName;
  final String title;
  final int count;
  final int maxLines;
  @override
  State<CategoryItemCard> createState() => _CategoryItemCardState();
}

class _CategoryItemCardState extends State<CategoryItemCard> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.light ? Colors.grey.shade300 : Colors.grey.shade800,
          width: 1,
        ),
      ),
      child: Material(
        color: Theme.of(context).brightness == Brightness.light ? cardBgColorLight : cardBgColorDark,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          onTap: widget.onTap,
          customBorder: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: SizedBox(
            width: MediaQuery.of(context).size.width / 2 - 34,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: .1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  height: 100,
                  child: Center(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary.withValues(alpha: .1),
                        borderRadius: BorderRadius.circular(100),
                      ),
                      height: 80,
                      width: 80,
                      child: Center(
                        child: SizedBox(
                          width: 64,
                          height: 64,
                          child: SvgPicture.asset(
                            widget.iconAssetName,
                            width: 64,
                            height: 64,
                            theme: SvgTheme(
                              currentColor: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Skeletonizer(
                  enabled: widget.loading,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SelectableText(
                          onTap: widget.onTap,
                          widget.title,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w700,
                          ),
                          maxLines: widget.maxLines,
                        ),
                        Text(
                          '${widget.count}',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Theme.of(context).brightness == Brightness.light ? Colors.black45 : Colors.white70,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
