import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:payments_repository/payments_repository.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/widgets/badges/payment_state_badge.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/bottom_sheets/payments/payment_detail.bottom_sheet.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/card_style.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_stripped_table.widget.dart';

class PaymentItemCard extends StatefulWidget {
  const PaymentItemCard({
    super.key,
    required this.payment,
  });

  final PaymentModel payment;

  @override
  State<PaymentItemCard> createState() => _PaymentItemCardState();
}

String getCurrency(PaymentModel payment) {
  if (payment.currency == 'USD' || payment.currency == '\$') {
    return '\$';
  } else {
    return '${payment.currency} ';
  }
}

String getAmountWithCurrency(PaymentModel payment, double value) {
  return '${getCurrency(payment)}${NumberFormat('###,###,###,###.##').format(value)}';
}

class _PaymentItemCardState extends State<PaymentItemCard> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return CardStyle(
      onTap: () async {
        await showModalBottomSheet(
          isScrollControlled: true,
          useSafeArea: true,
          context: context,
          showDragHandle: true,
          constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * .8),
          builder: (context) => PaymentDetailBottomSheet(
            payment: widget.payment,
          ),
        );
      },
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Symbols.tag,
                            size: 18,
                            color: Theme.of(context).colorScheme.primary,
                            weight: 700,
                          ),
                          const SizedBox(
                            width: 2,
                          ),
                          SelectableText(
                            'PGLPN${widget.payment.id.toString().padLeft(5, '0')}',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w700,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                      // state
                      PaymentStateBadge(state: widget.payment.state),
                    ],
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: SelectableText(
                      widget.payment.transactionNumber,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                        height: 1.3,
                      ),
                      textAlign: TextAlign.start,
                    ),
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  // amounts
                  CStrippedTable(
                    data: [
                      StrippedTableItem(
                        label: tr!.amount,
                        value: getAmountWithCurrency(widget.payment, widget.payment.amount),
                      ),
                      StrippedTableItem(
                        label: tr!.amountApplied,
                        value: getAmountWithCurrency(
                          widget.payment,
                          widget.payment.amountApplied,
                        ),
                      ),
                      StrippedTableItem(
                        label: tr!.remainingAmount,
                        value: getAmountWithCurrency(
                          widget.payment,
                          widget.payment.amount - widget.payment.amountApplied,
                        ),
                        valueWidget: Text(
                          getAmountWithCurrency(
                            widget.payment,
                            widget.payment.amount - widget.payment.amountApplied,
                          ),
                          style: TextStyle(
                            fontSize: 12,
                            color: widget.payment.amount - widget.payment.amountApplied > 0
                                ? Theme.of(context).colorScheme.error
                                : Theme.of(context).colorScheme.primary,
                          ),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
