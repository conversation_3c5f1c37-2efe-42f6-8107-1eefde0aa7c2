import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_select_bottom_sheet.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_stripped_table.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/card_style.widget.dart';
import 'package:skeletonizer/skeletonizer.dart';

class DashBoardItemCard extends StatefulWidget {
  const DashBoardItemCard({
    super.key,
    this.loading = false,
    this.moreInfoLoading = false,
    this.hasMoreDetails = false,
    this.categoryItems,
    this.onCategoryItemChange,
    this.defaultCategoryIndex,
    this.selectMenuTitle,
    this.tableTopTitle,
    required this.color,
    required this.data,
    required this.iconAssetName,
    this.strippedTableData,
    this.onStrippedTableItemTab,
    this.onTap,
    this.onCollapseBtnOpen,
  });

  final bool loading;
  final bool moreInfoLoading;
  final bool hasMoreDetails;
  final int? defaultCategoryIndex;
  final List<SelectItem>? categoryItems;
  final void Function(SelectItem? item)? onCategoryItemChange;
  final String? selectMenuTitle;
  final String? tableTopTitle;
  final Color color;
  final DashBoardCardData data;
  final String iconAssetName;
  final List<StrippedTableItem>? strippedTableData;
  final void Function(StrippedTableItem item)? onStrippedTableItemTab;
  final void Function()? onTap;
  final void Function()? onCollapseBtnOpen;

  @override
  State<DashBoardItemCard> createState() => _DashBoardItemCardState();
}

class _DashBoardItemCardState extends State<DashBoardItemCard> {
  bool _isCollapsed = true;
  bool _isSelectOpen = false;
  late int _selectedFilterIndex;
  final ExpandableController _expandableController = ExpandableController();

  void changeSelectedFilterIndex(int index) {
    setState(() {
      _selectedFilterIndex = index;
    });
    if (widget.onCategoryItemChange != null) {
      widget.onCategoryItemChange!(widget.categoryItems?[_selectedFilterIndex]);
    }
  }

  @override
  void initState() {
    setState(() {
      _selectedFilterIndex = widget.defaultCategoryIndex ?? 0;
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return CardStyle(
      onTap: widget.onTap,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        clipBehavior: Clip.hardEdge,
        child: Stack(
          children: [
            _background(),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          _icon(),
                          const SizedBox(
                            width: 8,
                          ),
                          _texts(),
                        ],
                      ),
                      widget.hasMoreDetails ? _collapseBtn() : Container(),
                    ],
                  ),
                  if (widget.data.description != null)
                    const SizedBox(
                      height: 10,
                    ),
                  if (widget.data.description != null)
                    Align(
                      alignment: Directionality.of(context) == TextDirection.ltr
                          ? Alignment.centerLeft
                          : Alignment.centerRight,
                      child: Text(
                        widget.data.description ?? '',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).brightness == Brightness.light ? Colors.black87 : Colors.white70,
                        ),
                      ),
                    ),
                  if (widget.hasMoreDetails) _moreDetails(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _moreDetails() {
    return Expandable(
      theme: const ExpandableThemeData(
        alignment: Alignment.topCenter,
      ),
      controller: _expandableController,
      collapsed: Container(),
      expanded: Column(
        children: [
          const SizedBox(
            height: 8,
          ),
          SizedBox(
            width: double.infinity,
            child: Wrap(
              alignment: WrapAlignment.spaceBetween,
              runSpacing: 10,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: Text(
                    widget.tableTopTitle ?? '',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                      color: Theme.of(context).brightness == Brightness.light ? Colors.black54 : Colors.white,
                    ),
                  ),
                ),
                _moreDetailsTop(),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Skeletonizer(
              enabled: widget.moreInfoLoading,
              child: CStrippedTable(
                onItemTap: widget.onStrippedTableItemTab,
                data: widget.strippedTableData ?? [],
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _moreDetailsTop() {
    return SizedBox(
      height: 32,
      child: FilledButton.icon(
        onPressed: () async {
          setState(() {
            _isSelectOpen = true;
          });
          await showModalBottomSheet(
            isScrollControlled: false,
            useSafeArea: true,
            showDragHandle: true,
            context: context,
            builder: (context) => CSelectBottomSheet(
              selectedIndex: _selectedFilterIndex,
              changeSelectedIndex: changeSelectedFilterIndex,
              items: widget.categoryItems ?? [],
              title: widget.selectMenuTitle,
            ),
          );
          setState(() {
            _isSelectOpen = false;
          });
        },
        style: ButtonStyle(
          backgroundColor: WidgetStatePropertyAll(widget.color),
          padding: const WidgetStatePropertyAll(
            EdgeInsets.only(
              left: 10,
              right: 16,
            ),
          ),
        ),
        icon: Icon(
          _isSelectOpen ? Symbols.expand_less : Symbols.expand_more,
          size: 18,
        ),
        label: Text(
          widget.categoryItems![_selectedFilterIndex].label,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w700,
          ),
        ),
      ),
    );
  }

  Widget _texts() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.data.title,
          style: TextStyle(
            color: widget.color,
            fontWeight: FontWeight.w700,
            fontSize: 16,
          ),
        ),
        Skeletonizer(
          enabled: widget.loading,
          child: Text(
            widget.loading ? 'Text' : '${widget.data.count}',
            style: TextStyle(
              fontWeight: FontWeight.w700,
              fontSize: 24,
              color: Theme.of(context).brightness == Brightness.light ? Colors.black54 : Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _collapseBtn() {
    return SizedBox(
      height: 40,
      width: 40,
      child: IconButton(
        onPressed: () {
          setState(() {
            _isCollapsed = !_isCollapsed;
          });
          _expandableController.toggle();
          if (widget.onCollapseBtnOpen != null && !_isCollapsed) {
            widget.onCollapseBtnOpen!();
          }
        },
        style: ButtonStyle(
          backgroundColor: WidgetStatePropertyAll(
            widget.color,
          ),
        ),
        icon: Icon(
          _isCollapsed ? Symbols.keyboard_double_arrow_down : Symbols.keyboard_double_arrow_up,
          color: Theme.of(context).colorScheme.onPrimary,
        ),
      ),
    );
  }

  Widget _icon() {
    return Container(
      height: 56,
      width: 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: widget.color.withValues(alpha: .1),
      ),
      child: Center(
        child: SvgPicture.asset(
          widget.iconAssetName,
          width: 42,
          height: 42,
          theme: SvgTheme(
            currentColor: widget.color,
          ),
        ),
      ),
    );
  }

  Widget _background() {
    return Positioned(
      top: -100,
      right: -65,
      child: Container(
        width: 200,
        height: 200,
        decoration: BoxDecoration(
          color: widget.color.withValues(alpha: .1),
          borderRadius: BorderRadius.circular(100),
        ),
      ),
    );
  }
}
