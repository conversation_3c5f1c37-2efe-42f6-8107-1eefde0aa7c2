import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_icon_text.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/card_style.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_stripped_table.widget.dart';
import 'package:skeletonizer/skeletonizer.dart';

class InvoiceItemCardSkeleton extends StatefulWidget {
  const InvoiceItemCardSkeleton({
    super.key,
  });

  @override
  State<InvoiceItemCardSkeleton> createState() => _InvoiceItemCardSkeletonState();
}

class _InvoiceItemCardSkeletonState extends State<InvoiceItemCardSkeleton> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return CardStyle(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // invoice Number
                      _invoiceNoSection(),
                      // status
                    ],
                  ),
                  // container no
                  const Padding(
                    padding: EdgeInsets.symmetric(vertical: 4),
                    child: Skeletonizer(
                      child: Text(
                        'containerNumber',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w700,
                          height: 1.3,
                        ),
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.start,
                      ),
                    ),
                  ),
                  _datesSection(),
                  const SizedBox(
                    height: 8,
                  ),
                  // ammounts
                  Skeletonizer(
                    child: CStrippedTable(
                      data: [
                        StrippedTableItem(
                          label: tr!.amount,
                          value: "\$ 10000",
                        ),
                        StrippedTableItem(
                          label: tr!.received,
                          value: "\$ 10000",
                        ),
                        StrippedTableItem(
                          label: tr!.amountDue,
                          value: "\$ 10000",
                        ),
                      ],
                    ),
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _invoiceNoSection() {
    return Skeletonizer(
      child: Row(
        children: [
          Icon(
            Symbols.tag,
            size: 18,
            color: Theme.of(context).colorScheme.primary,
            weight: 700,
          ),
          const SizedBox(
            width: 2,
          ),
          Text(
            'bookingNumber',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w700,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _datesSection() {
    return const Skeletonizer(
      child: Row(
        children: [
          //issue date
          Expanded(
            child: CIconText(
              icon: Symbols.edit_calendar,
              text: 'invoiceDate text',
            ),
          ),
          //due date
          Expanded(
            child: CIconText(
              icon: Symbols.calendar_clock,
              text: 'invoiceDate text',
            ),
          ),
        ],
      ),
    );
  }
}
