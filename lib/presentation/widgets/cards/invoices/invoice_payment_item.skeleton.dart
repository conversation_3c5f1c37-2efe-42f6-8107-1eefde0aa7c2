import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/card_style.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_stripped_table.widget.dart';
import 'package:skeletonizer/skeletonizer.dart';

class InvoicePaymentItemCardSkeleton extends StatefulWidget {
  const InvoicePaymentItemCardSkeleton({
    super.key,
  });

  @override
  State<InvoicePaymentItemCardSkeleton> createState() => _InvoicePaymentItemCardSkeletonState();
}

class _InvoicePaymentItemCardSkeletonState extends State<InvoicePaymentItemCardSkeleton> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return CardStyle(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // invoice Number
                      _invoiceNoSection(),
                    ],
                  ),
                  // container no
                  const SizedBox(
                    height: 8,
                  ),
                  // ammounts
                  Skeletonizer(
                    child: CStrippedTable(
                      data: [
                        StrippedTableItem(
                          label: tr!.type,
                          value: 'paymentType',
                        ),
                        StrippedTableItem(
                          label: tr!.amountApplied,
                          value: '100000',
                        ),
                        StrippedTableItem(
                          label: tr!.remark,
                          value: 'widget.payment.paymentRemark',
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  Skeletonizer(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          tr!.paymentAllocations,
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  Skeletonizer(
                    child: CStrippedTable(
                      data: [
                        StrippedTableItem(
                          label: 'allocation',
                          value: 'allocation',
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _invoiceNoSection() {
    return Skeletonizer(
      child: Row(
        children: [
          Icon(
            Symbols.tag,
            size: 18,
            color: Theme.of(context).colorScheme.primary,
            weight: 700,
          ),
          const SizedBox(
            width: 4,
          ),
          SelectableText(
            'widget.payment.vin',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w700,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }
}
