import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:invoices_repository/invoices_repository.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:payments_repository/payments_repository.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/badges/received_payment_state_badge.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/card_style.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_stripped_table.widget.dart';

class InvoicePaymentItemCard extends StatefulWidget {
  const InvoicePaymentItemCard({
    super.key,
    required this.payment,
  });

  final PaymentReceived<Invoice> payment;

  @override
  State<InvoicePaymentItemCard> createState() => _InvoicePaymentItemCardState();
}

class _InvoicePaymentItemCardState extends State<InvoicePaymentItemCard> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return CardStyle(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // invoice Number
                      _invoiceNoSection(),
                      // status
                      PaymentReceivedStateBadge(
                        state: widget.payment.state,
                      ),
                    ],
                  ),
                  // container no
                  const SizedBox(
                    height: 8,
                  ),
                  // ammounts
                  CStrippedTable(
                    data: [
                      StrippedTableItem(
                        label: tr!.type,
                        value: getPaymentType(),
                      ),
                      StrippedTableItem(
                        label: tr!.amountApplied,
                        value: '\$${NumberFormat('###,###,###,###.##').format(widget.payment.amountApplied)}',
                      ),
                      StrippedTableItem(
                        label: tr!.remark,
                        value: widget.payment.paymentRemark ?? '',
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        tr!.paymentAllocations,
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  CStrippedTable(
                    data: widget.payment.paymentAllocations
                        .map(
                          (PaymentAlloction allocation) => StrippedTableItem(
                            label: getAllocationType(allocation.type),
                            value: '\$${NumberFormat('###,###,###,###').format(allocation.amount)}',
                          ),
                        )
                        .toList(),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _invoiceNoSection() {
    return Row(
      children: [
        Icon(
          Symbols.tag,
          size: 18,
          color: Theme.of(context).colorScheme.primary,
          weight: 700,
        ),
        const SizedBox(
          width: 2,
        ),
        SelectableText(
          widget.payment.parent.invoiceNumber,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w700,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  String getPaymentType() {
    switch (widget.payment.type) {
      case PaymentReceivedType.shipment:
        return tr!.shipment;
      case PaymentReceivedType.mix:
        return tr!.mix;
      case PaymentReceivedType.clearance:
        return tr!.clearance;
      case PaymentReceivedType.clearLog:
        return tr!.clearLog;
      case PaymentReceivedType.singleVcc:
        return tr!.singleVcc;
      case PaymentReceivedType.exitClaimCharge:
        return tr!.exitClaimCharge;
      case PaymentReceivedType.deliveryCharge:
        return tr!.deliveryCharge;
      case PaymentReceivedType.detentionCharge:
        return tr!.detentionCharge;
      case PaymentReceivedType.auction:
        return tr!.auction;
    }
  }

  String getAllocationType(PaymentAllocationType type) {
    switch (type) {
      case PaymentAllocationType.towing:
        return tr!.towing;
      case PaymentAllocationType.shipping:
        return tr!.shipping;
      case PaymentAllocationType.title:
        return tr!.title;
      case PaymentAllocationType.storageClr:
        return tr!.storageClr;
      case PaymentAllocationType.storageDo:
        return tr!.storageDo;
      case PaymentAllocationType.charge:
        return tr!.charge;
      case PaymentAllocationType.other:
        return tr!.other;
      case PaymentAllocationType.storage:
        return tr!.storage;
      case PaymentAllocationType.dismantle:
        return tr!.dismantle;
      case PaymentAllocationType.clearance:
        return tr!.clearance;
      case PaymentAllocationType.vatCustom:
        return tr!.vatCustom;
      case PaymentAllocationType.freight:
        return tr!.freight;
      case PaymentAllocationType.attestationFee:
        return tr!.attestationFee;
      case PaymentAllocationType.inspectionCharges:
        return tr!.inspectionCharges;
      case PaymentAllocationType.auctionStorage:
        return tr!.auctionStorage;
      case PaymentAllocationType.sharjahYardStorage:
        return tr!.sharjahYardStorage;
      case PaymentAllocationType.fedExOrMailingFee:
        return tr!.fedExOrMailingFee;
      case PaymentAllocationType.recoveryFee:
        return tr!.recoveryFee;
      case PaymentAllocationType.customHold:
        return tr!.customHold;
      case PaymentAllocationType.relistFee:
        return tr!.relistFee;
      case PaymentAllocationType.detentionCharges:
        return tr!.detentionCharges;
      case PaymentAllocationType.shortage:
        return tr!.shortage;
      case PaymentAllocationType.suvCharges:
        return tr!.suvCharges;
      case PaymentAllocationType.tdsCharges:
        return tr!.tdsCharges;
      case PaymentAllocationType.registrationFee:
        return tr!.registrationFee;
      case PaymentAllocationType.transportationFee:
        return tr!.transportationFee;
      case PaymentAllocationType.officeFeeAndBank:
        return tr!.officeFeeAndBank;
      case PaymentAllocationType.emptyContainers:
        return tr!.emptyContainers;
      case PaymentAllocationType.previousPayment:
        return tr!.previousPayment;
      case PaymentAllocationType.vehiclePrice:
        return tr!.vehiclePrice;
    }
  }
}
