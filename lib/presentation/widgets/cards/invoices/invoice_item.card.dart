import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:invoices_repository/invoices_repository.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/constants/date_formats.const.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/badges/invoice_state_badge.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/bottom_sheets/invoices/invoice_detail.bottom_sheet.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_icon_text.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/card_style.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_stripped_table.widget.dart';

class InvoiceItemCard extends StatefulWidget {
  const InvoiceItemCard({
    super.key,
    required this.invoice,
  });

  final Invoice invoice;

  @override
  State<InvoiceItemCard> createState() => _InvoiceItemCardState();
}

class _InvoiceItemCardState extends State<InvoiceItemCard> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return CardStyle(
      onTap: () async {
        await showModalBottomSheet(
          isScrollControlled: true,
          useSafeArea: true,
          context: context,
          showDragHandle: true,
          constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * .8),
          builder: (context) => InvoiceDetailBottomSheet(
            invoice: widget.invoice,
          ),
        );
      },
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // invoice Number
                      _invoiceNoSection(),
                      // status
                      InvoiceStateBadge(state: widget.invoice.invoiceState),
                    ],
                  ),
                  // container no
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: SelectableText(
                      widget.invoice.containerNumber,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                        height: 1.3,
                      ),
                      textAlign: TextAlign.start,
                    ),
                  ),
                  _datesSection(),
                  const SizedBox(
                    height: 8,
                  ),
                  // ammounts
                  CStrippedTable(
                    data: [
                      StrippedTableItem(
                        label: tr!.amount,
                        value: "\$${widget.invoice.getAmount()}",
                      ),
                      StrippedTableItem(
                        label: tr!.received,
                        value: "\$${widget.invoice.getPaymentReceived()}",
                      ),
                      StrippedTableItem(
                        label: tr!.amountDue,
                        value: "\$${widget.invoice.getAmountDue()}",
                      ),
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _invoiceNoSection() {
    return Row(
      children: [
        Icon(
          Symbols.tag,
          size: 18,
          color: Theme.of(context).colorScheme.primary,
          weight: 700,
        ),
        const SizedBox(
          width: 2,
        ),
        SelectableText(
          widget.invoice.invoiceNumber,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w700,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  Widget _datesSection() {
    return Row(
      children: [
        //issue date
        Expanded(
          child: CIconText(
            icon: Symbols.edit_calendar,
            text: DateFormat(cardsDateFormat).format(
              widget.invoice.invoiceDate,
            ),
          ),
        ),
        //due date
        if (widget.invoice.invoiceDueDate != null)
          Expanded(
            child: CIconText(
              icon: Symbols.calendar_clock,
              text: DateFormat(cardsDateFormat).format(
                widget.invoice.invoiceDueDate ?? DateTime.now(),
              ),
            ),
          ),
      ],
    );
  }
}
