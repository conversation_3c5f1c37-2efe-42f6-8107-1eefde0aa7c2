import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/card_style.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_stripped_table.widget.dart';
import 'package:towing_rates_repository/towing_rates_repository.dart';

class TowingRateItemCard extends StatefulWidget {
  const TowingRateItemCard({
    super.key,
    required this.towingRate,
  });

  final TowingRate towingRate;

  @override
  State<TowingRateItemCard> createState() => _TowingRateItemCardState();
}

class _TowingRateItemCardState extends State<TowingRateItemCard> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return CardStyle(
      onTap: null,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _locationsSection(),
                    ],
                  ),

                  const SizedBox(
                    height: 16,
                  ),
                  // ammounts
                  CStrippedTable(
                    data: [
                      if (widget.towingRate.ga != null)
                        StrippedTableItem(
                          label: "GA",
                          value: "\$${widget.towingRate.ga?.formatComma()}",
                        ),
                      if (widget.towingRate.ca != null)
                        StrippedTableItem(
                          label: "CA",
                          value: "\$${widget.towingRate.ca?.formatComma()}",
                        ),
                      if (widget.towingRate.tx != null)
                        StrippedTableItem(
                          label: "TX",
                          value: "\$${widget.towingRate.tx?.formatComma()}",
                        ),
                      if (widget.towingRate.nj != null)
                        StrippedTableItem(
                          label: "(NJ)",
                          value: "\$${widget.towingRate.nj?.formatComma()}",
                        ),
                      if (widget.towingRate.ba != null)
                        StrippedTableItem(
                          label: "(BAL)",
                          value: "\$${widget.towingRate.ba?.formatComma()}",
                        ),
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _locationsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '${tr!.branch}:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(
              width: 4,
            ),
            Text(
              widget.towingRate.branchName,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w700,
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: .7),
              ),
            ),
          ],
        ),
        SizedBox(
          height: 4,
        ),
        Row(
          children: [
            SizedBox(
              width: 2,
            ),
            SvgPicture.asset(
              'assets/svgs/bottom-right-turn-icon.svg',
              theme: SvgTheme(
                currentColor: Theme.of(context).colorScheme.primary,
              ),
              width: 17,
            ),
            const SizedBox(
              width: 4,
            ),
            Text(
              '${tr!.city}:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(
              width: 4,
            ),
            Text(
              widget.towingRate.cityName,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w700,
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: .7),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
