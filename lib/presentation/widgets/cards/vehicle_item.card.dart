import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/constants/constants.dart';
import 'package:pgl_mobile_app/constants/date_formats.const.dart';
import 'package:pgl_mobile_app/logic/helpers/images.helper.dart';
import 'package:pgl_mobile_app/presentation/screens/screens.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/badges/vehicle_state_badge.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/bottom_sheets/tracking.bottom_sheet.dart';
import 'package:pgl_mobile_app/presentation/widgets/bottom_sheets/vehicle_detail.bottom_sheet.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_icon_text.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/card_style.widget.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:vehicles_repository/vehicles_repository.dart';

class VehicleItemCard extends StatefulWidget {
  const VehicleItemCard({
    super.key,
    required this.vehicle,
    this.showBackgroundIcon = false,
  });

  final Vehicle vehicle;
  final bool showBackgroundIcon;

  @override
  State<VehicleItemCard> createState() => _VehicleItemCardState();
}

class _VehicleItemCardState extends State<VehicleItemCard> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late final String url = getImageSizeUrl(
    url: widget.vehicle.coverPhoto,
    size: 100,
  );

  @override
  void initState() {
    super.initState();
  }

  @override
  void setState(fn) {
    if (mounted) {
      super.setState(fn);
    }
  }

  @override
  Widget build(BuildContext context) {
    return CardStyle(
      onTap: () async {
        await showModalBottomSheet(
          isScrollControlled: true,
          useSafeArea: true,
          context: context,
          showDragHandle: true,
          constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * .8),
          builder: (context) => VehicleDetailBottomSheet(
            vehicle: widget.vehicle,
          ),
        );
      },
      child: Stack(
        children: [
          if (widget.showBackgroundIcon)
            Positioned(
              bottom: -30,
              right: -20,
              child: SvgPicture.asset(
                'assets/svgs/car-icon.svg',
                width: 120,
                height: 120,
                theme: SvgTheme(
                  currentColor: Theme.of(context).colorScheme.primary.withValues(alpha: .1),
                ),
              ),
            ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                _imageSection(),
                const SizedBox(
                  width: 10,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // lot
                          _lotSection(),
                          // status
                          VehicleStateBadge(state: widget.vehicle.vehicleState),
                        ],
                      ),
                      // description
                      Row(
                        children: [
                          Flexible(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              child: Text(
                                widget.vehicle.getDescription(),
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w700,
                                  height: 1.3,
                                ),
                                maxLines: 2,
                                softWrap: true,
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.start,
                              ),
                            ),
                          ),
                          const SizedBox(
                            width: 16,
                          ),
                          SizedBox(
                            height: 26,
                            child: FilledButton(
                              // color: Theme.of(context).colorScheme.primary,
                              style: FilledButton.styleFrom(
                                padding: const EdgeInsets.symmetric(horizontal: 12),
                                textStyle: const TextStyle(
                                  fontSize: 12,
                                ),
                                minimumSize: const Size(0, 26),
                              ),
                              onPressed: () async {
                                await showModalBottomSheet(
                                  isScrollControlled: true,
                                  useSafeArea: true,
                                  context: context,
                                  showDragHandle: true,
                                  constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * .8),
                                  builder: (context) => TrackingBottomSheet(
                                    query: widget.vehicle.vin,
                                  ),
                                );
                              },
                              child: Text(tr!.track),
                            ),
                          )
                        ],
                      ),

                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: CIconText(
                              icon: Symbols.directions_car,
                              text: widget.vehicle.vin,
                              selectable: true,
                            ),
                          ),
                          widget.vehicle.isKeyPresent != null
                              ? Icon(
                                  widget.vehicle.isKeyPresent != null && widget.vehicle.isKeyPresent == true
                                      ? Symbols.key
                                      : Symbols.key_off,
                                  color: widget.vehicle.isKeyPresent != null && widget.vehicle.isKeyPresent == true
                                      ? Theme.of(context).colorScheme.primary
                                      : Theme.of(context).colorScheme.error,
                                  size: 20,
                                )
                              : Container()
                        ],
                      ),
                      // dates
                      _datesSection(),
                    ],
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _imageSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Material(
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          onTap: widget.vehicle.images.isNotEmpty
              ? () {
                  Get.toNamed(
                    imageGallery,
                    arguments: ImageGalleryArguments(
                      urls: widget.vehicle.images,
                      title: widget.vehicle.getDescription(),
                    ),
                  );
                }
              : widget.vehicle.photoLink != null
                  ? () {
                      launchUrl(Uri.parse(widget.vehicle.photoLink ?? ''));
                    }
                  : () {},
          borderRadius: BorderRadius.circular(12),
          child: Container(
            height: 100,
            width: 100,
            clipBehavior: Clip.hardEdge,
            decoration: BoxDecoration(
              color: widget.vehicle.photoLink != null ||
                      widget.vehicle.coverPhoto != null ||
                      widget.vehicle.images.isNotEmpty
                  ? Theme.of(context).colorScheme.primary.withValues(alpha: .1)
                  : Colors.red.shade600.withValues(alpha: .1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: widget.vehicle.coverPhoto != null
                ? _image()
                : Center(
                    child: SvgPicture.asset(
                      widget.vehicle.photoLink != null || widget.vehicle.images.isNotEmpty
                          ? 'assets/svgs/car-image-icon.svg'
                          : 'assets/svgs/no-car-image-icon.svg',
                      height: 80,
                      width: 80,
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  Widget _image() {
    return CachedNetworkImage(
      imageUrl: url,
      fit: BoxFit.cover,
      fadeInCurve: Curves.easeIn,
      fadeOutCurve: Curves.easeIn,
      fadeInDuration: const Duration(milliseconds: 200),
      fadeOutDuration: const Duration(milliseconds: 200),
      placeholder: (context, url) => _imageLoading(),
      errorWidget: (context, url, error) {
        return const Icon(Icons.error);
      },
      httpHeaders: {},
    );
  }

  Widget _imageLoading() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
      ),
      clipBehavior: Clip.hardEdge,
      child: Skeletonizer(
        child: Skeleton.replace(
          height: 100,
          width: 100,
          replace: true,
          child: Container(),
        ),
      ),
    );
  }

  Widget _lotSection() {
    return Expanded(
      child: Row(
        children: [
          Icon(
            Symbols.tag,
            size: 18,
            color: Theme.of(context).colorScheme.primary,
            weight: 700,
          ),
          const SizedBox(
            width: 2,
          ),
          Flexible(
            flex: 1,
            child: SelectableText(
              widget.vehicle.lotNumber,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w700,
                color: Theme.of(context).colorScheme.primary,
              ),
              maxLines: 1,
            ),
          ),
        ],
      ),
    );
  }

  Widget _datesSection() {
    return Row(
      children: [
        //purchase date
        if ([
              VehicleState.onHandNoTitle,
              VehicleState.onHandWithTitle,
              VehicleState.onTheWay,
              VehicleState.auctionPaid,
              VehicleState.auctionUnpaid,
            ].contains(widget.vehicle.vehicleState) &&
            widget.vehicle.purchaseAt != null)
          Expanded(
            child: CIconText(
              icon: Symbols.calendar_add_on,
              text: DateFormat(cardsDateFormat).format(
                widget.vehicle.purchaseAt ?? DateTime.now(),
              ),
            ),
          ),
        //delivery date
        if ([
              VehicleState.onHandNoTitle,
              VehicleState.onHandWithTitle,
              VehicleState.shipped,
              VehicleState.auctionPaid,
              VehicleState.auctionUnpaid,
            ].contains(widget.vehicle.vehicleState) &&
            widget.vehicle.deliverDate != null)
          Expanded(
            child: CIconText(
              icon: Symbols.event_available,
              text: DateFormat(cardsDateFormat).format(
                widget.vehicle.deliverDate ?? DateTime.now(),
              ),
            ),
          ),
        //load date
        if ([
              VehicleState.shipped,
            ].contains(widget.vehicle.vehicleState) &&
            widget.vehicle.container != null)
          Expanded(
            child: CIconText(
              icon: Symbols.event_upcoming,
              text: DateFormat(cardsDateFormat).format(
                widget.vehicle.container?.loadingDate ?? DateTime.now(),
              ),
            ),
          ),
      ],
    );
  }
}
