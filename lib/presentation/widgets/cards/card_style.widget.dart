import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/configs/themes/colors.theme.dart';

class CardStyle extends StatefulWidget {
  const CardStyle({
    super.key,
    required this.child,
    this.onTap,
  });

  final Widget child;
  final void Function()? onTap;

  @override
  State<CardStyle> createState() => _CardStyleState();
}

class _CardStyleState extends State<CardStyle> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: 16,
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Theme.of(context).brightness == Brightness.light ? Colors.grey.shade300 : Colors.grey.shade800,
            width: 1,
          ), // Set border color and width
        ),
        child: Material(
          color: Theme.of(context).brightness == Brightness.light ? cardBgColorLight : cardBgColorDark,
          borderRadius: BorderRadius.circular(16),
          child: InkWell(
            onTap: widget.onTap,
            customBorder: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: widget.child,
          ),
        ),
      ),
    );
  }
}
