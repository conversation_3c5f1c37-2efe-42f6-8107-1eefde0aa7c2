import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/constants/date_formats.const.dart';
import 'package:pgl_mobile_app/logic/helpers/images.helper.dart';
import 'package:pgl_mobile_app/presentation/widgets/badges/shipment_state_badge.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/bottom_sheets/shipment_detail.bottom_sheet.dart';
import 'package:pgl_mobile_app/presentation/widgets/bottom_sheets/tracking.bottom_sheet.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/card_style.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_icon_text.widget.dart';
import 'package:shipments_repository/shipments_repository.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:url_launcher/url_launcher.dart';

class ShipmentItemCard extends StatefulWidget {
  const ShipmentItemCard({
    super.key,
    required this.shipment,
    this.showBackgroundIcon = false,
  });

  final Shipment shipment;
  final bool showBackgroundIcon;

  @override
  State<ShipmentItemCard> createState() => _ShipmentItemCardState();
}

class _ShipmentItemCardState extends State<ShipmentItemCard> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late final String url = getImageSizeUrl(
    url: widget.shipment.coverPhoto,
    size: 100,
  );

  @override
  void initState() {
    super.initState();
  }

  @override
  void setState(fn) {
    if (mounted) {
      super.setState(fn);
    }
  }

  @override
  Widget build(BuildContext context) {
    return CardStyle(
      onTap: () async {
        await showModalBottomSheet(
          isScrollControlled: true,
          useSafeArea: true,
          context: context,
          showDragHandle: true,
          constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * .8),
          builder: (context) => ShipmentDetailBottomSheet(
            shipment: widget.shipment,
          ),
        );
      },
      child: Stack(
        children: [
          if (widget.showBackgroundIcon)
            Positioned(
              bottom: -30,
              right: 0,
              child: SvgPicture.asset(
                'assets/svgs/truck-icon.svg',
                width: 120,
                height: 120,
                theme: SvgTheme(
                  currentColor: Theme.of(context).colorScheme.primary.withValues(alpha: .1),
                ),
              ),
            ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                _imageSection(),
                const SizedBox(
                  width: 10,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // booking
                          _bookingNoSection(),
                          // status
                          const SizedBox(
                            width: 8,
                          ),
                          ShipmentStateBadge(state: widget.shipment.shipmentState),
                        ],
                      ),
                      // container no
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Padding(
                                padding: const EdgeInsets.symmetric(vertical: 4),
                                child: SelectableText(
                                  widget.shipment.containerNumber,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w700,
                                    height: 1.3,
                                  ),
                                  textAlign: TextAlign.start,
                                ),
                              ),
                            ),
                            const SizedBox(
                              width: 16,
                            ),
                            SizedBox(
                              height: 26,
                              child: FilledButton(
                                // color: Theme.of(context).colorScheme.primary,
                                style: FilledButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(horizontal: 12),
                                  textStyle: const TextStyle(
                                    fontSize: 12,
                                  ),
                                  minimumSize: const Size(0, 26),
                                ),
                                onPressed: () async {
                                  await showModalBottomSheet(
                                    isScrollControlled: true,
                                    useSafeArea: true,
                                    context: context,
                                    showDragHandle: true,
                                    constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * .8),
                                    builder: (context) => TrackingBottomSheet(
                                      query: widget.shipment.containerNumber,
                                    ),
                                  );
                                },
                                child: Text(tr!.track),
                              ),
                            ),
                          ],
                        ),
                      ),
                      _datesSection(),
                      const SizedBox(
                        height: 4,
                      ),
                      _locationsSection(),
                    ],
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _imageSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Material(
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          onTap: widget.shipment.photoLink != null
              ? () {
                  launchUrl(Uri.parse(widget.shipment.photoLink ?? ''));
                }
              : null,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            height: 100,
            width: 100,
            clipBehavior: Clip.hardEdge,
            decoration: BoxDecoration(
              color: widget.shipment.photoLink != null || widget.shipment.coverPhoto != null
                  ? Theme.of(context).colorScheme.primary.withValues(alpha: .1)
                  : Colors.red.shade600.withValues(alpha: .1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: widget.shipment.coverPhoto != null
                ? _image()
                : Center(
                    child: SvgPicture.asset(
                      widget.shipment.photoLink != null
                          ? 'assets/svgs/shipment-image-icon.svg'
                          : 'assets/svgs/no-shipment-image-icon.svg',
                      height: 80,
                      width: 80,
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  Widget _image() {
    return CachedNetworkImage(
      imageUrl: url,
      fit: BoxFit.cover,
      fadeInCurve: Curves.easeIn,
      fadeOutCurve: Curves.easeIn,
      fadeInDuration: const Duration(milliseconds: 200),
      fadeOutDuration: const Duration(milliseconds: 200),
      placeholder: (context, url) => _imageLoading(),
      errorWidget: (context, url, error) => const Icon(Icons.error),
    );
  }

  Widget _imageLoading() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
      ),
      clipBehavior: Clip.hardEdge,
      child: Skeletonizer(
        child: Skeleton.replace(
          height: 100,
          width: 100,
          replace: true,
          child: Container(),
        ),
      ),
    );
  }

  Widget _bookingNoSection() {
    return Expanded(
      child: Row(
        children: [
          Icon(
            Symbols.book,
            size: 18,
            color: Theme.of(context).colorScheme.primary,
            weight: 700,
          ),
          const SizedBox(
            width: 2,
          ),
          Flexible(
            flex: 1,
            child: SelectableText(
              widget.shipment.getBookingNo(),
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w700,
                color: Theme.of(context).colorScheme.primary,
              ),
              maxLines: 1,
            ),
          ),
        ],
      ),
    );
  }

  Widget _datesSection() {
    return Row(
      children: [
        if (widget.shipment.bookings.vessel != null)
          if (widget.shipment.bookings.vessel!.etd != null)
            Expanded(
              child: CIconText(
                icon: Symbols.event_upcoming,
                text: DateFormat(cardsDateFormat).format(
                  widget.shipment.bookings.vessel!.etd ?? DateTime.now(),
                ),
              ),
            ),
        //etd
        if (widget.shipment.bookings.eta != null)
          Expanded(
            child: CIconText(
              icon: Symbols.event_available,
              text: DateFormat(cardsDateFormat).format(
                widget.shipment.bookings.eta ?? DateTime.now(),
              ),
            ),
          ),
      ],
    );
  }

  Widget _locationsSection() {
    return Row(
      children: [
        //point of loading
        if (widget.shipment.bookings.vessel != null)
          if (widget.shipment.bookings.vessel!.location != null)
            Expanded(
              child: CIconText(
                icon: Symbols.add_location_alt,
                // text: widget.shipment.bookings.location,
                text: widget.shipment.bookings.vessel!.location ?? '',
              ),
            ),
        //point discharge
        if (widget.shipment.bookings.destination != null)
          Expanded(
            child: CIconText(
              icon: Symbols.moved_location,
              text: widget.shipment.bookings.destination ?? '',
            ),
          ),
      ],
    );
  }
}
