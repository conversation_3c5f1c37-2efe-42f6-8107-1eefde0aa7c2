import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/constants/date_formats.const.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/widgets/badges/transaction_state_badge.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/bottom_sheets/transaction_detail.bottom_sheet.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/card_style.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_stripped_table.widget.dart';

class TransactionItemCard extends StatefulWidget {
  const TransactionItemCard({
    super.key,
    required this.transaction,
  });

  final TransactionModel transaction;

  @override
  State<TransactionItemCard> createState() => _TransactionItemCardState();
}

class _TransactionItemCardState extends State<TransactionItemCard> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return CardStyle(
      onTap: () async {
        await showModalBottomSheet(
          isScrollControlled: true,
          useSafeArea: true,
          context: context,
          showDragHandle: true,
          constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * .8),
          builder: (context) => TransactionDetailBottomSheet(
            transaction: widget.transaction,
          ),
        );
      },
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Symbols.tag,
                            size: 18,
                            color: Theme.of(context).colorScheme.primary,
                            weight: 700,
                          ),
                          const SizedBox(
                            width: 2,
                          ),
                          SelectableText(
                            'T-${widget.transaction.id.toString().padLeft(6, '0')}',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w700,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                      // state
                      TransactionStateBadge(type: widget.transaction.status),
                    ],
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: SelectableText(
                      widget.transaction.category.name,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                        height: 1.3,
                      ),
                      textAlign: TextAlign.start,
                    ),
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  // amounts
                  CStrippedTable(
                    data: [
                      StrippedTableItem(
                        label: tr!.amount,
                        value:
                            "${widget.transaction.amount > 0 ? NumberFormat('###,###,###,###').format(widget.transaction.amount) : '0'} (AED)",
                      ),
                      StrippedTableItem(
                        label: tr!.date,
                        value: DateFormat(cardsDateFormat).format(
                          widget.transaction.createdAt,
                        ),
                      ),
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
