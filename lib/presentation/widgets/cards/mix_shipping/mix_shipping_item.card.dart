import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:mix_shipping_repository/mix_shipping_repository.dart';
import 'package:pgl_mobile_app/constants/date_formats.const.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/badges/mix_shiping_state_badge.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/bottom_sheets/mix_shipping/mix_shipping_detail.bottom_sheet.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_icon_text.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/card_style.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_stripped_table.widget.dart';

class MixshippingItemCard extends StatefulWidget {
  const MixshippingItemCard({
    super.key,
    required this.mixShipping,
  });

  final MixShipping mixShipping;

  @override
  State<MixshippingItemCard> createState() => _MixshippingItemCardState();
}

class _MixshippingItemCardState extends State<MixshippingItemCard> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return CardStyle(
      onTap: () async {
        await showModalBottomSheet(
          isScrollControlled: true,
          useSafeArea: true,
          context: context,
          showDragHandle: true,
          constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * .8),
          builder: (context) => MixShippingDetailBottomSheet(
            mixShipping: widget.mixShipping,
          ),
        );
      },
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // invoice Number
                      _invoiceNoSection(),
                      // status
                      MixShippingStateBadge(
                        state: widget.mixShipping.mixShippingState,
                      ),
                    ],
                  ),
                  // container no
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: SelectableText(
                      widget.mixShipping.containerNumber,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                        height: 1.3,
                      ),
                      textAlign: TextAlign.start,
                    ),
                  ),
                  _datesSection(),
                  const SizedBox(
                    height: 8,
                  ),
                  // ammounts
                  CStrippedTable(
                    data: [
                      StrippedTableItem(
                        label: tr!.amount,
                        value: "\$${widget.mixShipping.getTotalCostString()}",
                      ),
                      StrippedTableItem(
                        label: '${tr!.amount} (AED)',
                        value: "${widget.mixShipping.getTotalCostStringAED()} DH",
                      ),
                      StrippedTableItem(
                        label: '${tr!.received} (AED)',
                        value: "${widget.mixShipping.getTotalPaidStringCurrency()} DH",
                      ),
                      StrippedTableItem(
                        label: '${tr!.amountDue} (AED)',
                        value: "${widget.mixShipping.getTotalDueStringAED()} DH",
                      ),
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _invoiceNoSection() {
    return Row(
      children: [
        Icon(
          Symbols.tag,
          size: 18,
          color: Theme.of(context).colorScheme.primary,
          weight: 700,
        ),
        const SizedBox(
          width: 2,
        ),
        SelectableText(
          widget.mixShipping.invoiceNumber,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w700,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  Widget _datesSection() {
    return Row(
      children: [
        //issue date
        Expanded(
          child: CIconText(
            icon: Symbols.edit_calendar,
            text: DateFormat(cardsDateFormat).format(
              widget.mixShipping.invDate,
            ),
          ),
        ),
        //due date
        if (widget.mixShipping.invDueDate != null)
          Expanded(
            child: CIconText(
              icon: Symbols.calendar_clock,
              text: widget.mixShipping.invDueDate != null
                  ? DateFormat(cardsDateFormat).format(
                      widget.mixShipping.invDueDate!,
                    )
                  : '',
            ),
          ),
      ],
    );
  }
}
