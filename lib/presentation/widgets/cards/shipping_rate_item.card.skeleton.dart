import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/card_style.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_stripped_table.widget.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ShippingRateItemCardSkeleton extends StatefulWidget {
  const ShippingRateItemCardSkeleton({
    super.key,
  });

  @override
  State<ShippingRateItemCardSkeleton> createState() => _ShippingRateItemCardSkeletonState();
}

class _ShippingRateItemCardSkeletonState extends State<ShippingRateItemCardSkeleton> {
  @override
  Widget build(BuildContext context) {
    return CardStyle(
      onTap: null,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _locationsSection(),
                      // type
                    ],
                  ),

                  const SizedBox(
                    height: 16,
                  ),
                  // ammounts
                  Skeletonizer(
                    child: CStrippedTable(
                      data: [
                        StrippedTableItem(
                          label: 'tr!.hc40',
                          value: "\$ 1000",
                        ),
                        StrippedTableItem(
                          label: 'tr!.hc45',
                          value: "\$ 1000",
                        ),
                      ],
                    ),
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _locationsSection() {
    return Skeletonizer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'MMMMMMMMMMMMMM',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 4,
          ),
          Row(
            children: [
              Text(
                'MMMMMMMMMMMMM',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
