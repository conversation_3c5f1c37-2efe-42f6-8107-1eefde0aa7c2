import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/configs/themes/colors.theme.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/widgets/badges/received_payment_state_badge.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/bottom_sheets/auction_payments/auction_payments_payments.bottom_sheet.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/card_style.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_stripped_table.widget.dart';

class AuctionPaymentItemCard extends StatefulWidget {
  const AuctionPaymentItemCard({
    super.key,
    required this.auctionPayment,
  });

  final AuctionPaymentModel auctionPayment;

  @override
  State<AuctionPaymentItemCard> createState() => _AuctionPaymentItemCardState();
}

class _AuctionPaymentItemCardState extends State<AuctionPaymentItemCard> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  late final double _amountApplied = widget.auctionPayment.getAmountApplied();
  late final double _balance = widget.auctionPayment.getBalance();
  @override
  Widget build(BuildContext context) {
    return CardStyle(
      onTap: () async {
        await showModalBottomSheet(
          isScrollControlled: true,
          useSafeArea: true,
          context: context,
          showDragHandle: true,
          constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * .8),
          builder: (context) => AuctionPaymentsPaymentsBottomSheet(
            payments: widget.auctionPayment.payments,
          ),
        );
      },
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Row(
                          children: [
                            Icon(
                              Symbols.tag,
                              size: 18,
                              color: Theme.of(context).colorScheme.primary,
                              weight: 700,
                            ),
                            const SizedBox(
                              width: 2,
                            ),
                            Flexible(
                              flex: 1,
                              child: SelectableText(
                                widget.auctionPayment.lotNumber,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w700,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                                maxLines: 1,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // state
                      if (widget.auctionPayment.payments.isNotEmpty)
                        PaymentReceivedStateBadge(state: widget.auctionPayment.payments[0].state),
                    ],
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Row(
                      children: [
                        Icon(
                          Symbols.directions_car,
                          size: 18,
                          color: Theme.of(context).colorScheme.primary,
                          weight: 700,
                        ),
                        const SizedBox(
                          width: 2,
                        ),
                        SelectableText(
                          widget.auctionPayment.vin,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w700,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  // amounts
                  CStrippedTable(
                    data: [
                      StrippedTableItem(
                        label: tr!.vehiclePrice,
                        value: '\$${widget.auctionPayment.price.toStringAsFixed(0)}',
                      ),
                      StrippedTableItem(
                        label: tr!.amountApplied,
                        value: '\$${_amountApplied.toStringAsFixed(0)}',
                        valueWidget: Text(
                          '\$${_amountApplied.toStringAsFixed(0)}',
                          style: TextStyle(
                            color: _amountApplied > 0 ? Colors.lightBlue[600] : dashBoardRedColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      StrippedTableItem(
                        label: tr!.balance,
                        value: '\$${_balance.toStringAsFixed(0)}',
                        valueWidget: Text(
                          '\$${_balance.toStringAsFixed(0)}',
                          style: TextStyle(
                            color: _balance >= 0 ? Theme.of(context).colorScheme.primary : dashBoardRedColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
