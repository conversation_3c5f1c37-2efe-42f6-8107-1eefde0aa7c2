import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/badges/shipping_rate_type_badge.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/card_style.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_stripped_table.widget.dart';

class ShippingRateItemCard extends StatefulWidget {
  const ShippingRateItemCard({
    super.key,
    required this.shippingRate,
  });

  final ShippingRate shippingRate;

  @override
  State<ShippingRateItemCard> createState() => _ShippingRateItemCardState();
}

class _ShippingRateItemCardState extends State<ShippingRateItemCard> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return CardStyle(
      onTap: null,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _locationsSection(),
                      // type
                      ShippingRateTypeBadge(type: widget.shippingRate.shippingRateType),
                    ],
                  ),

                  const SizedBox(
                    height: 16,
                  ),
                  // ammounts
                  CStrippedTable(
                    data: [
                      if (widget.shippingRate.rate_40hc != 0)
                        StrippedTableItem(
                          label: tr!.hc40,
                          value: "\$${widget.shippingRate.rate_40hc.formatComma()}",
                        ),
                      if (widget.shippingRate.rate_45hc != 0)
                        StrippedTableItem(
                          label: tr!.hc45,
                          value: "\$${widget.shippingRate.rate_45hc.formatComma()}",
                        ),
                      if (widget.shippingRate.mixRate != 0)
                        StrippedTableItem(
                          label: tr!.mix,
                          value: "\$${widget.shippingRate.mixRate.formatComma()} + ${tr!.towing}",
                        ),
                      if (widget.shippingRate.sedanRate != 0)
                        StrippedTableItem(
                          label: tr!.sedanRate,
                          value: "\$${widget.shippingRate.sedanRate.formatComma()}",
                        ),
                      if (widget.shippingRate.suvRate != 0)
                        StrippedTableItem(
                          label: tr!.suvRate,
                          value: "\$${widget.shippingRate.suvRate.formatComma()}",
                        ),
                      if (widget.shippingRate.sedanDismantleRate != 0)
                        StrippedTableItem(
                          label: tr!.sedanDismantleRate,
                          value: "\$${widget.shippingRate.sedanDismantleRate.formatComma()}",
                        ),
                      if (widget.shippingRate.suvDismantleRate != 0)
                        StrippedTableItem(
                          label: tr!.suvDismantleRate,
                          value: "\$${widget.shippingRate.suvDismantleRate.formatComma()}",
                        ),
                      if (widget.shippingRate.suvDismantleRate != 0)
                        StrippedTableItem(
                          label: tr!.tdsAmount,
                          value: "\$${widget.shippingRate.tdsAmount.formatComma()}",
                        ),
                      if (widget.shippingRate.companyType != null)
                        StrippedTableItem(
                          label: tr!.companyType,
                          value: widget.shippingRate.companyType ?? '',
                        ),
                      if (widget.shippingRate.vehicleType != null)
                        StrippedTableItem(
                          label: tr!.vehicleType,
                          value: widget.shippingRate.vehicleType ?? '',
                        ),
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _locationsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '${tr!.from}:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(
              width: 4,
            ),
            Icon(
              Symbols.location_on,
              size: 20,
              weight: 600,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(
              width: 4,
            ),
            Text(
              widget.shippingRate.location,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w700,
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: .7),
              ),
            ),
          ],
        ),
        const SizedBox(
          height: 4,
        ),
        Row(
          children: [
            const SizedBox(
              width: 2,
            ),
            SvgPicture.asset(
              'assets/svgs/bottom-right-turn-icon.svg',
              theme: SvgTheme(
                currentColor: Theme.of(context).colorScheme.primary,
              ),
              width: 17,
            ),
            const SizedBox(
              width: 4,
            ),
            Text(
              '${tr!.to}:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(
              width: 4,
            ),
            Icon(
              Symbols.where_to_vote,
              size: 20,
              weight: 600,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(
              width: 4,
            ),
            Text(
              widget.shippingRate.destination,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w700,
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: .7),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
