import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_icon_text.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/card_style.widget.dart';
import 'package:skeletonizer/skeletonizer.dart';

class VehicleItemCardSkeleton extends StatefulWidget {
  const VehicleItemCardSkeleton({
    super.key,
  });

  @override
  State<VehicleItemCardSkeleton> createState() => _VehicleItemCardSkeletonState();
}

class _VehicleItemCardSkeletonState extends State<VehicleItemCardSkeleton> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return CardStyle(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            _imageSection(),
            const SizedBox(
              width: 10,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // lot
                      _lotSection(),
                    ],
                  ),
                  // description
                  const Padding(
                    padding: EdgeInsets.symmetric(vertical: 4),
                    child: Skeletonizer(
                      child: Text(
                        'Deserunt ex occaecat sint eiusmod reprehenderit qui',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w700,
                          height: 1.3,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.start,
                      ),
                    ),
                  ),
                  const Skeletonizer(
                    child: CIconText(
                      icon: Symbols.directions_car,
                      text: 'ssssssssssssssssssss',
                    ),
                  ),
                  // dates
                  _datesSection(),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _imageSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
        ),
        clipBehavior: Clip.hardEdge,
        child: Skeletonizer(
          child: Skeleton.replace(
            height: 100,
            width: 100,
            replace: true,
            child: Container(),
          ),
        ),
      ),
    );
  }

  Widget _lotSection() {
    return Skeletonizer(
      child: Row(
        children: [
          Icon(
            Symbols.tag,
            size: 18,
            color: Theme.of(context).colorScheme.primary,
            weight: 700,
          ),
          const SizedBox(
            width: 2,
          ),
          Text(
            'sssssssssssssssssssssss',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w700,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _datesSection() {
    return const Skeletonizer(
      child: Row(
        children: [
          //purchase date
          // Expanded(
          //   child: CIconText(
          //     icon: Symbols.calendar_add_on,
          //     text: 'sssssssssssssss',
          //   ),
          // ),
          //delivery date
          Expanded(
            child: CIconText(
              icon: Symbols.calendar_add_on,
              text: 'sssssssssssssss',
            ),
          ),
          //load date
        ],
      ),
    );
  }
}
