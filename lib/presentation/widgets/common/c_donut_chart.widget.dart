import 'package:d_chart/d_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:skeletonizer/skeletonizer.dart';

class CDountChart extends StatefulWidget {
  const CDountChart({
    super.key,
    required this.graphData,
    this.loading = false,
  });
  final List<OrdinalData> graphData;
  final bool loading;
  @override
  State<CDountChart> createState() => _CDountChartState();
}

class _CDountChartState extends State<CDountChart> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  Color getIndexColor(int? index) {
    switch (index) {
      case 0:
        return Colors.blue.shade500;
      case 1:
        return Colors.green.shade500;
      case 2:
        return Colors.red.shade400;
      default:
        return Colors.blue.shade500;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SizedBox(
          width: 128,
          height: 128,
          child: Transform.scale(
            scale: 1.3,
            child: widget.loading
                ? const Center(
                    child: SizedBox(
                      height: 74,
                      width: 74,
                      child: CircularProgressIndicator(
                        strokeWidth: 14,
                      ),
                    ),
                  )
                : DChartPieO(
                    data: widget.graphData,
                    // configRenderPie: const ConfigRenderPie(
                    //   arcWidth: 14,
                    // ),
                    configSeriesPie: ConfigSeriesPieO(
                      arcWidth: 14,
                      customColor: (group, data, index) {
                        return getIndexColor(index);
                      },
                    ),
                  ),
          ),
        ),
        Expanded(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            width: MediaQuery.of(context).size.width / 2,
            child: Column(
              children: widget.graphData
                  .asMap()
                  .entries
                  .map(
                    (entry) => _labelItem(
                      color: getIndexColor(entry.key),
                      label: entry.value.domain,
                      value: entry.value.measure! > 0
                          ? "\$${NumberFormat('###,###,###,###').format(entry.value.measure)}"
                          : '\$ 0',
                    ),
                  )
                  .toList(),
            ),
          ),
        )
      ],
    );
  }

  Widget _labelItem({
    required Color? color,
    required String label,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Container(
            height: 8,
            width: 8,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          const SizedBox(
            width: 8,
          ),
          Expanded(
            child: Skeletonizer(
              enabled: widget.loading,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      label,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      widget.loading ? 'PlaceHolder' : value,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w700,
                        color: Theme.of(context).brightness == Brightness.light ? Colors.black54 : Colors.white70,
                      ),
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.end,
                    ),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
