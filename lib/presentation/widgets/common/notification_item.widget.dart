import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:notifications_repository/notifications_repository.dart';
import 'package:pgl_mobile_app/configs/themes/colors.theme.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:pgl_mobile_app/constants/constants.dart';
import 'package:pgl_mobile_app/presentation/screens/misc/images_preview.screen.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

class NotificationItem extends StatefulWidget {
  const NotificationItem({
    super.key,
    required this.notification,
  });

  final NotificationModel notification;
  @override
  State<NotificationItem> createState() => _NotificationItemState();
}

class _NotificationItemState extends State<NotificationItem> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            width: 1,
            color: Colors.grey.withValues(alpha: .2),
          ),
        ),
      ),
      child: Material(
        color: widget.notification.seenAt == null
            ? Theme.of(context).brightness == Brightness.light
                ? cardBgColorLight
                : cardBgColorDark
            : null,
        child: ListTile(
          onTap: () {
            onNotificationTap(widget.notification);
          },
          title: Text(
            widget.notification.title,
          ),
          subtitle: Column(
            children: [
              HtmlWidget(
                widget.notification.description,
                onTapUrl: (url) async {
                  launchUrl(Uri.parse(url));
                  return true;
                },
                onTapImage: (ImageMetadata imageMetadata) {
                  Get.toNamed(
                    imagePreview,
                    arguments: ImagesPreviewArguments(
                      urls: [imageMetadata.sources.first.url],
                      initialIndex: 0,
                      showListInGalley: false,
                      title: tr!.preview,
                    ),
                  );
                },
              ),
              const SizedBox(
                height: 4,
              ),
              Row(
                children: [
                  Icon(
                    Symbols.schedule,
                    color: Theme.of(context).colorScheme.primary,
                    size: 18,
                  ),
                  const SizedBox(
                    width: 4,
                  ),
                  Text(
                    widget.notification.getTimeAgo(),
                    style:
                        TextStyle(fontSize: 12, color: Theme.of(context).colorScheme.onSurface.withValues(alpha: .7)),
                  )
                ],
              ),
            ],
          ),
          leading: Container(
            height: 40,
            width: 40,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(40),
              color: Theme.of(context).colorScheme.primary.withValues(alpha: .1),
            ),
            child: Center(
              child: _getNotificationIcon(
                widget.notification.notificationType,
                widget.notification.title,
              ),
            ),
          ),
          // trailing: SizedBox(
          //   width: 40,
          //   height: 40,
          //   child: IconButton(
          //     padding: EdgeInsets.zero,
          //     onPressed: () {},
          //     icon: const Icon(
          //       Symbols.more_vert,
          //       weight: 700,
          //       size: 24,
          //     ),
          //   ),
          // ),
          isThreeLine: true,
        ),
      ),
    );
  }

  Widget _getNotificationIcon(NotificationType type, String title) {
    switch (type) {
      case NotificationType.announcement:
        return Icon(
          Symbols.campaign,
          color: Theme.of(context).colorScheme.primary,
        );
      case NotificationType.shippingRate:
      case NotificationType.mixShippingRate:
        return Icon(
          Symbols.attach_money,
          color: Theme.of(context).colorScheme.primary,
        );
      case NotificationType.arrivalNotice:
        return Icon(
          Symbols.alarm,
          color: Theme.of(context).colorScheme.primary,
        );
      case NotificationType.transaction:
        return Icon(
          Symbols.article,
          color: Theme.of(context).colorScheme.primary,
        );
    }
  }
}

Future? onNotificationTap(NotificationModel notificationModel) {
  switch (notificationModel.notificationType) {
    case NotificationType.announcement:
      return Get.toNamed(annoncements);
    case NotificationType.shippingRate:
      return Get.toNamed(shippingRates);
    case NotificationType.arrivalNotice:
      return Get.toNamed(arrivalNoticeSingle, arguments: notificationModel);
    case NotificationType.transaction:
      return Get.toNamed(paymentsList);
    default:
      return null;
  }
}
