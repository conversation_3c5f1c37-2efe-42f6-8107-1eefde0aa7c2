import 'package:flutter/material.dart';

class CToast extends StatefulWidget {
  const CToast({
    super.key,
    this.backgroundColor,
    this.textStyle = const TextStyle(
      color: Colors.white,
    ),
    required this.text,
    this.before,
  });

  final Color? backgroundColor;
  final TextStyle textStyle;
  final String text;
  final Widget? before;

  @override
  State<CToast> createState() => _CToastState();
}

class _CToastState extends State<CToast> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25.0),
        color: widget.backgroundColor ?? Colors.black.withValues(alpha: .5),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.before != null) ...[
            widget.before ?? Container(),
            const SizedBox(
              width: 8,
            ),
          ],
          Text(
            widget.text,
            style: widget.textStyle,
          ),
        ],
      ),
    );
  }
}
