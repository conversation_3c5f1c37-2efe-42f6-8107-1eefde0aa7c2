import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:skeletonizer/skeletonizer.dart';

class AnnouncementItemSkeleton extends StatelessWidget {
  const AnnouncementItemSkeleton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      child: Container(
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              width: 1,
              color: Colors.grey.withValues(alpha: .2),
            ),
          ),
        ),
        child: Material(
          color: null,
          child: ListTile(
            onTap: () {},
            title: const Text(
              'widget.announcement.title',
            ),
            subtitle: Column(
              children: [
                const Text(
                  "widget.announcement.description widget.announcement.description widget.announcement.description widget.announcement.description",
                  style: TextStyle(
                    fontSize: 14,
                  ),
                ),
                const SizedBox(
                  height: 4,
                ),
                Row(
                  children: [
                    Icon(
                      Symbols.schedule,
                      color: Theme.of(context).colorScheme.primary,
                      size: 18,
                    ),
                    const SizedBox(
                      width: 4,
                    ),
                    Text(
                      'widget.announcement',
                      style:
                          TextStyle(fontSize: 12, color: Theme.of(context).colorScheme.onSurface.withValues(alpha: .7)),
                    )
                  ],
                )
              ],
            ),
            leading: Container(
              height: 40,
              width: 40,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(40),
              ),
              clipBehavior: Clip.hardEdge,
              child: Skeleton.replace(
                height: 40,
                width: 40,
                child: Container(),
              ),
            ),
            isThreeLine: true,
          ),
        ),
      ),
    );
  }
}
