import 'package:announcements_repository/announcements_repository.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/configs/themes/colors.theme.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:pgl_mobile_app/constants/constants.dart';
import 'package:pgl_mobile_app/presentation/screens/misc/images_preview.screen.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

class AnnouncementItem extends StatefulWidget {
  const AnnouncementItem({
    super.key,
    required this.announcement,
  });

  final Announcement announcement;
  @override
  State<AnnouncementItem> createState() => _AnnouncementItemState();
}

class _AnnouncementItemState extends State<AnnouncementItem> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            width: 1,
            color: Colors.grey.withValues(alpha: .2),
          ),
        ),
      ),
      child: Material(
        color: widget.announcement.seenAt == null
            ? Theme.of(context).brightness == Brightness.light
                ? cardBgColorLight
                : cardBgColorDark
            : null,
        child: ListTile(
          onTap: () {},
          title: Text(
            widget.announcement.announcementData.title,
          ),
          subtitle: Column(
            children: [
              HtmlWidget(
                widget.announcement.announcementData.description,
                onTapUrl: (url) async {
                  launchUrl(Uri.parse(url));
                  return true;
                },
                onTapImage: (ImageMetadata imageMetadata) {
                  Get.toNamed(
                    imagePreview,
                    arguments: ImagesPreviewArguments(
                      urls: [imageMetadata.sources.first.url],
                      initialIndex: 0,
                      showListInGalley: false,
                      title: tr!.preview,
                    ),
                  );
                },
              ),
              const SizedBox(
                height: 4,
              ),
              Row(
                children: [
                  Icon(
                    Symbols.schedule,
                    color: Theme.of(context).colorScheme.primary,
                    size: 18,
                  ),
                  const SizedBox(
                    width: 4,
                  ),
                  Text(
                    widget.announcement.getTimeAgo(),
                    style:
                        TextStyle(fontSize: 12, color: Theme.of(context).colorScheme.onSurface.withValues(alpha: .7)),
                  )
                ],
              )
            ],
          ),
          leading: Container(
            height: 40,
            width: 40,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(40),
              color: Theme.of(context).colorScheme.primary.withValues(alpha: .1),
            ),
            child: Center(
              child: Text(
                widget.announcement.announcementData.title[0],
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          // trailing: SizedBox(
          //   width: 40,
          //   height: 40,
          //   child: IconButton(
          //     padding: EdgeInsets.zero,
          //     onPressed: () {},
          //     icon: const Icon(
          //       Symbols.more_vert,
          //       weight: 700,
          //       size: 24,
          //     ),
          //   ),
          // ),
          isThreeLine: true,
        ),
      ),
    );
  }
}
