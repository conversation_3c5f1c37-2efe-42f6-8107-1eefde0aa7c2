import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:material_symbols_icons/symbols.dart';

class CTextField extends StatefulWidget {
  const CTextField({
    super.key,
    required this.label,
    this.labelStyle,
    this.style,
    this.focusNode,
    this.initialValue,
    this.icon,
    this.iconSize,
    this.error,
    this.obscureText,
    this.minLines,
    this.maxLines = 1,
    this.contentPadding,
    this.controller,
    this.onChanged,
    this.onFieldSubmitted,
    this.autofillHints = const [],
    this.prefixIconConstraints,
    this.textInputType,
    this.textInputFormatters,
    this.loading = false,
    this.readOnly = false,
    this.onTap,
    this.borderRadius = 32,
    this.validator,
    this.enabled = true,
  });

  final String label;
  final TextStyle? labelStyle;
  final TextStyle? style;
  final FocusNode? focusNode;
  final String? initialValue;
  final IconData? icon;
  final double? iconSize;
  final String? error;
  final bool? obscureText;
  final int? minLines;
  final int? maxLines;
  final EdgeInsetsGeometry? contentPadding;
  final TextEditingController? controller;
  final void Function(String text)? onChanged;
  final void Function(String text)? onFieldSubmitted;
  final void Function()? onTap;
  final List<String> autofillHints;
  final BoxConstraints? prefixIconConstraints;
  final TextInputType? textInputType;
  final List<TextInputFormatter>? textInputFormatters;
  final bool loading;
  final bool readOnly;
  final double? borderRadius;
  final String? Function(String? value)? validator;
  final bool enabled;
  @override
  State<CTextField> createState() => _CTextFieldState();
}

class _CTextFieldState extends State<CTextField> {
  bool _obscureText = true;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      keyboardType: widget.textInputType,
      inputFormatters: widget.textInputFormatters,
      style: widget.style,
      focusNode: widget.focusNode,
      initialValue: widget.initialValue,
      obscureText: widget.obscureText != null ? _obscureText : false,
      controller: widget.controller,
      onChanged: widget.onChanged,
      onTap: widget.onTap,
      minLines: widget.minLines,
      maxLines: widget.maxLines,
      onFieldSubmitted: widget.onFieldSubmitted,
      autofillHints: widget.autofillHints,
      validator: widget.validator,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      decoration: InputDecoration(
        contentPadding: widget.contentPadding,
        errorText: widget.error,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(widget.borderRadius ?? 32),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(widget.borderRadius ?? 32),
          borderSide: BorderSide(
            color: Theme.of(context).brightness == Brightness.light ? Colors.grey.shade300 : Colors.grey.shade800,
          ),
        ),
        labelText: widget.label,
        labelStyle: widget.labelStyle,
        alignLabelWithHint: true,
        prefixIcon: widget.icon != null
            ? Icon(
                widget.icon,
                size: widget.iconSize,
              )
            : null,
        prefixIconColor: WidgetStateColor.resolveWith(
          _getInputIconColor,
        ),
        prefixIconConstraints: widget.prefixIconConstraints,
        suffixIcon: widget.obscureText == true
            ? Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                child: IconButton(
                  icon: Icon(
                    _obscureText ? Symbols.visibility : Symbols.visibility_off,
                  ),
                  onPressed: () {
                    setState(() {
                      _obscureText = !_obscureText;
                    });
                  },
                ),
              )
            : widget.loading
                ? Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: const Align(
                      widthFactor: 1.0,
                      child: SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                        ),
                      ),
                    ),
                  )
                : null,
      ),
    );
  }

  Color _getInputIconColor(Set<WidgetState> states) {
    if (states.contains(WidgetState.error)) {
      return Theme.of(context).colorScheme.error;
    } else if (states.contains(WidgetState.focused)) {
      return Theme.of(context).colorScheme.primary;
    } else {
      return Theme.of(context).colorScheme.primary;
    }
  }
}
