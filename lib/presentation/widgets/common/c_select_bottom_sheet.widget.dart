import 'package:flutter/material.dart';
import 'package:get/route_manager.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/bottom_sheet_style2.widget.dart';

class CSelectBottomSheet extends StatefulWidget {
  const CSelectBottomSheet({
    super.key,
    required this.items,
    required this.selectedIndex,
    required this.changeSelectedIndex,
    this.title,
  });
  final List<SelectItem> items;
  final int selectedIndex;
  final void Function(int index) changeSelectedIndex;
  final String? title;
  @override
  State<CSelectBottomSheet> createState() => _CSelectBottomSheetState();
}

class _CSelectBottomSheetState extends State<CSelectBottomSheet> {
  late int selectedIndex;

  @override
  void initState() {
    super.initState();
    setState(() {
      selectedIndex = widget.selectedIndex;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: BottomSheetStyle2(
        children: [
          Text(
            widget.title ?? '',
            style: const TextStyle(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(
            height: 16,
          ),
          ...widget.items
              .asMap()
              .map(
                (int index, SelectItem item) => MapEntry(
                  index,
                  Container(
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          width: 1.0,
                          color: Theme.of(context).colorScheme.secondary.withValues(alpha: .2),
                        ), // Bottom border
                      ),
                    ),
                    child: ListTile(
                      title: widget.items[index].labelWidget ??
                          Text(
                            widget.items[index].label,
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                      trailing: index == selectedIndex
                          ? const Icon(
                              Symbols.check_circle_filled,
                              fill: 1,
                            )
                          : null,
                      contentPadding: const EdgeInsets.symmetric(horizontal: 8),
                      selected: index == selectedIndex,
                      dense: true,
                      onTap: () {
                        widget.changeSelectedIndex(index);
                        setState(() {
                          selectedIndex = index;
                        });
                        Get.back();
                      },
                    ),
                  ),
                ),
              )
              .values,
          const SizedBox(
            height: 16,
          ),
        ],
      ),
    );
  }
}
