import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

class NoItemsFound extends StatefulWidget {
  const NoItemsFound({
    super.key,
    required this.svgIcon,
    required this.onRefresh,
    required this.itemName,
    this.iconData,
  });

  final String svgIcon;
  final void Function() onRefresh;
  final String itemName;
  final IconData? iconData;

  @override
  State<NoItemsFound> createState() => _NoItemsFoundState();
}

class _NoItemsFoundState extends State<NoItemsFound> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        widget.iconData != null
            ? Icon(
                widget.iconData,
                size: 80,
                color: Theme.of(context).colorScheme.primary,
              )
            : SvgPicture.asset(
                widget.svgIcon,
                width: 80,
                height: 80,
                theme: SvgTheme(
                  currentColor: Theme.of(context).colorScheme.primary,
                ),
              ),
        const SizedBox(
          height: 8,
        ),
        Center(
          child: Text(
            tr!.noItemsFound(widget.itemName),
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        const SizedBox(
          height: 8,
        ),
        Container(
          constraints: const BoxConstraints(
            maxWidth: 120,
          ),
          child: FilledButton(
            onPressed: widget.onRefresh,
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Refresh',
                      style: TextStyle(
                        fontSize: 12,
                      ),
                    ),
                    SizedBox(
                      width: 4,
                    ),
                    Icon(
                      Symbols.refresh,
                      size: 18,
                    )
                  ],
                ),
              ],
            ),
          ),
        )
      ],
    );
  }
}
