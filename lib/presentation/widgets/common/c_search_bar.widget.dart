import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/configs/themes/colors.theme.dart';

class CSearchBar extends StatefulWidget {
  const CSearchBar({
    super.key,
    required this.label,
    this.onChanged,
    this.onTap,
    this.onSubmitted,
    this.clearable = true,
    this.initailValue,
  });
  final String label;

  final void Function(String text)? onChanged;
  final void Function()? onTap;
  final void Function(String text)? onSubmitted;
  final bool clearable;
  final String? initailValue;
  @override
  State<CSearchBar> createState() => _CSearchBarState();
}

class _CSearchBarState extends State<CSearchBar> {
  late final TextEditingController controller;

  @override
  void initState() {
    controller = TextEditingController(
      text: widget.initailValue,
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 44,
      child: SearchBar(
        shape: WidgetStatePropertyAll(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(100),
            side: BorderSide(
              color: Theme.of(context).brightness == Brightness.light ? Colors.grey.shade300 : Colors.grey.shade800,
              width: 1,
            ),
          ),
        ),
        onTap: widget.onTap,
        onSubmitted: widget.onSubmitted,
        surfaceTintColor: WidgetStatePropertyAll(
            Theme.of(context).brightness == Brightness.light ? cardBgColorLight : cardBgColorDark),
        hintText: widget.label,
        hintStyle: const WidgetStatePropertyAll(TextStyle(fontSize: 14)),
        textStyle: const WidgetStatePropertyAll(TextStyle(fontSize: 14)),
        padding: const WidgetStatePropertyAll<EdgeInsets>(EdgeInsets.symmetric(horizontal: 8.0)),
        controller: controller,
        onChanged: widget.onChanged,
        elevation: const WidgetStatePropertyAll(0),
        backgroundColor: WidgetStatePropertyAll(
          Theme.of(context).brightness == Brightness.light ? cardBgColorLight : cardBgColorDark,
        ),
        trailing: <Widget>[
          if (controller.text.isEmpty || !widget.clearable)
            SizedBox(
              height: 38,
              width: 38,
              child: IconButton(
                onPressed: () {
                  if (widget.onSubmitted != null) {
                    widget.onSubmitted!(controller.text);
                  }
                },
                icon: const Icon(
                  Symbols.search,
                  size: 20,
                ),
              ),
            )
          else if (controller.text.isNotEmpty && widget.clearable)
            SizedBox(
              height: 38,
              width: 38,
              child: IconButton(
                onPressed: () {
                  controller.clear();
                  if (widget.onChanged != null) {
                    widget.onChanged!('');
                  }
                },
                icon: const Icon(
                  Symbols.close,
                  size: 20,
                ),
              ),
            )
        ],
      ),
    );
  }
}
