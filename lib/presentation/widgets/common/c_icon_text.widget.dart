import 'package:flutter/material.dart';

class CIconText extends StatefulWidget {
  const CIconText({
    super.key,
    required this.text,
    required this.icon,
    this.selectable = false,
  });

  final String text;
  final IconData icon;
  final bool selectable;
  @override
  State<CIconText> createState() => _CIconTextState();
}

class _CIconTextState extends State<CIconText> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 1),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(
            widget.icon,
            size: 18,
            color: Theme.of(context).colorScheme.primary,
            weight: 600,
          ),
          const SizedBox(
            width: 4,
          ),
          Expanded(
            child: widget.selectable
                ? SelectableText(
                    widget.text,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w700,
                      color: Theme.of(context).brightness == Brightness.light ? Colors.black54 : Colors.white70,
                      overflow: TextOverflow.ellipsis,
                    ),
                    maxLines: 1,
                  )
                : Text(
                    widget.text,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w700,
                      color: Theme.of(context).brightness == Brightness.light ? Colors.black54 : Colors.white70,
                      overflow: TextOverflow.ellipsis,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
          ),
        ],
      ),
    );
  }
}
