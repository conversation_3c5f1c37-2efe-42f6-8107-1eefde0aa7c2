import 'package:flutter/material.dart';

class BottomSheetStyle extends StatefulWidget {
  const BottomSheetStyle({
    super.key,
    this.header,
    required this.children,
    this.footer,
  });

  final List<Widget> children;
  final Widget? header;
  final Widget? footer;
  @override
  State<BottomSheetStyle> createState() => _BottomSheetStyleState();
}

class _BottomSheetStyleState extends State<BottomSheetStyle> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          if (widget.header != null) widget.header ?? Container(),
          Expanded(
            child: Scrollbar(
              child: SingleChildScrollView(
                child: Column(
                  children: [const Row(), ...widget.children],
                ),
              ),
            ),
          ),
          if (widget.footer != null) widget.footer ?? Container(),
        ],
      ),
    );
  }
}
