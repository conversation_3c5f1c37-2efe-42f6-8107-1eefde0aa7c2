// ignore_for_file: use_build_context_synchronously

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:get/get.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/constants/routes.const.dart';
import 'package:pgl_mobile_app/exports/blocs.exports.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/firebase_messaging_config.dart';
import 'package:url_launcher/url_launcher.dart';

List<int> destinationMixRates = [12, 22, 24];

class NavigationItemData {
  final String label;
  final IconData icon;
  final Function? onSelected;
  final Color? color;
  final String? badge;
  final String? trailingBadge;
  final List<NavigationItemData> children;
  NavigationItemData({
    required this.label,
    required this.icon,
    this.onSelected,
    this.color,
    this.badge,
    this.trailingBadge,
    this.children = const [],
  });
}

class CNavigationDrawer extends StatefulWidget {
  const CNavigationDrawer({
    super.key,
    required this.changeTab,
    required this.selectedNavigation,
    required this.changeSelectedNavigation,
    required this.firebaseMessagingConfig,
    required this.closeDrawer,
  });

  final void Function(int value) changeTab;
  final void Function(int value) changeSelectedNavigation;
  final void Function() closeDrawer;
  final int selectedNavigation;
  final FirebaseMessagingConfig firebaseMessagingConfig;

  @override
  State<CNavigationDrawer> createState() => _CNavigationDrawerState();
}

class _CNavigationDrawerState extends State<CNavigationDrawer> {
  late StreamSubscription dashboardCount;
  late int countAnnouncements;

  @override
  void initState() {
    countAnnouncements = context.read<DashboardBloc>().state.countAnnouncements;
    dashboardCount = context.read<DashboardBloc>().stream.listen((event) {
      setState(() {
        countAnnouncements = event.countAnnouncements;
      });
    });
    super.initState();
  }

  @override
  void setState(VoidCallback fn) {
    if (mounted) {
      super.setState(fn);
    }
  }

  late AppLocalizations? tr = AppLocalizations.of(context);

  late List<NavigationItemData> navigationItems = [
    NavigationItemData(
      label: context.read<AuthenticationBloc>().state.user.fullname,
      icon: Symbols.account_circle,
    ),
    NavigationItemData(
      label: tr!.home,
      icon: Symbols.home,
      onSelected: () {
        widget.changeTab(0);
        widget.closeDrawer();
      },
    ),
    NavigationItemData(
      label: tr!.vehicles,
      icon: Symbols.directions_car,
      onSelected: () {
        widget.changeTab(1);
        widget.closeDrawer();
      },
    ),
    NavigationItemData(
      label: tr!.shipments,
      icon: Symbols.local_shipping,
      onSelected: () {
        widget.changeTab(2);
        widget.closeDrawer();
      },
    ),
    NavigationItemData(
      label: tr!.invoices,
      icon: Symbols.article,
      onSelected: () {
        widget.changeTab(3);
        widget.closeDrawer();
      },
    ),
    if (context.read<AuthenticationBloc>().state.user.mixShipping)
      NavigationItemData(
        label: tr!.mixShipping,
        icon: Symbols.local_shipping,
        onSelected: () {
          widget.changeTab(4);
          widget.closeDrawer();
        },
      ),
    // if (context.read<AuthenticationBloc>().state.user.hasStatements)
    //   NavigationItemData(
    //     label: tr!.statements,
    //     icon: Symbols.article,
    //     onSelected: () {
    //       widget.closeDrawer();
    //       Get.toNamed(statementsList);
    //     },
    //   ),
    NavigationItemData(
      label: tr!.payments,
      icon: Symbols.receipt_long,
      onSelected: () {
        widget.closeDrawer();
        Get.toNamed(paymentsList);
      },
      children: [
        NavigationItemData(
            label: tr!.freightPayments,
            icon: Symbols.local_shipping,
            onSelected: () {
              // widget.changeTab(4);
              // widget.closeDrawer();
            },
            badge: context.read<DashboardBloc>().state.countAllPayments > 0
                ? context.read<DashboardBloc>().state.countAllPayments.toString()
                : null,

            // trailingBadge: context.read<DashboardBloc>().state.countAllPayments > 0
            //     ? context.read<DashboardBloc>().state.countAllPayments.toString()
            //     : null,
            children: [
              NavigationItemData(
                label: tr!.all,
                icon: Symbols.receipt_long,
                onSelected: () {
                  widget.closeDrawer();
                  context.read<PaymentsBloc>().add(
                        PaymentsFilterChanged(
                          paymentsFilter: PaymentsFilter(state: PaymentTab.all),
                        ),
                      );
                  Get.toNamed(paymentsList);
                },
                trailingBadge: context.read<DashboardBloc>().state.countAllPayments > 0
                    ? context.read<DashboardBloc>().state.countAllPayments.toString()
                    : null,
              ),
              NavigationItemData(
                label: tr!.approved,
                icon: Symbols.receipt_long,
                onSelected: () {
                  widget.closeDrawer();
                  context.read<PaymentsBloc>().add(
                        PaymentsFilterChanged(
                          paymentsFilter: PaymentsFilter(state: PaymentTab.approved),
                        ),
                      );
                  Get.toNamed(paymentsList);
                },
                trailingBadge: context.read<DashboardBloc>().state.countApprovedPayments > 0
                    ? context.read<DashboardBloc>().state.countApprovedPayments.toString()
                    : null,
              ),
              NavigationItemData(
                label: tr!.pending,
                icon: Symbols.receipt_long,
                onSelected: () {
                  widget.closeDrawer();
                  context.read<PaymentsBloc>().add(
                        PaymentsFilterChanged(
                          paymentsFilter: PaymentsFilter(state: PaymentTab.pending),
                        ),
                      );
                  Get.toNamed(paymentsList);
                },
                trailingBadge: context.read<DashboardBloc>().state.countPendingPayments > 0
                    ? context.read<DashboardBloc>().state.countPendingPayments.toString()
                    : null,
              ),
            ]),
        NavigationItemData(
          label: tr!.auctionPayments,
          icon: Symbols.credit_card,
          badge: context.read<DashboardBloc>().state.countAuctionPayments > 0
              ? context.read<DashboardBloc>().state.countAuctionPayments.toString()
              : null,
          onSelected: () {
            widget.closeDrawer();
            Get.toNamed(auctionPayments);
          },
        ),
      ],
    ),

    NavigationItemData(
      label: tr!.announcements,
      icon: Symbols.campaign,
      onSelected: () {
        widget.closeDrawer();
        Get.toNamed(annoncements);
      },
      badge: countAnnouncements > 0 ? "$countAnnouncements" : null,
    ),
    if (context.read<AuthenticationBloc>().state.user.showShippingRate &&
        context.read<AuthenticationBloc>().state.user.hasShippingRate)
      NavigationItemData(
        label: tr!.shippingRates,
        icon: Symbols.attach_money,
        onSelected: () {
          widget.closeDrawer();

          Get.toNamed(shippingRates);
        },
      ),
    if (context.read<AuthenticationBloc>().state.user.towingRates)
      NavigationItemData(
        label: tr!.towingRates,
        icon: Symbols.auto_towing,
        onSelected: () {
          widget.closeDrawer();
          Get.toNamed(towingRates);
        },
        children: [
          if (context.read<AuthenticationBloc>().state.user.companyObject != null &&
              context.read<AuthenticationBloc>().state.user.companyObject!.complete)
            NavigationItemData(
              label: tr!.complete,
              icon: Symbols.auto_towing,
              onSelected: () {
                widget.closeDrawer();
                context.read<TowingRatesBloc>().add(
                      TowingRatesFilterChanged(
                        towingRatesFilter: TowingRatesFilter(tab: 'complete'),
                      ),
                    );
                Get.toNamed(towingRates);
              },
            ),
          if (context.read<AuthenticationBloc>().state.user.companyObject != null &&
              (context.read<AuthenticationBloc>().state.user.companyObject!.completeHalfcut ||
                  context.read<AuthenticationBloc>().state.user.companyObject!.mixHalfcut))
            NavigationItemData(
              label: tr!.halfcut,
              icon: Symbols.auto_towing,
              onSelected: () {
                widget.closeDrawer();
                context.read<TowingRatesBloc>().add(
                      TowingRatesFilterChanged(
                        towingRatesFilter: TowingRatesFilter(tab: 'halfcut'),
                      ),
                    );
                Get.toNamed(towingRates);
              },
            ),
        ],
      ),
    NavigationItemData(
      label: tr!.mixShippingCalculator,
      icon: Symbols.calculate,
      onSelected: () {
        widget.closeDrawer();
        Get.toNamed(mixShippingCalculator);
      },
    ),
    NavigationItemData(
      label: tr!.tracking,
      icon: Symbols.schema,
      onSelected: () {
        widget.closeDrawer();
        Get.toNamed(tracking);
      },
    ),
    NavigationItemData(
      label: tr!.settings,
      icon: Symbols.settings,
      onSelected: () {
        widget.closeDrawer();
        Get.toNamed(settings);
      },
    ),
    // NavigationItemData(
    //   label: tr!.customerComplains,
    //   icon: Symbols.forum,
    // ),
    NavigationItemData(
      label: tr!.contactUs,
      icon: Symbols.call,
      onSelected: () {
        widget.closeDrawer();
        Get.toNamed(contactUs);
      },
    ),
    NavigationItemData(
      label: tr!.privacyPolicy,
      icon: Symbols.admin_panel_settings,
      onSelected: () {
        launchUrl(
          Uri.parse('https://peacegl.com/privacy-policy'),
        );
      },
    ),
    NavigationItemData(
      label: tr!.aboutUs,
      icon: Symbols.info,
      onSelected: () {
        launchUrl(
          Uri.parse('https://peacegl.com/about'),
        );
      },
    ),
    NavigationItemData(
      label: tr!.logout,
      icon: Symbols.logout,
      color: Theme.of(context).colorScheme.error,
      onSelected: () {
        return showDialog<void>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text(tr!.logoutOfYourAccount),
              actions: <Widget>[
                TextButton(
                  style: TextButton.styleFrom(
                    textStyle: Theme.of(context).textTheme.labelLarge,
                  ),
                  child: Text(tr!.cancel),
                  onPressed: () {
                    Get.back();
                  },
                ),
                BlocBuilder<AuthenticationBloc, AuthenticationState>(
                  builder: (context, state) {
                    return FilledButton(
                      style: TextButton.styleFrom(
                        textStyle: Theme.of(context).textTheme.labelLarge,
                      ),
                      child: Text(tr!.logout),
                      onPressed: () {
                        widget.firebaseMessagingConfig.deleteFCMToken().then((value) {
                          context.read<AuthenticationBloc>().add(AuthenticationLogoutRequested());
                        }).catchError(
                          (error) {
                            context.read<AuthenticationBloc>().add(AuthenticationLogoutRequested());
                          },
                        );
                      },
                    );
                  },
                ),
              ],
            );
          },
        );
      },
    ),
  ];

  void handleOnSelect(int value, NavigationItemData item) {
    setState(() {
      widget.changeSelectedNavigation(value);
    });
    if (item.onSelected != null) {
      item.onSelected!();
    }
  }

  ListTile listTile(NavigationItemData item, int index) {
    return ListTile(
      tileColor:
          widget.selectedNavigation == index ? Theme.of(context).colorScheme.primary.withValues(alpha: .15) : null,
      contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 1),
      titleTextStyle: TextStyle(
        fontSize: 14,
        color: Theme.of(context).colorScheme.onSurface,
      ),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(100))),
      title: Text(
        item.label,
        style: TextStyle(
          color: item.color,
        ),
        overflow: TextOverflow.ellipsis,
      ),
      leading: Badge(
        label: item.badge != null ? Text(item.badge ?? '') : null,
        textStyle: TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 10,
        ),
        isLabelVisible: item.badge != null,
        backgroundColor: Theme.of(context).colorScheme.primary,
        child: Icon(
          item.icon,
          color: item.color,
          fill: widget.selectedNavigation == index ? 1 : 0,
        ),
      ),
      onTap: () {
        handleOnSelect(index, item);
      },
      trailing: item.trailingBadge != null
          ? Container(
              padding: EdgeInsets.symmetric(vertical: 1, horizontal: 5),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                borderRadius: BorderRadius.circular(
                  10,
                ),
              ),
              child: Text(
                item.trailingBadge ?? '',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                ),
              ),
            )
          : null,
    );
  }

  ExpansionTile expansionTile(NavigationItemData item, int index, int factor) {
    return ExpansionTile(
      initiallyExpanded: ((widget.selectedNavigation - factor) ~/ factor) == index,
      backgroundColor:
          widget.selectedNavigation == index ? Theme.of(context).colorScheme.primary.withValues(alpha: .15) : null,
      collapsedShape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(
          Radius.circular(28),
        ),
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(
          Radius.circular(28),
        ),
      ),
      childrenPadding: Directionality.of(context) == TextDirection.ltr
          ? EdgeInsets.only(
              left: 16,
            )
          : EdgeInsets.only(
              right: 16,
            ),
      title: Text(
        item.label,
        style: TextStyle(
          color: item.color ?? Theme.of(context).colorScheme.onSurface,
          fontSize: 14,
        ),
        overflow: TextOverflow.ellipsis,
      ),
      leading: Badge(
        label: item.badge != null ? Text(item.badge ?? '') : null,
        textStyle: TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 10,
        ),
        isLabelVisible: item.badge != null,
        backgroundColor: Theme.of(context).colorScheme.primary,
        child: Icon(
          item.icon,
          color: item.color,
          fill: widget.selectedNavigation == index ? 1 : 0,
        ),
      ),
      children: item.children.asMap().entries.map(
        (entry) {
          int index2 = (entry.key + (factor * (index + 1)));

          NavigationItemData child = entry.value;
          if (child.children.isNotEmpty) {
            return expansionTile(child, index2, factor * 10);
          }
          return listTile(child, index2);
        },
      ).toList(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return NavigationDrawer(
      children: <Widget>[
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 8,
            ),
            Image.asset(
              'assets/new_launcher_icons/ic_logo_splash.png',
              width: 80,
              height: 80,
              // alignment: Directionality.of(context) == TextDirection.ltr ? Alignment.centerLeft : Alignment.centerRight,
            ),
            Text(
              tr!.peaceGlobalLogistics,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
            ),
          ],
        ),
        Divider(
          height: 1,
        ),
        Card(
          elevation: 0,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 12),
            child: Column(
              children: [
                ...navigationItems.asMap().entries.map(
                  (entry) {
                    int index = entry.key;
                    NavigationItemData item = entry.value;
                    if (item.children.isNotEmpty) {
                      return expansionTile(item, index, 1000);
                    } else {
                      return listTile(item, index);
                    }
                  },
                ),
              ],
            ),
          ),
        ),
        // ...navigationItems.map(
        //   (NavigationItemData item) => NavigationDrawerDestination(
        //     label: Expanded(
        //       child: Text(
        //         item.label,
        //         style: TextStyle(
        //           color: item.color,
        //         ),
        //         overflow: TextOverflow.ellipsis,
        //       ),
        //     ),
        //     icon: Badge(
        //       label: item.badge != null ? Text(item.badge ?? '') : null,
        //       isLabelVisible: item.badge != null,
        //       child: Icon(
        //         item.icon,
        //         color: item.color,
        //       ),
        //     ),
        //     selectedIcon: Badge(
        //       label: item.badge != null ? Text(item.badge ?? '') : null,
        //       isLabelVisible: item.badge != null,
        //       child: Icon(
        //         item.icon,
        //         fill: 1,
        //         color: item.color,
        //       ),
        //     ),
        //   ),
        // ),
      ],
    );
  }
}
