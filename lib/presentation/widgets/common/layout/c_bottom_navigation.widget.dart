import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/logic/blocs/authentication/authentication.exports.dart';

class CBottomNavigation extends StatefulWidget {
  const CBottomNavigation({
    super.key,
    required this.currentTab,
    required this.changeTab,
  });

  final int currentTab;
  final void Function(int value)? changeTab;

  @override
  State<CBottomNavigation> createState() => _CBottomNavigationState();
}

class _CBottomNavigationState extends State<CBottomNavigation> {
  int currentPageIndex = 0;
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthenticationBloc, AuthenticationState>(
      builder: (context, state) {
        return NavigationBar(
          onDestinationSelected: widget.changeTab,
          selectedIndex: widget.currentTab,
          destinations: <Widget>[
            NavigationDestination(
              selectedIcon: const Icon(
                Symbols.home,
                fill: 1,
              ),
              icon: const Icon(Symbols.home),
              label: tr!.home,
              tooltip: tr!.home,
            ),
            NavigationDestination(
              selectedIcon: const Icon(
                Symbols.directions_car,
                fill: 1,
              ),
              icon: const Icon(Symbols.directions_car),
              label: tr!.vehicles,
              tooltip: tr!.vehicles,
            ),
            NavigationDestination(
              selectedIcon: const Icon(
                Symbols.local_shipping,
                fill: 1,
              ),
              icon: const Icon(Symbols.local_shipping),
              label: tr!.shipments,
              tooltip: tr!.shipments,
            ),
            NavigationDestination(
              selectedIcon: const Icon(
                Symbols.article,
                fill: 1,
              ),
              icon: const Icon(Symbols.article),
              label: tr!.invoices,
              tooltip: tr!.invoices,
            ),
            if (state.user.mixShipping)
              NavigationDestination(
                selectedIcon: const Icon(
                  Symbols.local_shipping,
                  fill: 1,
                ),
                icon: const Icon(Symbols.local_shipping),
                label: tr!.mixShipping,
              ),
          ],
        );
      },
    );
  }
}
