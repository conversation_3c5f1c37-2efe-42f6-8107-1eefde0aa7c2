import 'package:flutter/material.dart';
import 'package:get/route_manager.dart';
import 'package:infinite_carousel/infinite_carousel.dart';
import 'package:pgl_mobile_app/constants/routes.const.dart';
import 'package:pgl_mobile_app/presentation/screens/misc/images_preview.screen.dart';

class CCarousel extends StatefulWidget {
  const CCarousel({
    super.key,
    required this.items,
  });

  final List<String> items;

  @override
  State<CCarousel> createState() => _CCarouselState();
}

class _CCarouselState extends State<CCarousel> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(28),
      ),
      clipBehavior: Clip.hardEdge,
      height: 200,
      child: InfiniteCarousel.builder(
        itemCount: widget.items.length,
        itemExtent: 200,
        center: false,
        anchor: 0.0,
        velocityFactor: 0.5,
        onIndexChanged: (index) {},
        axisDirection: Axis.horizontal,
        loop: false,
        itemBuilder: (context, itemIndex, realIndex) {
          return Container(
            margin: const EdgeInsets.only(right: 8),
            // height: 140,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(28),
              color: Colors.red,
            ),
            clipBehavior: Clip.hardEdge,
            child: Material(
              child: InkWell(
                onTap: () {
                  Get.toNamed(
                    imagePreview,
                    arguments: ImagesPreviewArguments(
                      urls: widget.items,
                      initialIndex: itemIndex,
                    ),
                  );
                },
                customBorder: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(28),
                ),
                child: Image.network(
                  widget.items[itemIndex],
                  height: 200,
                  width: 200,
                  fit: BoxFit.cover,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
