import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/configs/themes/colors.theme.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';

class CStrippedTable extends StatefulWidget {
  const CStrippedTable({
    super.key,
    required this.data,
    this.onItemTap,
  });

  final List<StrippedTableItem> data;
  final void Function(StrippedTableItem item)? onItemTap;

  @override
  State<CStrippedTable> createState() => _CStrippedTableState();
}

class _CStrippedTableState extends State<CStrippedTable> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: widget.data
          .asMap()
          .map(
            (int index, StrippedTableItem item) => MapEntry(
              index,
              ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: Material(
                  color: index % 2 == 0
                      ? Theme.of(context).colorScheme.surface
                      : Theme.of(context).brightness == Brightness.light
                          ? cardBgColorLight
                          : cardBgColorDark,
                  child: InkWell(
                    onTap: widget.onItemTap != null
                        ? () {
                            widget.onItemTap!(item);
                          }
                        : item.onTap != null
                            ? () {
                                item.onTap!();
                              }
                            : null,
                    child: Container(
                      // height: 30,
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            item.label,
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          Container(
                            padding: Directionality.of(context) == TextDirection.ltr
                                ? const EdgeInsets.only(left: 10)
                                : const EdgeInsets.only(right: 10),
                            // height: 30,
                            child: Row(
                              children: [
                                item.valueWidget ??
                                    Container(
                                      constraints: BoxConstraints(
                                        maxWidth: (MediaQuery.of(context).size.width - 32) * .6,
                                      ),
                                      child: item.selectable
                                          ? SelectableText(
                                              item.value,
                                              style: const TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.w500,
                                              ),
                                              textAlign: item.textAlign,
                                            )
                                          : Text(
                                              item.value,
                                              style: const TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.w500,
                                              ),
                                              textAlign: item.textAlign,
                                            ),
                                    ),
                                if (widget.onItemTap != null)
                                  const SizedBox(
                                    width: 2,
                                  ),
                                if (widget.onItemTap != null || item.onTap != null)
                                  Icon(
                                    Directionality.of(context) == TextDirection.ltr
                                        ? Symbols.chevron_right
                                        : Symbols.chevron_left,
                                    size: 16,
                                    color: Theme.of(context).colorScheme.primary,
                                  ),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          )
          .values
          .toList(),
    );
  }
}
