import 'package:flutter/material.dart';

class BottomSheetStyle2 extends StatefulWidget {
  const BottomSheetStyle2({
    super.key,
    required this.children,
  });

  final List<Widget> children;

  @override
  State<BottomSheetStyle2> createState() => _BottomSheetStyle2State();
}

class _BottomSheetStyle2State extends State<BottomSheetStyle2> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: SingleChildScrollView(
        child: Column(
          children: [const Row(), ...widget.children],
        ),
      ),
    );
  }
}
