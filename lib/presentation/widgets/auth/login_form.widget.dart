import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:formz/formz.dart';
import 'package:get/get.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/logic/blocs/login/login.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_textfield.widget.dart';

class LoginForm extends StatefulWidget {
  const LoginForm({super.key});

  @override
  State<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late FocusNode _emailFocusNode;
  late FocusNode _passwordFocusNode;

  @override
  void initState() {
    super.initState();
    _emailFocusNode = FocusNode();
    _passwordFocusNode = FocusNode();
  }

  @override
  void dispose() {
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 36, vertical: 24),
      child: BlocListener<LoginBloc, LoginState>(
        listener: (context, state) {
          if (state.status.isFailure) {
            ScaffoldMessenger.of(context)
              ..hideCurrentSnackBar()
              ..showSnackBar(
                SnackBar(content: Text(tr!.authenticationFailure)),
              );
          }
        },
        child: AutofillGroup(
          child: Column(
            children: [
              _usernameInput(),
              const SizedBox(
                height: 16,
              ),
              _passwordInput(),
              const SizedBox(
                height: 8,
              ),
              Align(
                alignment: Alignment.centerRight,
                child: GestureDetector(
                  child: Text(
                    tr!.forgotPasswordQ,
                    style: TextStyle(color: Theme.of(context).colorScheme.primary),
                  ),
                  onTap: () {
                    showDialog<void>(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: Text(tr!.forgotPassword),
                          content: Text(tr!.forgotPasswordPrompt),
                          actions: <Widget>[
                            TextButton(
                              style: TextButton.styleFrom(
                                textStyle: Theme.of(context).textTheme.labelLarge,
                              ),
                              child: Text(tr!.close),
                              onPressed: () {
                                Get.back();
                              },
                            ),
                          ],
                        );
                      },
                    );
                  },
                ),
              ),
              const SizedBox(
                height: 8,
              ),
              _loginButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _usernameInput() {
    String? getUsernameError(UsernameValidationError? error) {
      switch (error) {
        case UsernameValidationError.empty:
          return tr!.itemMustBeProvided(tr!.eitherEmailOrUsername);
        default:
          return null;
      }
    }

    return BlocBuilder<LoginBloc, LoginState>(
      buildWhen: (previous, current) => previous.username != current.username,
      builder: (context, state) {
        return CTextField(
          key: const Key('loginForm_usernameInput_textField'),
          focusNode: _emailFocusNode,
          initialValue: state.username.value,
          onChanged: (username) => context.read<LoginBloc>().add(LoginUsernameChanged(username)),
          label: tr!.emailOrUsername,
          error: getUsernameError(state.username.displayError),
          icon: Symbols.alternate_email,
          onFieldSubmitted: (text) {
            FocusScope.of(context).requestFocus(_passwordFocusNode);
          },
          autofillHints: const [
            AutofillHints.username,
            AutofillHints.email,
          ],
        );
      },
    );
  }

  Widget _passwordInput() {
    String? getPasswordError(PasswordValidationError? error) {
      switch (error) {
        case PasswordValidationError.empty:
          return tr!.itemMustBeProvided(tr!.password);
        case PasswordValidationError.min:
          return tr!.itemMustBeAtLeastCountChars(tr!.password, 8);
        default:
          return null;
      }
    }

    return BlocBuilder<LoginBloc, LoginState>(
      buildWhen: (previous, current) => previous.password != current.password,
      builder: (context, state) {
        return CTextField(
          key: const Key('loginForm_passwordInput_textField'),
          focusNode: _passwordFocusNode,
          initialValue: state.password.value,
          onChanged: (password) => context.read<LoginBloc>().add(LoginPasswordChanged(password)),
          label: tr!.password,
          error: getPasswordError(state.password.displayError),
          icon: Symbols.lock,
          obscureText: true,
          autofillHints: const [
            AutofillHints.password,
          ],
          onFieldSubmitted: (text) {
            if (state.isValid) {
              context.read<LoginBloc>().add(const LoginSubmitted());
            }
          },
        );
      },
    );
  }

  Widget _loginButton() {
    return BlocBuilder<LoginBloc, LoginState>(
      builder: (context, state) {
        return SizedBox(
          width: double.infinity,
          height: 48,
          child: FilledButton(
            onPressed: state.isValid
                ? () {
                    context.read<LoginBloc>().add(const LoginSubmitted());
                  }
                : null,
            child: state.status.isInProgress
                ? SizedBox(
                    height: 22,
                    width: 22,
                    child: CircularProgressIndicator(
                      color: Theme.of(context).colorScheme.onPrimary,
                      strokeWidth: 2,
                    ),
                  )
                : Text(
                    tr!.logIn,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
          ),
        );
      },
    );
  }
}
