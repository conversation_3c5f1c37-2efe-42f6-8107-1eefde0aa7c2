import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/widgets/filter/fields/filter_textfield.widget.dart';

class YearField extends StatefulWidget {
  const YearField({
    super.key,
    required this.onChanged,
    required this.setController,
    this.initialValue,
  });

  final Function(String text) onChanged;
  final Function(TextEditingController textEditingController) setController;
  final String? initialValue;
  @override
  State<YearField> createState() => _YearFieldState();
}

class _YearFieldState extends State<YearField> {
  List<String> yearsArray = [];
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  void initState() {
    int currentYear = DateTime.now().year;
    setState(() {
      yearsArray = List.generate(101, (index) => (currentYear - 100 + index).toString());
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Autocomplete<String>(
      fieldViewBuilder: (context, textEditingController, focusNode, onFieldSubmitted) {
        textEditingController.addListener(
          () {
            setState(() {
              widget.onChanged(textEditingController.text);
            });
          },
        );
        widget.setController(textEditingController);
        return FilterTextField(
          label: tr!.year,
          icon: Symbols.calendar_today,
          textEditingController: textEditingController,
          focusNode: focusNode,
          onFieldSubmitted: (String text) {
            onFieldSubmitted();
          },
        );
      },
      displayStringForOption: (String option) {
        return option;
      },
      optionsBuilder: (TextEditingValue textEditingValue) {
        if (textEditingValue.text == '') {
          return yearsArray.reversed;
        }
        return yearsArray.reversed.where((String option) {
          return option.contains(textEditingValue.text.toLowerCase());
        });
      },
      optionsViewBuilder: (context, onSelected, options) {
        List<String> options2 = options.toList();
        return Align(
          alignment: Alignment.topLeft,
          child: Material(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxHeight: 200),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 8,
                ),
                width: MediaQuery.of(context).size.width - 32,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: options2
                        .map(
                          (String item) => Material(
                            child: InkWell(
                              onTap: () {
                                onSelected(item);
                              },
                              child: Column(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 12,
                                      horizontal: 16,
                                    ),
                                    width: double.infinity,
                                    child: Text(item),
                                  ),
                                  Container(
                                    height: 1,
                                    margin: Directionality.of(context) == TextDirection.ltr
                                        ? const EdgeInsets.only(
                                            left: 16,
                                          )
                                        : const EdgeInsets.only(
                                            right: 16,
                                          ),
                                    color: Colors.grey.withValues(alpha: .2),
                                  )
                                ],
                              ),
                            ),
                          ),
                        )
                        .toList(),
                  ),
                ),
              ),
            ),
          ),
        );
      },
      initialValue: TextEditingValue(text: widget.initialValue ?? ''),
    );
  }
}
