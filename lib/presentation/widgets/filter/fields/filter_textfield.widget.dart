import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_textfield.widget.dart';

class FilterTextField extends StatefulWidget {
  const FilterTextField({
    super.key,
    required this.label,
    required this.icon,
    required this.textEditingController,
    this.focusNode,
    this.onFieldSubmitted,
    this.isNumber = false,
    this.loading = false,
    this.readOnly = false,
    this.enabled = true,
    this.onTap,
    this.validator,
  });

  final String label;
  final IconData icon;
  final TextEditingController textEditingController;
  final bool isNumber;
  final bool loading;
  final bool readOnly;
  final bool enabled;
  final FocusNode? focusNode;
  final Function(String)? onFieldSubmitted;
  final Function()? onTap;
  final String? Function(String? value)? validator;
  @override
  State<FilterTextField> createState() => _FilterTextFieldState();
}

class _FilterTextFieldState extends State<FilterTextField> {
  @override
  Widget build(BuildContext context) {
    return CTextField(
      enabled: widget.enabled,
      onFieldSubmitted: widget.onFieldSubmitted,
      focusNode: widget.focusNode,
      label: widget.label,
      icon: widget.icon,
      controller: widget.textEditingController,
      iconSize: 18,
      labelStyle: const TextStyle(
        fontSize: 12,
      ),
      style: const TextStyle(
        fontSize: 12,
      ),
      contentPadding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 4,
      ),
      prefixIconConstraints: const BoxConstraints(
        minWidth: 42,
      ),
      textInputFormatters: widget.isNumber
          ? [
              FilteringTextInputFormatter.digitsOnly,
            ]
          : null,
      textInputType: widget.isNumber ? TextInputType.number : null,
      loading: widget.loading,
      readOnly: widget.readOnly,
      onTap: widget.onTap,
      validator: widget.validator,
    );
  }
}
