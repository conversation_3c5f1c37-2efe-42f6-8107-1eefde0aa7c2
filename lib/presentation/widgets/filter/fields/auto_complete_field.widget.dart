import 'dart:async';

import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/configs/themes/colors.theme.dart';
import 'package:pgl_mobile_app/logic/classes/debounce.class.dart';
import 'package:pgl_mobile_app/presentation/widgets/filter/fields/filter_textfield.widget.dart';

class AutoCompleteField<T extends Object> extends StatefulWidget {
  const AutoCompleteField({
    super.key,
    required this.label,
    required this.icon,
    required this.onChanged,
    required this.setController,
    this.initialValue,
    required this.getItems,
    required this.displayStringForOption,
    this.debounceDuration = const Duration(milliseconds: 500),
    this.allowEmpty = false,
    this.enabled = true,
    this.validator,
    this.loading = false,
  });

  final Function(T value) onChanged;
  final String label;
  final IconData icon;
  final Function(TextEditingController textEditingController) setController;
  final String? initialValue;
  final Future<Iterable<T>> Function(String q) getItems;
  final AutocompleteOptionToString<T> displayStringForOption;
  final Duration debounceDuration;
  final bool allowEmpty;
  final bool enabled;
  final String? Function(String? value)? validator;
  final bool loading;

  @override
  State<AutoCompleteField<T>> createState() => _AutoCompleteFieldState<T>();
}

class _AutoCompleteFieldState<T extends Object> extends State<AutoCompleteField<T>> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late AutocompleteOptionToString<T> displayStringForOption;

  String? _currentQuery;
  bool _loading = false;
  late Iterable<T> _lastOptions = <T>[];
  late final Debounceable<Iterable<T>?, String> _debouncedSearch;

  Future<Iterable<T>?> _search(String query) async {
    _currentQuery = query;
    setState(() {
      _loading = true;
    });
    final Iterable<T> options = await widget.getItems(_currentQuery!);
    setState(() {
      _loading = false;
    });
    if (_currentQuery != query) {
      return null;
    }
    _currentQuery = null;

    return options;
  }

  @override
  void initState() {
    super.initState();
    _debouncedSearch = debounce<Iterable<T>?, String>(_search, widget.debounceDuration);
    if (widget.allowEmpty) {
      _fetchInitial();
    }
    displayStringForOption = widget.displayStringForOption;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  Future<void> _fetchInitial() async {
    Iterable<T> options = await _debouncedSearch('') ?? [];
    setState(() {
      _lastOptions = options;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Autocomplete<T>(
      fieldViewBuilder: (context, textEditingController, focusNode, onFieldSubmitted) {
        widget.setController(textEditingController);
        return FilterTextField(
          label: widget.label,
          icon: widget.icon,
          textEditingController: textEditingController,
          focusNode: focusNode,
          onFieldSubmitted: (String text) {
            onFieldSubmitted();
          },
          loading: widget.loading ? widget.loading : _loading,
          enabled: widget.enabled,
          validator: widget.validator,
        );
      },
      onSelected: (T option) {
        widget.onChanged(option);
      },
      displayStringForOption: (T option) {
        return displayStringForOption(option);
      },
      optionsBuilder: (TextEditingValue textEditingValue) async {
        if ((textEditingValue.text == '' ||
                _lastOptions
                    .where(
                      (element) => displayStringForOption(element) == textEditingValue.text,
                    )
                    .isNotEmpty) &&
            !widget.allowEmpty) {
          return _lastOptions;
        }
        final Iterable<T>? options = await _debouncedSearch(textEditingValue.text);
        if (options == null) {
          return _lastOptions;
        }
        _lastOptions = options;
        return options;
      },
      optionsViewBuilder: (context, onSelected, options) {
        List<T> options2 = options.toList();
        return Align(
          alignment: Directionality.of(context) == TextDirection.ltr ? Alignment.topLeft : Alignment.topRight,
          child: Material(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            clipBehavior: Clip.hardEdge,
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxHeight: 200),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 8,
                ),
                color: cardBgColorLight,
                width: MediaQuery.of(context).size.width - 32,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: options2
                        .map(
                          (T item) => Material(
                            color: cardBgColorLight,
                            child: InkWell(
                              onTap: () {
                                onSelected(item);
                              },
                              child: Column(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 12,
                                      horizontal: 16,
                                    ),
                                    width: double.infinity,
                                    child: Text(displayStringForOption(item)),
                                  ),
                                  Container(
                                    height: 1,
                                    margin: Directionality.of(context) == TextDirection.ltr
                                        ? const EdgeInsets.only(
                                            left: 16,
                                          )
                                        : const EdgeInsets.only(
                                            right: 16,
                                          ),
                                    color: Colors.grey.withValues(alpha: .2),
                                  )
                                ],
                              ),
                            ),
                          ),
                        )
                        .toList(),
                  ),
                ),
              ),
            ),
          ),
        );
      },
      initialValue: TextEditingValue(text: widget.initialValue ?? ''),
    );
  }
}
