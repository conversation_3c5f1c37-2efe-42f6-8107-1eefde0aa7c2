import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pgl_mobile_app/configs/themes/colors.theme.dart';
import 'package:pgl_mobile_app/logic/blocs/dashboard/bloc/dashboard_bloc.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/dashboard_item.card.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

class DashboardInvoicesCard extends StatefulWidget {
  const DashboardInvoicesCard({
    super.key,
    required this.changeTab,
  });
  final void Function(int index) changeTab;

  @override
  State<DashboardInvoicesCard> createState() => _DashboardInvoicesCardState();
}

class _DashboardInvoicesCardState extends State<DashboardInvoicesCard> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DashboardBloc, DashboardState>(
      builder: (context, state) {
        return DashBoardItemCard(
          loading: state.loading,
          iconAssetName: 'assets/svgs/invoice-icon.svg',
          color: dashBoardPurpleColor,
          data: DashBoardCardData(
            title: tr!.invoices,
            count: state.countInvoices,
            description: tr!.invoiceDescription,
          ),
          onTap: () {
            widget.changeTab(3);
          },
        );
      },
    );
  }
}
