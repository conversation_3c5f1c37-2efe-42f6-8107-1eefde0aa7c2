// import 'package:d_chart/commons/data_model/data_model.dart';
import 'package:d_chart/d_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pgl_mobile_app/logic/blocs/dashboard/bloc/dashboard_bloc.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_donut_chart.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

class DashboardInvoicesGraph extends StatefulWidget {
  const DashboardInvoicesGraph({super.key});

  @override
  State<DashboardInvoicesGraph> createState() => _DashboardInvoicesGraphState();
}

class _DashboardInvoicesGraphState extends State<DashboardInvoicesGraph> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DashboardBloc, DashboardState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: CDountChart(
            loading: state.loading,
            graphData: [
              OrdinalData(
                domain: tr!.allInvoices,
                measure: state.invoicePaymentStatus.all,
              ),
              OrdinalData(
                domain: tr!.paid,
                measure: state.invoicePaymentStatus.paid,
              ),
              OrdinalData(
                domain: tr!.due,
                measure: state.invoicePaymentStatus.due,
              ),
            ],
          ),
        );
      },
    );
  }
}
