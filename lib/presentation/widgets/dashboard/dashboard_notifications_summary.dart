import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/constants/constants.dart';
import 'package:pgl_mobile_app/exports/blocs.exports.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/dashboard/dashboard_small_card.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

class DashBoardNotificationSummary extends StatefulWidget {
  const DashBoardNotificationSummary({super.key});

  @override
  State<DashBoardNotificationSummary> createState() => _DashBoardNotificationSummaryState();
}

class _DashBoardNotificationSummaryState extends State<DashBoardNotificationSummary> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthenticationBloc, AuthenticationState>(
      builder: (context, authState) {
        return BlocBuilder<DashboardBloc, DashboardState>(
          builder: (context, state) {
            return SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: IntrinsicHeight(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    DashboardSmallCard(
                      title: tr!.announcements,
                      count: state.countAnnouncements,
                      icon: Symbols.campaign,
                      onTap: () {
                        Get.toNamed(annoncements);
                      },
                    ),
                    if (authState.user.showShippingRate && authState.user.hasShippingRate) ...[
                      const SizedBox(
                        width: 32,
                        height: 50,
                        child: VerticalDivider(
                          width: 1,
                        ),
                      ),
                      DashboardSmallCard(
                        title: tr!.shippingRates,
                        count: 0,
                        icon: Symbols.attach_money,
                        onTap: () {
                          Get.toNamed(shippingRates);
                        },
                      ),
                    ] else ...[
                      const SizedBox(
                        width: 32,
                        height: 50,
                        child: VerticalDivider(
                          width: 1,
                        ),
                      ),
                      DashboardSmallCard(
                        title: tr!.rateCalculator,
                        count: 0,
                        icon: Symbols.attach_money,
                        onTap: () {
                          Get.toNamed(
                            mixShippingCalculator,
                          );
                        },
                      ),
                    ],
                    const SizedBox(
                      width: 32,
                      height: 50,
                      child: VerticalDivider(
                        width: 1,
                      ),
                    ),
                    DashboardSmallCard(
                      title: tr!.arrivalNotice,
                      count: state.countArrivalNoticesNotifications,
                      icon: Symbols.alarm,
                      onTap: () {
                        Get.toNamed(notifications, arguments: NotificationType.arrivalNotice);
                      },
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
