import 'package:flutter/material.dart';

class DashboardSmallCard extends StatefulWidget {
  const DashboardSmallCard({
    super.key,
    required this.title,
    required this.icon,
    required this.count,
    this.onTap,
  });

  final String title;
  final IconData icon;
  final int count;
  final void Function()? onTap;

  @override
  State<DashboardSmallCard> createState() => _DashboardSmallCardState();
}

class _DashboardSmallCardState extends State<DashboardSmallCard> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onTap,
      enableFeedback: false,
      splashFactory: NoSplash.splashFactory,
      highlightColor: Colors.transparent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(
            height: 16,
          ),
          Badge(
            label: Text('${widget.count}'),
            isLabelVisible: widget.count > 0,
            child: Container(
              height: 40,
              width: 40,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: Theme.of(context).colorScheme.primary.withValues(alpha: .1),
              ),
              child: Center(
                child: Icon(
                  widget.icon,
                  size: 28,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                constraints: BoxConstraints(maxWidth: (MediaQuery.of(context).size.width - 120) / 3),
                child: Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    widget.title,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w700,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
              ),
              // Text(
              //   '${widget.count}',
              //   overflow: TextOverflow.ellipsis,
              //   style: TextStyle(
              //     fontSize: 14,
              //     fontWeight: FontWeight.w700,
              //     color: Theme.of(context).brightness == Brightness.light ? Colors.black54 : Colors.white,
              //   ),
              // ),
            ],
          ),
        ],
      ),
    );
  }
}
