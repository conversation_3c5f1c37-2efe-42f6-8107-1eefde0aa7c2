import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:pgl_mobile_app/configs/themes/colors.theme.dart';
import 'package:pgl_mobile_app/constants/constants.dart';
import 'package:pgl_mobile_app/logic/blocs/dashboard/bloc/dashboard_bloc.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/dashboard_item.card.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

class DashboardStatementsCard extends StatefulWidget {
  const DashboardStatementsCard({
    super.key,
  });

  @override
  State<DashboardStatementsCard> createState() => _DashboardStatementsCardState();
}

class _DashboardStatementsCardState extends State<DashboardStatementsCard> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DashboardBloc, DashboardState>(
      builder: (context, state) {
        return DashBoardItemCard(
          loading: state.loading,
          iconAssetName: 'assets/svgs/invoice-open-icon.svg',
          color: dashBoardBlueColor,
          data: DashBoardCardData(
            title: tr!.statements,
            count: state.countTransactions,
          ),
          onTap: () {
            Get.toNamed(statementsList);
          },
        );
      },
    );
  }
}
