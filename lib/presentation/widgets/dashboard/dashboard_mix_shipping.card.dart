import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pgl_mobile_app/configs/themes/colors.theme.dart';
import 'package:pgl_mobile_app/logic/blocs/dashboard/bloc/dashboard_bloc.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/dashboard_item.card.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

class DashboardMixShippingCard extends StatefulWidget {
  const DashboardMixShippingCard({
    super.key,
    required this.changeTab,
  });
  final void Function(int index) changeTab;

  @override
  State<DashboardMixShippingCard> createState() => _DashboardMixShippingCardState();
}

class _DashboardMixShippingCardState extends State<DashboardMixShippingCard> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DashboardBloc, DashboardState>(
      builder: (context, state) {
        return DashBoardItemCard(
          loading: state.loading,
          iconAssetName: 'assets/svgs/invoice-icon.svg',
          color: dashBoardYellowColor,
          data: DashBoardCardData(
            title: tr!.mixShipping,
            count: state.countMixShipping,
          ),
          onTap: () {
            widget.changeTab(4);
          },
        );
      },
    );
  }
}
