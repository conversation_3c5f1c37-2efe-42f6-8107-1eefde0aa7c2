import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/route_manager.dart';
import 'package:pgl_mobile_app/configs/themes/colors.theme.dart';
import 'package:pgl_mobile_app/constants/constants.dart';
import 'package:pgl_mobile_app/logic/blocs/dashboard/bloc/dashboard_bloc.dart';
import 'package:pgl_mobile_app/logic/blocs/shipment/bloc/shipment_bloc.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/logic/models/shipment/shipment_summary_item.modal.dart';
import 'package:pgl_mobile_app/presentation/screens/screens.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/dashboard_item.card.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:shipments_repository/shipments_repository.dart';

class DahsboardShipmentsCard extends StatefulWidget {
  const DahsboardShipmentsCard({
    super.key,
    required this.changeTab,
  });
  final void Function(int index) changeTab;

  @override
  State<DahsboardShipmentsCard> createState() => _DahsboardShipmentsCardState();
}

class _DahsboardShipmentsCardState extends State<DahsboardShipmentsCard> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  late List<SelectItem> shipmentsSummaryItems = getShipmentsSummaryItems(context);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DashboardBloc, DashboardState>(
      builder: (context, state) {
        return DashBoardItemCard(
          loading: state.loading,
          moreInfoLoading: state.shipmentSummaryLoading,
          iconAssetName: 'assets/svgs/truck-icon.svg',
          hasMoreDetails: true,
          defaultCategoryIndex: shipmentsSummaryItems.indexOf(
            SelectItem(value: state.shipmentSummaryCategory, label: state.shipmentSummaryCategory),
          ),
          categoryItems: shipmentsSummaryItems,
          selectMenuTitle: tr!.selectVehicleStatus,
          tableTopTitle: tr!.location,
          color: dashBoardRedColor,
          data: DashBoardCardData(
            title: tr!.shipments,
            count: state.countShipments,
          ),
          strippedTableData: [
            ...state.shipmentSummaryItems.map<StrippedTableItem>(
              (ShipmentSummaryItem item) {
                return StrippedTableItem<ShipmentSummaryItem>(
                  label: item.locationName,
                  value: "${item.toMap()[state.shipmentSummaryCategory]}",
                  metaData: item,
                );
              },
            ),
            StrippedTableItem(label: tr!.total, value: '${state.getSelectedShipmentCategoryTotal()}'),
          ],
          onStrippedTableItemTab: (StrippedTableItem item) {
            context.read<ShipmentBloc>().add(
                  ShipmentsFilterChanged(
                    shipmentsFilter: ShipmentsFilter(
                      state: state.shipmentSummaryCategory == 'total' ? '' : state.shipmentSummaryCategory,
                      filterData: item.metaData.locationId != null
                          ? ShipmentFilterData(
                              filterPointOfLoading: FilterPointOfLoadingShipment(
                                id: item.metaData.locationId,
                                name: item.metaData.locationName,
                              ),
                            )
                          : null,
                    ),
                  ),
                );
            Get.toNamed(
              shipmentsList,
              arguments: ShipmentsListArguments(
                title: tr!.shipments,
              ),
            );
          },
          onCategoryItemChange: (SelectItem? item) {
            context.read<DashboardBloc>().add(ShipmentSummaryCategoryChanged(
                  category: item != null ? item.value : 'total',
                ));
          },
          onTap: () {
            widget.changeTab(2);
          },
          onCollapseBtnOpen: () {
            context.read<DashboardBloc>().add(LoadShipmentSummary());
          },
        );
      },
    );
  }
}
