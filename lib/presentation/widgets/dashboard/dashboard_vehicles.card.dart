import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/route_manager.dart';
import 'package:pgl_mobile_app/constants/constants.dart';
import 'package:pgl_mobile_app/logic/blocs/dashboard/bloc/dashboard_bloc.dart';
import 'package:pgl_mobile_app/logic/blocs/vehicles/bloc/vehicles_bloc.dart';
import 'package:pgl_mobile_app/logic/classes/classes.exports.dart';
import 'package:pgl_mobile_app/presentation/screens/screens.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/dashboard_item.card.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:vehicles_repository/vehicles_repository.dart';

class DashboardVehiclesCard extends StatefulWidget {
  const DashboardVehiclesCard({
    super.key,
    required this.changeTab,
  });
  final void Function(int index) changeTab;

  @override
  State<DashboardVehiclesCard> createState() => _DashboardVehiclesCardState();
}

class _DashboardVehiclesCardState extends State<DashboardVehiclesCard> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  late List<SelectItem> vehicleSummaryItems = getVehiclesSummaryItems(context);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DashboardBloc, DashboardState>(
      builder: (context, state) {
        return DashBoardItemCard(
          loading: state.loading,
          moreInfoLoading: state.vehicleSummaryLoading,
          iconAssetName: 'assets/svgs/cars-icon.svg',
          hasMoreDetails: true,
          defaultCategoryIndex: vehicleSummaryItems.indexOf(
            SelectItem(value: state.vehicleSummaryCategory, label: state.vehicleSummaryCategory),
          ),
          categoryItems: vehicleSummaryItems,
          selectMenuTitle: tr!.selectVehicleStatus,
          tableTopTitle: tr!.location,
          color: Theme.of(context).colorScheme.primary,
          data: DashBoardCardData(
            title: tr!.vehicles,
            count: state.countVehicles,
            description: tr!.vehicleDescription,
          ),
          strippedTableData: [
            ...state.vehicleSummaryItems.map<StrippedTableItem>(
              (VehicleSummaryItem item) {
                return StrippedTableItem<VehicleSummaryItem>(
                  label: item.locationName,
                  value: "${item.toMap()[state.vehicleSummaryCategory]}",
                  metaData: item,
                );
              },
            ),
            StrippedTableItem(
              label: tr!.total,
              value: '${state.getSelectedVehicleCategoryTotal()}',
            ),
          ],
          onStrippedTableItemTab: (StrippedTableItem item) {
            context.read<VehiclesBloc>().add(
                  VehiclesFilterChanged(
                    vehiclesFilter: VehiclesFilter(
                      state: state.vehicleSummaryCategory == 'total' ? '' : state.vehicleSummaryCategory,
                      filterData: item.metaData.locationId != null
                          ? VehicleFilterData(
                              filterPointOfLoading: VehicleFilterPointOfLoading(
                                  id: item.metaData.locationId, name: item.metaData.locationName),
                            )
                          : null,
                    ),
                  ),
                );
            Get.toNamed(
              vehiclesList,
              arguments: VehiclesListArguments(
                title: tr!.vehicles,
              ),
            );
          },
          onCategoryItemChange: (SelectItem? item) {
            context.read<DashboardBloc>().add(VehicleSummaryCategoryChanged(
                  category: item != null ? item.value : 'total',
                ));
          },
          onTap: () {
            widget.changeTab(1);
          },
          onCollapseBtnOpen: () {
            context.read<DashboardBloc>().add(LoadVehicleSummary());
          },
        );
      },
    );
  }
}
