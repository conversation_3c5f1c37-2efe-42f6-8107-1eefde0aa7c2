import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/tracking/tracking_item_info.widget.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:timelines_plus/timelines_plus.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

class TrackingItemsListLoading extends StatefulWidget {
  const TrackingItemsListLoading({
    super.key,
    required this.trackingResult,
    this.isShipment = false,
  });

  final TrackingResult? trackingResult;
  final bool isShipment;

  @override
  State<TrackingItemsListLoading> createState() => _TrackingItemsListLoadingState();
}

class _TrackingItemsListLoadingState extends State<TrackingItemsListLoading> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _containerImage(),
              if (!widget.isShipment)
                const SizedBox(
                  width: 16,
                ),
              if (!widget.isShipment) _vehicleImage()
            ],
          ),
          const SizedBox(
            height: 8,
          ),
          FixedTimeline.tileBuilder(
            theme: TimelineThemeData(
              nodePosition: 0,
              color: const Color(0xff989898),
              indicatorTheme: const IndicatorThemeData(
                position: 0,
                size: 40.0,
              ),
              connectorTheme: const ConnectorThemeData(
                thickness: 2.5,
              ),
            ),
            builder: TimelineTileBuilder.connected(
              connectionDirection: ConnectionDirection.before,
              itemCount: widget.isShipment ? 4 : (widget.trackingResult?.trackingItems.length ?? 0),
              contentsBuilder: (_, index) {
                TrackingItem? item = widget.trackingResult?.trackingItems[index];
                if (item == null) return null;

                return Skeletonizer(child: TrackingItemInfo(trackingResult: widget.trackingResult, item: item));
              },
              indicatorBuilder: (_, index) {
                TrackingItem? item = widget.trackingResult?.trackingItems[index];
                if (item == null) return null;

                return Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(40),
                  ),
                  clipBehavior: Clip.hardEdge,
                  child: Skeletonizer(
                    child: Skeleton.replace(
                      height: 60,
                      width: 60,
                      replace: true,
                      child: Container(),
                    ),
                  ),
                );
              },
              connectorBuilder: (_, index, ___) {
                TrackingItem? item = widget.trackingResult?.trackingItems[index];
                if (item == null) return null;

                return item.isReached
                    ? SolidLineConnector(
                        color: item.isReached ? Theme.of(context).colorScheme.primary.withValues(alpha: .7) : null,
                        thickness: 2,
                      )
                    : DashedLineConnector(
                        color: item.isReached ? Theme.of(context).colorScheme.primary.withValues(alpha: .7) : null,
                        thickness: 2,
                      );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _vehicleImage() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
      ),
      clipBehavior: Clip.hardEdge,
      child: Skeletonizer(
        child: Skeleton.replace(
          height: (MediaQuery.of(context).size.width - 48) / 2,
          width: (MediaQuery.of(context).size.width - 48) / 2,
          replace: true,
          child: Container(),
        ),
      ),
    );
  }

  Container _containerImage() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
      ),
      clipBehavior: Clip.hardEdge,
      child: Skeletonizer(
        child: Skeleton.replace(
          height: (MediaQuery.of(context).size.width - 48) / 2,
          width: (MediaQuery.of(context).size.width - 48) / 2,
          replace: true,
          child: Container(),
        ),
      ),
    );
  }
}
