import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/logic/helpers/images.helper.dart';
import 'package:pgl_mobile_app/presentation/widgets/tracking/tracking_item_icon.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/tracking/tracking_item_info.widget.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:timelines_plus/timelines_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

class TrackingItemsList extends StatefulWidget {
  const TrackingItemsList({super.key, required this.trackingResult});

  final TrackingResult? trackingResult;

  @override
  State<TrackingItemsList> createState() => _TrackingItemsListState();
}

class _TrackingItemsListState extends State<TrackingItemsList> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _containerImage(),
              if (widget.trackingResult?.trackingItemType == TrackingItemType.vehicle)
                const SizedBox(
                  width: 16,
                ),
              if (widget.trackingResult?.trackingItemType == TrackingItemType.vehicle) _vehicleImage()
            ],
          ),
          const SizedBox(
            height: 8,
          ),
          FixedTimeline.tileBuilder(
            theme: TimelineThemeData(
              nodePosition: 0,
              color: const Color(0xff989898),
              indicatorTheme: const IndicatorThemeData(
                position: 0,
                size: 40.0,
              ),
              connectorTheme: const ConnectorThemeData(
                thickness: 2.5,
              ),
            ),
            builder: TimelineTileBuilder.connected(
              connectionDirection: ConnectionDirection.before,
              itemCount: widget.trackingResult?.trackingItems.length ?? 0,
              contentsBuilder: (_, index) {
                TrackingItem? item = widget.trackingResult?.trackingItems[index];
                if (item == null) return null;

                return TrackingItemInfo(trackingResult: widget.trackingResult, item: item);
              },
              indicatorBuilder: (_, index) {
                TrackingItem? item = widget.trackingResult?.trackingItems[index];
                if (item == null) return null;

                return TrackingItemIcon(item: item);
              },
              connectorBuilder: (_, index, ___) {
                TrackingItem? item = widget.trackingResult?.trackingItems[index];
                if (item == null) return null;

                return item.isReached
                    ? SolidLineConnector(
                        color: item.isReached ? Theme.of(context).colorScheme.primary.withValues(alpha: .7) : null,
                        thickness: 2,
                      )
                    : DashedLineConnector(
                        color: item.isReached ? Theme.of(context).colorScheme.primary.withValues(alpha: .7) : null,
                        thickness: 2,
                      );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _vehicleImage() {
    return Material(
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: widget.trackingResult?.trackingItemVehicle?.photoLink != null
            ? () {
                launchUrl(Uri.parse(widget.trackingResult?.trackingItemVehicle?.photoLink ?? ''));
              }
            : null,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          height: (MediaQuery.of(context).size.width - 48) / 2,
          width: (MediaQuery.of(context).size.width - 48) / 2,
          clipBehavior: Clip.hardEdge,
          decoration: BoxDecoration(
            color: widget.trackingResult?.trackingItemVehicle?.photoLink != null ||
                    widget.trackingResult?.trackingItemVehicle?.coverPhoto != null
                ? Theme.of(context).colorScheme.primary.withValues(alpha: .1)
                : Colors.red.shade600.withValues(alpha: .1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: widget.trackingResult?.trackingItemVehicle?.coverPhoto != null
              ? CachedNetworkImage(
                  imageUrl: getImageSizeUrl(
                    url: widget.trackingResult?.trackingItemVehicle?.coverPhoto,
                    size: 250,
                  ),
                  fit: BoxFit.cover,
                  fadeInCurve: Curves.easeIn,
                  fadeOutCurve: Curves.easeIn,
                  fadeInDuration: const Duration(milliseconds: 200),
                  fadeOutDuration: const Duration(milliseconds: 200),
                  placeholder: (context, url) => Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    clipBehavior: Clip.hardEdge,
                    child: Skeletonizer(
                      child: Skeleton.replace(
                        height: (MediaQuery.of(context).size.width - 48) / 2,
                        width: (MediaQuery.of(context).size.width - 48) / 2,
                        replace: true,
                        child: Container(),
                      ),
                    ),
                  ),
                  errorWidget: (context, url, error) {
                    return const Icon(Icons.error);
                  },
                )
              : Center(
                  child: SvgPicture.asset(
                    widget.trackingResult?.trackingItemVehicle?.photoLink != null
                        ? 'assets/svgs/car-image-icon.svg'
                        : 'assets/svgs/no-car-image-icon.svg',
                    height: 100,
                    width: 100,
                  ),
                ),
        ),
      ),
    );
  }

  Material _containerImage() {
    return Material(
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: widget.trackingResult?.trackingItemContainer?.photoLink != null
            ? () {
                launchUrl(Uri.parse(widget.trackingResult?.trackingItemContainer?.photoLink ?? ''));
              }
            : null,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          height: (MediaQuery.of(context).size.width - 48) / 2,
          width: (MediaQuery.of(context).size.width - 48) / 2,
          clipBehavior: Clip.hardEdge,
          decoration: BoxDecoration(
            color: widget.trackingResult?.trackingItemContainer?.photoLink != null ||
                    widget.trackingResult?.trackingItemContainer?.coverPhoto != null
                ? Theme.of(context).colorScheme.primary.withValues(alpha: .1)
                : Colors.red.shade600.withValues(alpha: .1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: widget.trackingResult?.trackingItemContainer?.coverPhoto != null
              ? CachedNetworkImage(
                  imageUrl: getImageSizeUrl(
                    url: widget.trackingResult?.trackingItemContainer?.coverPhoto,
                    size: 250,
                  ),
                  fit: BoxFit.cover,
                  fadeInCurve: Curves.easeIn,
                  fadeOutCurve: Curves.easeIn,
                  fadeInDuration: const Duration(milliseconds: 200),
                  fadeOutDuration: const Duration(milliseconds: 200),
                  placeholder: (context, url) => Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    clipBehavior: Clip.hardEdge,
                    child: Skeletonizer(
                      child: Skeleton.replace(
                        height: (MediaQuery.of(context).size.width - 48) / 2,
                        width: (MediaQuery.of(context).size.width - 48) / 2,
                        replace: true,
                        child: Container(),
                      ),
                    ),
                  ),
                  errorWidget: (context, url, error) {
                    return const Icon(Icons.error);
                  },
                )
              : Center(
                  child: SvgPicture.asset(
                    widget.trackingResult?.trackingItemContainer?.photoLink != null
                        ? 'assets/svgs/shipment-image-icon.svg'
                        : 'assets/svgs/no-shipment-image-icon.svg',
                    height: 100,
                    width: 100,
                  ),
                ),
        ),
      ),
    );
  }
}
