import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/constants/date_formats.const.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_icon_text.widget.dart';

class TrackingItemInfo extends StatefulWidget {
  const TrackingItemInfo({super.key, required this.trackingResult, required this.item});
  final TrackingResult? trackingResult;
  final TrackingItem item;
  @override
  State<TrackingItemInfo> createState() => _TrackingItemInfoState();
}

class _TrackingItemInfoState extends State<TrackingItemInfo> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  String _getLabel(TrackingItem item) {
    switch (widget.item.trackingItemStatus) {
      case TrackingItemStatus.onTheWay:
        return widget.item.trackingItemType == TrackingItemType.vehicle
            ? tr!.onTheWayToWarehouse
            : tr!.onTheWayToDestination;
      case TrackingItemStatus.onHandWithTitle:
        return tr!.onHandWithTitle;
      case TrackingItemStatus.onHandWithLoad:
        return tr!.onHandWithLoad;
      case TrackingItemStatus.onHandNoTitle:
        return tr!.onHandNoTitle;
      case TrackingItemStatus.shipped:
        return tr!.shipped;
      case TrackingItemStatus.auctionPaid:
        return tr!.auctionPaid;
      case TrackingItemStatus.auctionUnpaid:
        return tr!.auctionUnpaid;
      case TrackingItemStatus.atLoading:
        return tr!.atLoading;
      case TrackingItemStatus.atTheDock:
        return tr!.atTheDock;
      case TrackingItemStatus.arrived:
        return widget.item.isReached ? tr!.arrived : tr!.arrival;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            width: 1,
            color: Colors.grey.shade300,
          ),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.only(left: 16, top: 16, bottom: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              widget.item.trackingItemType == TrackingItemType.vehicle ? tr!.vehicle : tr!.container,
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              _getLabel(widget.item),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w700,
              ),
            ),
            if (widget.item.createdAt != null)
              CIconText(
                icon: Symbols.calendar_add_on,
                text: DateFormat(cardsDateFormat).format(
                  widget.item.createdAt ?? DateTime.now(),
                ),
              ),
            if (widget.item.trackingItemStatus == TrackingItemStatus.atLoading)
              Row(
                children: [
                  Text(
                    '${tr!.from}: ',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  Text(
                    (widget.trackingResult?.trackingItemVehicle != null
                            ? widget.trackingResult?.trackingItemVehicle?.polLocation
                            : widget.trackingResult?.trackingItemContainer != null
                                ? widget.trackingResult?.trackingItemContainer?.vehicles.first.polLocation
                                : '') ??
                        '',
                    style: const TextStyle(
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            if (widget.item.trackingItemStatus == TrackingItemStatus.atLoading)
              Row(
                children: [
                  Text(
                    '${tr!.to}: ',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  Text(
                    widget.trackingResult?.trackingItemContainer?.booking.destination ?? '',
                    style: const TextStyle(
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            if (widget.item.trackingItemStatus == TrackingItemStatus.atTheDock)
              Row(
                children: [
                  Text(
                    '${tr!.containerNo}: ',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  Text(
                    widget.trackingResult?.trackingItemContainer?.containerNumber ?? '',
                    style: const TextStyle(
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            if (widget.item.trackingItemStatus == TrackingItemStatus.atTheDock &&
                widget.trackingResult?.trackingItemContainer?.booking.etd != null)
              Row(
                children: [
                  Text(
                    '${tr!.etd}: ',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  Text(
                    DateFormat(cardsDateFormat).format(
                      widget.trackingResult?.trackingItemContainer?.booking.etd ?? DateTime.now(),
                    ),
                    style: const TextStyle(
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            if (widget.item.trackingItemStatus == TrackingItemStatus.arrived &&
                widget.trackingResult?.trackingItemContainer?.booking.eta != null)
              Row(
                children: [
                  Text(
                    '${tr!.eta}: ',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  Text(
                    DateFormat(cardsDateFormat).format(
                      widget.trackingResult?.trackingItemContainer?.booking.eta ?? DateTime.now(),
                    ),
                    style: const TextStyle(
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}
