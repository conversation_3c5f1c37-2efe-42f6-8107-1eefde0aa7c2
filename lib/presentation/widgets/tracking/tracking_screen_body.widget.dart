import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:material_symbols_icons/material_symbols_icons.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/exports/blocs.exports.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/no_items_found.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/tracking/tracking_items_list.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/tracking/tracking_items_list_loading.widget.dart';
import 'package:tracking_repository/tracking_repository.dart';

class TrackingScreenBody extends StatefulWidget {
  const TrackingScreenBody({
    super.key,
    required this.query,
    this.hasPadding = true,
    this.isShipment = false,
  });

  final String query;
  final bool hasPadding;
  final bool isShipment;
  @override
  State<TrackingScreenBody> createState() => _TrackingScreenBodyState();
}

class _TrackingScreenBodyState extends State<TrackingScreenBody> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late TrackingRepository trackingRepository = RepositoryProvider.of<TrackingRepository>(context);
  TrackingResult? _trackingResult;
  bool _loading = false;
  bool _initial = true;

  Future<void> _fetchTrackingData(String trackingValue) async {
    if (trackingValue.isNotEmpty) {
      setState(() {
        _loading = true;
        _initial = false;
      });
      TrackingResult result = await trackingRepository.getTrackingInfo(trackingValue: trackingValue);
      setState(() {
        _trackingResult = result;
        _loading = false;
      });
    } else {
      setState(() {
        _loading = false;
        _initial = true;
      });
    }
  }

  @override
  void didUpdateWidget(covariant TrackingScreenBody oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.query != widget.query) {
      _fetchTrackingData(widget.query);
    }
  }

  @override
  void initState() {
    super.initState();
    if (widget.query != '') {
      _fetchTrackingData(widget.query);
    }
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        _fetchTrackingData(widget.query);
      },
      child: Padding(
        padding: EdgeInsets.all(widget.hasPadding ? 16 : 0),
        child: _initial
            ? Center(
                child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 48),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Symbols.query_stats,
                      size: 80,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    Center(
                      child: SizedBox(
                        width: MediaQuery.of(context).size.width * .7,
                        child: Text(
                          tr!.enterYourVinNoLotNoOrContainerNoToTrackYourShipments,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    )
                  ],
                ),
              ))
            : _loading
                ? TrackingItemsListLoading(
                    trackingResult: TrackingResult(
                      trackingItemType: TrackingItemType.vehicle,
                      trackingItems: trackingRepository
                          .getDefaultOptions(null, null)
                          .map(
                            (item) => item.copyWith(
                              isReached: true,
                              createdAt: DateTime.now(),
                            ),
                          )
                          .toList(),
                    ),
                    isShipment: widget.isShipment,
                  )
                : _trackingResult?.trackingItemType == TrackingItemType.none
                    ? Center(
                        child: NoItemsFound(
                        svgIcon: 'assets/svgs/cars-icon.svg',
                        onRefresh: () {
                          _fetchTrackingData(widget.query);
                        },
                        itemName: tr!.items,
                      ))
                    : _trackingResult != null
                        ? TrackingItemsList(trackingResult: _trackingResult)
                        : null,
      ),
    );
  }
}
