import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';

class TrackingItemIcon extends StatefulWidget {
  const TrackingItemIcon({super.key, required this.item});

  final TrackingItem item;
  @override
  State<TrackingItemIcon> createState() => _TrackingItemIconState();
}

class _TrackingItemIconState extends State<TrackingItemIcon> {
  String _getIconFromStatus(TrackingItemStatus status, TrackingItemType type) {
    switch (status) {
      case TrackingItemStatus.onTheWay:
        return type == TrackingItemType.vehicle
            ? 'assets/svgs/tracking/on_the_way_to_warehouse.svg'
            : 'assets/svgs/tracking/on_the_way_to_destination.svg';
      case TrackingItemStatus.onHandWithTitle:
        return 'assets/svgs/tracking/on_hand_with_title.svg';
      case TrackingItemStatus.onHandNoTitle:
        return 'assets/svgs/tracking/on_hand_no_title.svg';
      case TrackingItemStatus.onHandWithLoad:
        return 'assets/svgs/tracking/on_hand_with_load.svg';
      case TrackingItemStatus.shipped:
        return 'assets/svgs/tracking/shipped.svg';
      case TrackingItemStatus.auctionPaid:
        return 'assets/svgs/tracking/auction_paid.svg';
      case TrackingItemStatus.auctionUnpaid:
        return 'assets/svgs/tracking/auction_unpaid.svg';
      case TrackingItemStatus.atLoading:
        return 'assets/svgs/tracking/at_loading.svg';
      case TrackingItemStatus.atTheDock:
        return 'assets/svgs/tracking/at_the_dock.svg';
      case TrackingItemStatus.arrived:
        return 'assets/svgs/tracking/on_the_way_to_destination.svg';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      width: 60,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(40),
        border: Border.all(
          color: widget.item.isReached
              ? Theme.of(context).colorScheme.primary.withValues(alpha: .7)
              : Theme.of(context).colorScheme.onSurface.withValues(alpha: .2),
          width: 1,
        ),
        color: widget.item.isReached
            ? Theme.of(context).colorScheme.primary.withValues(alpha: .1)
            : Theme.of(context).colorScheme.onSurface.withValues(alpha: .05),
      ),
      child: Center(
        child: SvgPicture.asset(
          _getIconFromStatus(widget.item.trackingItemStatus, widget.item.trackingItemType),
          width: 32,
          theme: SvgTheme(
            currentColor: widget.item.isReached
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.onSurface.withValues(alpha: .3),
          ),
        ),
      ),
    );
  }
}
