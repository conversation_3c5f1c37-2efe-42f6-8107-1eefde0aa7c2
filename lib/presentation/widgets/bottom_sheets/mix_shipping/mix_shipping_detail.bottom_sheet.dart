import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:mix_shipping_repository/mix_shipping_repository.dart';
import 'package:open_file/open_file.dart';
import 'package:payments_repository/payments_repository.dart';
import 'package:pgl_mobile_app/constants/date_formats.const.dart';
import 'package:pgl_mobile_app/logic/classes/stripped_table_item.class.dart';
import 'package:pgl_mobile_app/presentation/widgets/badges/mix_shiping_state_badge.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/bottom_sheets/mix_shipping/mix_shipping_payments.bottom_sheet.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/bottom_sheet_style2.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_stripped_table.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

class MixShippingDetailBottomSheet extends StatefulWidget {
  const MixShippingDetailBottomSheet({
    super.key,
    required this.mixShipping,
  });

  final MixShipping mixShipping;

  @override
  State<MixShippingDetailBottomSheet> createState() => _MixShippingDetailBottomSheetState();
}

class _MixShippingDetailBottomSheetState extends State<MixShippingDetailBottomSheet> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  bool invoiceDownloading = false;

  Future<void> _downloadInvoice() async {
    try {
      setState(() {
        invoiceDownloading = true;
      });
      File invoice = await RepositoryProvider.of<MixshippingRepository>(context).getInvoicePDF(widget.mixShipping.id);
      OpenFile.open(invoice.path);
      setState(() {
        invoiceDownloading = false;
      });
    } catch (e) {
      setState(() {
        invoiceDownloading = false;
      });
    }
  }

  List<PaymentReceived<MixShippingVehicle>> mergePayments(List<MixShippingVehicle> mixShippingVehicles) {
    List<PaymentReceived<MixShippingVehicle>> allPayments = [];
    for (var vehicle in mixShippingVehicles) {
      allPayments.addAll(vehicle.payments);
    }
    return allPayments;
  }

  late List<PaymentReceived<MixShippingVehicle>> mergedPayments = mergePayments(widget.mixShipping.mixShippingVehicles);

  @override
  Widget build(BuildContext context) {
    return BottomSheetStyle2(
      children: [
        Row(
          children: [
            Expanded(
              child: SelectableText(
                widget.mixShipping.containerNumber,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
            const SizedBox(
              width: 8,
            ),
            Row(
              children: [
                SizedBox(
                  height: 32,
                  child: FilledButton(
                    style: const ButtonStyle(
                      padding: WidgetStatePropertyAll(
                        EdgeInsets.symmetric(horizontal: 12),
                      ),
                    ),
                    onPressed: _downloadInvoice,
                    child: Row(
                      children: [
                        invoiceDownloading
                            ? SizedBox(
                                height: 18,
                                width: 18,
                                child: Align(
                                  child: SizedBox(
                                    height: 16,
                                    width: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: Theme.of(context).colorScheme.onPrimary,
                                    ),
                                  ),
                                ),
                              )
                            : const Icon(
                                Symbols.download,
                                size: 18,
                              ),
                        const SizedBox(
                          width: 6,
                        ),
                        Text(
                          tr!.download,
                          style: const TextStyle(
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            )
          ],
        ),
        const SizedBox(
          height: 8,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _invoiceNoSection(),
          ],
        ),
        const SizedBox(
          height: 8,
        ),
        _datesSection(),
        const SizedBox(
          height: 8,
        ),
        CStrippedTable(
          data: [
            if (mergedPayments.isNotEmpty)
              StrippedTableItem(
                label: tr!.invoiceApplied,
                value: tr!.view,
                onTap: () async => {
                  await showModalBottomSheet(
                    isScrollControlled: true,
                    useSafeArea: false,
                    context: context,
                    showDragHandle: true,
                    constraints: BoxConstraints(
                      maxHeight: MediaQuery.of(context).size.height * .9,
                    ),
                    builder: (context) => MixShippingPaymentsBottomSheet(
                      payments: mergedPayments,
                    ),
                  )
                },
              ),
            StrippedTableItem(
              label: tr!.status,
              value: tr!.status,
              valueWidget: MixShippingStateBadge(state: widget.mixShipping.mixShippingState),
            ),
            StrippedTableItem(
              label: tr!.amount,
              value: '\$${widget.mixShipping.getTotalCostString()}',
            ),
            StrippedTableItem(
              label: '${tr!.amount} (AED)',
              value: '${widget.mixShipping.getTotalCostStringAED()} DH',
            ),
            StrippedTableItem(
              label: '${tr!.received} (AED)',
              value: '${widget.mixShipping.getTotalPaidStringCurrency()} DH',
            ),
            StrippedTableItem(
              label: '${tr!.amountDue} (AED)',
              value: '${widget.mixShipping.getTotalDueStringAED()} DH',
            ),
            StrippedTableItem(
              label: tr!.issueDate,
              value: DateFormat(cardsDateFormat).format(
                widget.mixShipping.invDate,
              ),
            ),
            if (widget.mixShipping.invDueDate != null)
              StrippedTableItem(
                label: tr!.dueDate,
                value: DateFormat(cardsDateFormat).format(
                  widget.mixShipping.invDueDate!,
                ),
              ),
            if ([
                  MixShippingStatus.open,
                  MixShippingStatus.pastDue,
                ].contains(widget.mixShipping.mixShippingState) &&
                widget.mixShipping.invDueDate != null)
              StrippedTableItem(
                label: tr!.pastDueDays,
                value: tr!.countDays(
                  DateTime.now().difference(widget.mixShipping.invDueDate!).inDays,
                ),
              ),
            StrippedTableItem(
              label: tr!.vins,
              value: '',
              valueWidget: Column(
                children: widget.mixShipping.mixShippingVehicles
                    .map(
                      (item) => SelectableText(
                        item.vin,
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    )
                    .toList(),
              ),
            ),
            StrippedTableItem(
              label: tr!.lotNumbers,
              value: '',
              valueWidget: Column(
                children: widget.mixShipping.mixShippingVehicles
                    .map(
                      (item) => SelectableText(
                        item.lotNumber,
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    )
                    .toList(),
              ),
            ),
            if (widget.mixShipping.mixShippingState == MixShippingStatus.paid)
              StrippedTableItem(
                label: tr!.receivedDates,
                value: '',
                valueWidget: Column(
                  children: widget.mixShipping.mixShippingVehicles
                      .map(
                        (item) => Text(
                          DateFormat(cardsDateFormat).format(
                            item.paymentDate ?? DateTime.now(),
                          ),
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      )
                      .toList(),
                ),
              ),
            StrippedTableItem(
              label: tr!.description,
              value: widget.mixShipping.description ?? '',
            ),
          ],
        ),
        Container(
          height: 32,
        )
      ],
    );
  }

  Widget _invoiceNoSection() {
    return Row(
      children: [
        Icon(
          Symbols.tag,
          color: Theme.of(context).colorScheme.primary,
          size: 18,
          weight: 700,
        ),
        const SizedBox(
          width: 2,
        ),
        SelectableText(
          widget.mixShipping.invoiceNumber,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w700,
            color: Theme.of(context).brightness == Brightness.light ? Colors.black54 : Colors.white70,
          ),
        ),
      ],
    );
  }

  Widget _datesSection() {
    return Row(
      children: [
        Expanded(
          child: Row(
            children: [
              Icon(
                Symbols.edit_calendar,
                color: Theme.of(context).colorScheme.primary,
                size: 18,
                weight: 700,
              ),
              const SizedBox(
                width: 2,
              ),
              Text(
                DateFormat(cardsDateFormat).format(
                  widget.mixShipping.invDate,
                ),
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w700,
                  color: Theme.of(context).brightness == Brightness.light ? Colors.black54 : Colors.white70,
                ),
              ),
            ],
          ),
        ),
        if (widget.mixShipping.invDueDate != null)
          Expanded(
            child: Row(
              children: [
                Icon(
                  Symbols.calendar_clock,
                  color: Theme.of(context).colorScheme.primary,
                  size: 18,
                  weight: 700,
                ),
                const SizedBox(
                  width: 2,
                ),
                Text(
                  widget.mixShipping.invDueDate != null
                      ? DateFormat(cardsDateFormat).format(
                          widget.mixShipping.invDueDate!,
                        )
                      : '',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                    color: Theme.of(context).brightness == Brightness.light ? Colors.black54 : Colors.white70,
                  ),
                ),
              ],
            ),
          )
      ],
    );
  }
}
