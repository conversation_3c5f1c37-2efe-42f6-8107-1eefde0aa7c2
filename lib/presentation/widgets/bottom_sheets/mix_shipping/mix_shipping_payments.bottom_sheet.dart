import 'package:flutter/material.dart';
import 'package:mix_shipping_repository/mix_shipping_repository.dart';
import 'package:payments_repository/payments_repository.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/mix_shipping/mix_shipping_payment_item.card.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/bottom_sheet_style2.widget.dart';

class MixShippingPaymentsBottomSheet extends StatefulWidget {
  const MixShippingPaymentsBottomSheet({
    super.key,
    required this.payments,
  });

  final List<PaymentReceived<MixShippingVehicle>> payments;

  @override
  State<MixShippingPaymentsBottomSheet> createState() => _MixShippingPaymentsBottomSheetState();
}

class _MixShippingPaymentsBottomSheetState extends State<MixShippingPaymentsBottomSheet> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return BottomSheetStyle2(
      children: [
        Row(
          children: [
            SizedBox(
              width: 12,
            ),
            Text(
              tr!.invoiceApplied,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
              textAlign: TextAlign.start,
            ),
          ],
        ),
        Divider(),
        SizedBox(
          height: 8,
        ),
        ...widget.payments.map(
          (PaymentReceived<MixShippingVehicle> payment) => MixShippingPaymentItemCard(
            payment: payment,
          ),
        ),
        Container(
          height: 32,
        )
      ],
    );
  }
}
