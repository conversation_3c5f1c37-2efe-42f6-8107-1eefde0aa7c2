import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:open_file/open_file.dart';
import 'package:pgl_mobile_app/constants/date_formats.const.dart';
import 'package:pgl_mobile_app/logic/classes/stripped_table_item.class.dart';
import 'package:pgl_mobile_app/presentation/widgets/badges/shipment_state_badge.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/bottom_sheets/tracking.bottom_sheet.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/bottom_sheet_style2.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_stripped_table.widget.dart';
import 'package:shipments_repository/shipments_repository.dart';
import 'package:url_launcher/url_launcher.dart';

class ShipmentDetailBottomSheet extends StatefulWidget {
  const ShipmentDetailBottomSheet({
    super.key,
    required this.shipment,
  });

  final Shipment shipment;

  @override
  State<ShipmentDetailBottomSheet> createState() => _ShipmentDetailBottomSheetState();
}

class _ShipmentDetailBottomSheetState extends State<ShipmentDetailBottomSheet> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  bool bolDownloading = false;

  Future<void> _downloadBOL() async {
    try {
      setState(() {
        bolDownloading = true;
      });
      File invoice = await RepositoryProvider.of<ShipmentsRepository>(context).getbillOfLoadingPDF(widget.shipment.id);
      OpenFile.open(invoice.path);
      setState(() {
        bolDownloading = false;
      });
    } catch (e) {
      setState(() {
        bolDownloading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BottomSheetStyle2(
      children: [
        // const CCarousel(
        //   items: [
        //     'https://picsum.photos/1000?image=4',
        //     'https://picsum.photos/1000?image=5',
        //     'https://picsum.photos/1000?image=6',
        //     'https://picsum.photos/1000?image=7',
        //     'https://picsum.photos/1000?image=8',
        //     'https://picsum.photos/1000?image=9',
        //     'https://picsum.photos/1000?image=10',
        //     'https://picsum.photos/1000?image=11',
        //     'https://picsum.photos/1000?image=12',
        //   ],
        // ),
        // const SizedBox(
        //   height: 20,
        // ),
        Row(
          children: [
            Expanded(
              child: SelectableText(
                widget.shipment.containerNumber,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
            const SizedBox(
              width: 8,
            ),
            Row(
              children: [
                // SizedBox(
                //   height: 32,
                //   width: 32,
                //   child: OutlinedButton(
                //     style: OutlinedButton.styleFrom(
                //       side: BorderSide(width: 1.0, color: Theme.of(context).colorScheme.primary),
                //       padding: EdgeInsets.zero,
                //     ),
                //     onPressed: () {},
                //     child: const Icon(
                //       Symbols.chat_bubble,
                //       size: 16,
                //     ),
                //   ),
                // ),
                const SizedBox(
                  width: 4,
                ),
                // if (widget.shipment.trackingContatiner != null && widget.shipment.trackingContatiner != '')
                SizedBox(
                  height: 32,
                  child: FilledButton(
                    style: const ButtonStyle(
                      padding: WidgetStatePropertyAll(
                        EdgeInsets.symmetric(horizontal: 12),
                      ),
                    ),
                    onPressed: () async {
                      await showModalBottomSheet(
                        isScrollControlled: true,
                        useSafeArea: true,
                        context: context,
                        showDragHandle: true,
                        constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * .8),
                        builder: (context) => TrackingBottomSheet(
                          query: widget.shipment.containerNumber,
                          isShipment: true,
                        ),
                      );
                    },
                    child: Text(
                      tr!.track,
                      style: const TextStyle(
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
              ],
            )
          ],
        ),
        const SizedBox(
          height: 8,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _bookingNoSection(),
          ],
        ),
        const SizedBox(
          height: 8,
        ),
        _datesSection(),
        const SizedBox(
          height: 8,
        ),
        CStrippedTable(
          data: [
            if (widget.shipment.photoLink != null)
              StrippedTableItem(
                label: tr!.photoLink,
                value: widget.shipment.photoLink != null ? tr!.view : '',
                onTap: widget.shipment.photoLink != null
                    ? () {
                        launchUrl(Uri.parse(widget.shipment.photoLink ?? ''));
                      }
                    : null,
              ),
            StrippedTableItem(
              label: tr!.status,
              value: widget.shipment.status ?? '',
              valueWidget: ShipmentStateBadge(state: widget.shipment.shipmentState),
            ),
            StrippedTableItem(
              label: tr!.units,
              value: widget.shipment.noUnitsLoad ?? '',
            ),
            if (widget.shipment.bookings.vessel != null)
              StrippedTableItem(
                label: tr!.portOfLoading,
                value: widget.shipment.bookings.vessel!.location ?? '',
              ),
            StrippedTableItem(
              label: tr!.portOfDischarge,
              value: widget.shipment.bookings.destination ?? '',
            ),
            StrippedTableItem(
              label: tr!.size,
              value: widget.shipment.bookings.size ?? '',
            ),
            StrippedTableItem(
              label: tr!.loadingDate,
              value: widget.shipment.loadingDate != null
                  ? DateFormat(cardsDateFormat).format(
                      widget.shipment.loadingDate ?? DateTime.now(),
                    )
                  : '',
            ),
            if (widget.shipment.bookings.vessel != null)
              if (widget.shipment.bookings.vessel!.etd != null)
                StrippedTableItem(
                  label: tr!.etd,
                  value: DateFormat(cardsDateFormat).format(
                    widget.shipment.bookings.vessel!.etd ?? DateTime.now(),
                  ),
                ),
            if (widget.shipment.bookings.eta != null)
              StrippedTableItem(
                label: tr!.eta,
                value: DateFormat(cardsDateFormat).format(
                  widget.shipment.bookings.eta ?? DateTime.now(),
                ),
              ),
            if (widget.shipment.clearanceInvoiceLink != null && widget.shipment.clearanceInvoiceLink != '')
              StrippedTableItem(
                label: tr!.clearanceInvoice,
                value: tr!.view,
                onTap: widget.shipment.clearanceInvoiceLink != null
                    ? () {
                        launchUrl(Uri.parse(widget.shipment.clearanceInvoiceLink ?? ''));
                      }
                    : null,
              ),
          ],
        ),
        const SizedBox(
          height: 24,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              height: 32,
              child: FilledButton(
                style: const ButtonStyle(
                  padding: WidgetStatePropertyAll(
                    EdgeInsets.symmetric(horizontal: 12),
                  ),
                ),
                onPressed: _downloadBOL,
                child: Row(
                  children: [
                    bolDownloading
                        ? SizedBox(
                            height: 18,
                            width: 18,
                            child: Align(
                              child: SizedBox(
                                height: 16,
                                width: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Theme.of(context).colorScheme.onPrimary,
                                ),
                              ),
                            ),
                          )
                        : const Icon(
                            Symbols.download,
                            size: 18,
                          ),
                    const SizedBox(
                      width: 6,
                    ),
                    Text(
                      tr!.billOfLoading,
                      style: const TextStyle(
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        Container(
          height: 32,
        )
      ],
    );
  }

  Widget _bookingNoSection() {
    return Row(
      children: [
        Icon(
          Symbols.book,
          color: Theme.of(context).colorScheme.primary,
          size: 18,
          weight: 700,
        ),
        const SizedBox(
          width: 2,
        ),
        SelectableText(
          widget.shipment.getBookingNo(),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w700,
            color: Theme.of(context).brightness == Brightness.light ? Colors.black54 : Colors.white70,
          ),
        ),
      ],
    );
  }

  Widget _datesSection() {
    return Row(
      children: [
        Expanded(
          child: Row(
            children: [
              Icon(
                Symbols.event_upcoming,
                color: Theme.of(context).colorScheme.primary,
                size: 18,
                weight: 700,
              ),
              const SizedBox(
                width: 2,
              ),
              if (widget.shipment.bookings.vessel != null)
                if (widget.shipment.bookings.vessel!.etd != null)
                  Text(
                    DateFormat(cardsDateFormat).format(
                      widget.shipment.bookings.vessel!.etd ?? DateTime.now(),
                    ),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w700,
                      color: Theme.of(context).brightness == Brightness.light ? Colors.black54 : Colors.white70,
                    ),
                  ),
            ],
          ),
        ),
        Expanded(
          child: Row(
            children: [
              Icon(
                Symbols.event_available,
                color: Theme.of(context).colorScheme.primary,
                size: 18,
                weight: 700,
              ),
              const SizedBox(
                width: 2,
              ),
              if (widget.shipment.bookings.eta != null)
                Text(
                  DateFormat(cardsDateFormat).format(
                    widget.shipment.bookings.eta ?? DateTime.now(),
                  ),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                    color: Theme.of(context).brightness == Brightness.light ? Colors.black54 : Colors.white70,
                  ),
                ),
            ],
          ),
        )
      ],
    );
  }
}
