import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:payments_repository/payments_repository.dart';
import 'package:pgl_mobile_app/constants/date_formats.const.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/logic/classes/stripped_table_item.class.dart';
import 'package:pgl_mobile_app/presentation/widgets/badges/payment_state_badge.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/bottom_sheets/payments/payments_payments.bottom_sheet.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/payments/payment_item.card.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/bottom_sheet_style2.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_stripped_table.widget.dart';

class PaymentDetailBottomSheet extends StatefulWidget {
  const PaymentDetailBottomSheet({
    super.key,
    required this.payment,
  });

  final PaymentModel payment;

  @override
  State<PaymentDetailBottomSheet> createState() => _PaymentDetailBottomSheetState();
}

class _PaymentDetailBottomSheetState extends State<PaymentDetailBottomSheet> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return BottomSheetStyle2(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Symbols.tag,
                  size: 18,
                  color: Theme.of(context).colorScheme.primary,
                  weight: 700,
                ),
                const SizedBox(
                  width: 2,
                ),
                SelectableText(
                  'PGLPN${widget.payment.id.toString().padLeft(5, '0')}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
            // state
            PaymentStateBadge(state: widget.payment.state),
          ],
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            children: [
              SelectableText(
                widget.payment.transactionNumber,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w700,
                  height: 1.3,
                ),
                textAlign: TextAlign.start,
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 16,
        ),
        CStrippedTable(
          data: [
            if (widget.payment.amountApplied > 0)
              StrippedTableItem(
                label: tr!.invoiceApplied,
                value: tr!.view,
                onTap: () async => {
                  await showModalBottomSheet(
                    isScrollControlled: true,
                    useSafeArea: false,
                    context: context,
                    showDragHandle: true,
                    constraints: BoxConstraints(
                      maxHeight: MediaQuery.of(context).size.height * .9,
                    ),
                    builder: (context) => PaymentsPaymentsBottomSheet(
                      payment: widget.payment,
                    ),
                  )
                },
              ),
            StrippedTableItem(
              label: tr!.amount,
              value: getAmountWithCurrency(
                widget.payment,
                widget.payment.amount,
              ),
            ),
            StrippedTableItem(
              label: tr!.amountApplied,
              value: getAmountWithCurrency(
                widget.payment,
                widget.payment.amountApplied,
              ),
            ),
            StrippedTableItem(
              label: tr!.remainingAmount,
              value: getAmountWithCurrency(
                widget.payment,
                widget.payment.amount - widget.payment.amountApplied,
              ),
              valueWidget: Text(
                getAmountWithCurrency(
                  widget.payment,
                  widget.payment.amount - widget.payment.amountApplied,
                ),
                style: TextStyle(
                  fontSize: 12,
                  color: widget.payment.amount - widget.payment.amountApplied > 0
                      ? Theme.of(context).colorScheme.error
                      : Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
            StrippedTableItem(
              label: tr!.currency,
              value: widget.payment.currency,
            ),
            StrippedTableItem(
              label: tr!.paymentMethod,
              value: _getPaymentMethod(widget.payment.paymentMethod),
            ),
            StrippedTableItem(
              label: tr!.exchangeRate,
              value: NumberFormat('###,###,###,###.##').format(widget.payment.exchangeRate),
            ),
            StrippedTableItem(
              label: tr!.date,
              value: DateFormat(cardsDateFormat).format(
                widget.payment.createdAt,
              ),
            ),
            // StrippedTableItem(
            //   label: tr!.description,
            //   selectable: true,
            //   value: _getDescriptions() + widget.payment.description,
            //   textAlign: TextAlign.justify,
            // ),
          ],
        ),
        Container(
          height: 32,
        )
      ],
    );
  }

  String _getPaymentMethod(PaymentMethod paymentMethod) {
    switch (paymentMethod) {
      case PaymentMethod.cash:
        return tr!.cash;
      case PaymentMethod.check:
        return tr!.check;
      case PaymentMethod.wire:
        return tr!.wire;
      case PaymentMethod.damageCredit:
        return tr!.damageCredit;
      case PaymentMethod.demurrageCredit:
        return tr!.demurrageCredit;
      case PaymentMethod.exitPaperCredit:
        return tr!.exitPaperCredit;
      case PaymentMethod.mukhasa:
        return tr!.mukhasa;
      case PaymentMethod.salesTax:
        return tr!.salesTax;
      case PaymentMethod.storageCredit:
        return tr!.storageCredit;
    }
  }
}
