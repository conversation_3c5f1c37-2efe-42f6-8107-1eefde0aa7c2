import 'package:flutter/material.dart';
import 'package:payments_repository/payments_repository.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/exports/blocs.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/payments/payment_payment_item.card.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/payments/payment_payment_item.skeleton.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/bottom_sheet_style2.widget.dart';

class PaymentsPaymentsBottomSheet extends StatefulWidget {
  const PaymentsPaymentsBottomSheet({
    super.key,
    required this.payment,
  });

  final PaymentModel payment;

  @override
  State<PaymentsPaymentsBottomSheet> createState() => _PaymentsPaymentsBottomSheetState();
}

class _PaymentsPaymentsBottomSheetState extends State<PaymentsPaymentsBottomSheet> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late PaymentsBloc paymentsBloc = context.read<PaymentsBloc>();
  late PaymentModel payment = widget.payment;
  bool _loading = false;
  @override
  void initState() {
    _getPayments();
    super.initState();
  }

  void _getPayments() async {
    if (payment.payments.isEmpty) {
      setState(() {
        _loading = true;
      });
      final PaymentModel newPayment =
          await RepositoryProvider.of<PaymentsRepository>(context).getPayment(widget.payment);
      paymentsBloc.add(PaymentSingleFetch(payment: widget.payment));
      setState(() {
        _loading = false;
        payment = newPayment;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BottomSheetStyle2(
      children: [
        Row(
          children: [
            SizedBox(
              width: 12,
            ),
            Text(
              tr!.invoiceApplied,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
              textAlign: TextAlign.start,
            ),
          ],
        ),
        Divider(),
        SizedBox(
          height: 8,
        ),
        BlocBuilder<PaymentsBloc, PaymentsState>(
          builder: (context, state) {
            PaymentModel foundPayment = state.payments.firstWhere((element) => element.id == widget.payment.id);
            return Column(
              children: _loading
                  ? List.generate(1, (index) => PaymentPaymentItemCardSkeleton())
                  : foundPayment.payments
                      .map(
                        (PaymentReceived<PaymentModel> payment) => PaymentPaymentItemCard(
                          payment: payment,
                        ),
                      )
                      .toList(),
            );
          },
        ),
        Container(
          height: 32,
        )
      ],
    );
  }
}
