import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/constants/date_formats.const.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/logic/classes/stripped_table_item.class.dart';
import 'package:pgl_mobile_app/presentation/widgets/badges/transaction_state_badge.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/bottom_sheet_style2.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_stripped_table.widget.dart';

class TransactionDetailBottomSheet extends StatefulWidget {
  const TransactionDetailBottomSheet({
    super.key,
    required this.transaction,
  });

  final TransactionModel transaction;

  @override
  State<TransactionDetailBottomSheet> createState() => _TransactionDetailBottomSheetState();
}

class _TransactionDetailBottomSheetState extends State<TransactionDetailBottomSheet> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return BottomSheetStyle2(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Symbols.tag,
                  size: 18,
                  color: Theme.of(context).colorScheme.primary,
                  weight: 700,
                ),
                const SizedBox(
                  width: 2,
                ),
                SelectableText(
                  'T-0${widget.transaction.id}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
            // state
            TransactionStateBadge(type: widget.transaction.status),
          ],
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: SelectableText(
            widget.transaction.category.name,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w700,
              height: 1.3,
            ),
            textAlign: TextAlign.start,
          ),
        ),
        const SizedBox(
          height: 16,
        ),
        CStrippedTable(
          data: [
            StrippedTableItem(
              label: tr!.amount,
              value:
                  "${widget.transaction.amount > 0 ? NumberFormat('###,###,###,###').format(widget.transaction.amount) : '0'} (AED)",
            ),
            StrippedTableItem(
              label: tr!.type,
              value: _getType(widget.transaction.type),
            ),
            StrippedTableItem(
              label: tr!.paymentMethod,
              value: _getPaymentMethod(widget.transaction.paymentMethod),
            ),
            StrippedTableItem(
              label: tr!.date,
              value: DateFormat(cardsDateFormat).format(
                widget.transaction.createdAt,
              ),
            ),
            StrippedTableItem(
              label: tr!.description,
              selectable: true,
              value: _getDescriptions() + widget.transaction.description,
              textAlign: TextAlign.justify,
            ),
          ],
        ),
        Container(
          height: 32,
        )
      ],
    );
  }

  String _getType(TransactionType type) {
    switch (type) {
      case TransactionType.credit:
        return tr!.credit;
      case TransactionType.debit:
        return tr!.debit;
    }
  }

  String _getPaymentMethod(TransactionPaymentMethod method) {
    switch (method) {
      case TransactionPaymentMethod.cash:
        return tr!.cash;
      case TransactionPaymentMethod.bankTransfer:
        return tr!.bankTransfer;
    }
  }

  String _getDescriptions() {
    String str = '';
    for (var element in widget.transaction.transactionContainerVehicles) {
      if (element.type == 2 && element.vehicle != null) {
        if (!widget.transaction.description.contains(element.vehicle?['vin'])) {
          str += "${element.vehicle?['vin']} | ${element.vehicle?['lot_number']} \n";
        }
      } else if (element.type == 1 && element.container != null) {
        if (!widget.transaction.description.contains(element.container?['container_number'])) {
          str += "${element.vehicle?['container_number']} | ${element.vehicle?['bookings']?['booking_number']} \n";
        }
      }
    }
    return str;
  }
}
