import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:open_file/open_file.dart';
import 'package:pgl_mobile_app/constants/date_formats.const.dart';
import 'package:pgl_mobile_app/logic/classes/stripped_table_item.class.dart';
import 'package:pgl_mobile_app/presentation/widgets/badges/vehicle_state_badge.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/bottom_sheets/tracking.bottom_sheet.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/bottom_sheet_style2.widget.dart';
// import 'package:pgl_mobile_app/presentation/widgets/common/c_carousel.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_stripped_table.widget.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:vehicles_repository/vehicles_repository.dart';

class VehicleDetailBottomSheet extends StatefulWidget {
  const VehicleDetailBottomSheet({
    super.key,
    required this.vehicle,
  });

  final Vehicle vehicle;

  @override
  State<VehicleDetailBottomSheet> createState() => _VehicleDetailBottomSheetState();
}

class _VehicleDetailBottomSheetState extends State<VehicleDetailBottomSheet> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  late Duration difference = DateTime.now().difference(widget.vehicle.deliverDate ?? DateTime.now());

  @override
  Widget build(BuildContext context) {
    return BottomSheetStyle2(
      children: [
        // const CCarousel(
        //   items: [
        //     'https://picsum.photos/1000?image=4',
        //     'https://picsum.photos/1000?image=5',
        //     'https://picsum.photos/1000?image=6',
        //     'https://picsum.photos/1000?image=7',
        //     'https://picsum.photos/1000?image=8',
        //     'https://picsum.photos/1000?image=9',
        //     'https://picsum.photos/1000?image=10',
        //     'https://picsum.photos/1000?image=11',
        //     'https://picsum.photos/1000?image=12',
        //   ],
        // ),
        // const SizedBox(
        //   height: 20,
        // ),
        Row(
          children: [
            Expanded(
              child: Text(
                widget.vehicle.getDescription(),
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                ),
                maxLines: 3,
              ),
            ),
            const SizedBox(
              width: 8,
            ),
            Row(
              children: [
                // SizedBox(
                //   height: 32,
                //   width: 32,
                //   child: OutlinedButton(
                //     style: OutlinedButton.styleFrom(
                //       side: BorderSide(width: 1.0, color: Theme.of(context).colorScheme.primary),
                //       padding: EdgeInsets.zero,
                //     ),
                //     onPressed: () {},
                //     child: const Icon(
                //       Symbols.chat_bubble,
                //       size: 16,
                //     ),
                //   ),
                // ),
                const SizedBox(
                  width: 4,
                ),
                SizedBox(
                  height: 32,
                  child: FilledButton(
                    style: const ButtonStyle(
                      padding: WidgetStatePropertyAll(
                        EdgeInsets.symmetric(horizontal: 12),
                      ),
                    ),
                    onPressed: () async {
                      await showModalBottomSheet(
                        isScrollControlled: true,
                        useSafeArea: true,
                        context: context,
                        showDragHandle: true,
                        constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * .8),
                        builder: (context) => TrackingBottomSheet(
                          query: widget.vehicle.vin,
                        ),
                      );
                    },
                    child: Text(
                      tr!.track,
                      style: const TextStyle(
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
              ],
            )
          ],
        ),
        const SizedBox(
          height: 8,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _lotSection(),
            _vinSection(),
          ],
        ),
        const SizedBox(
          height: 8,
        ),
        CStrippedTable(
          data: [
            if (widget.vehicle.photoLink != null)
              StrippedTableItem(
                label: tr!.photoLink,
                value: widget.vehicle.photoLink != null ? tr!.view : '',
                onTap: widget.vehicle.photoLink != null
                    ? () {
                        launchUrl(Uri.parse(widget.vehicle.photoLink ?? ''));
                      }
                    : null,
              ),
            StrippedTableItem(
              label: tr!.status,
              value: tr!.status,
              valueWidget: VehicleStateBadge(
                state: widget.vehicle.vehicleState,
              ),
            ),
            if (widget.vehicle.container != null && widget.vehicle.container!.containerNumber != null)
              StrippedTableItem(
                label: tr!.containerNo,
                value: widget.vehicle.container!.containerNumber ?? '',
                selectable: true,
              ),
            StrippedTableItem(
              label: tr!.year,
              value: widget.vehicle.year,
            ),
            StrippedTableItem(
              label: tr!.make,
              value: widget.vehicle.make,
            ),
            StrippedTableItem(
              label: tr!.model,
              value: widget.vehicle.model,
            ),
            StrippedTableItem(
              label: tr!.color,
              value: widget.vehicle.color,
            ),
            StrippedTableItem(
              label: tr!.keyPresent,
              value: 'N/A',
              valueWidget: widget.vehicle.isKeyPresent != null
                  ? Icon(
                      widget.vehicle.isKeyPresent != null && widget.vehicle.isKeyPresent == true
                          ? Symbols.key
                          : Symbols.key_off,
                      color: widget.vehicle.isKeyPresent != null && widget.vehicle.isKeyPresent == true
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.error,
                    )
                  : null,
            ),
            StrippedTableItem(
              label: tr!.pointOfLoading,
              value: widget.vehicle.pointOfLoading ?? '',
            ),
            StrippedTableItem(
              label: tr!.accountNumber,
              value: widget.vehicle.buyerNumber ?? '',
            ),
            StrippedTableItem(
              label: tr!.auctionName,
              value: widget.vehicle.auctionName ?? '',
            ),
            StrippedTableItem(
              label: tr!.auctionCity,
              value: widget.vehicle.auctionCity ?? '',
            ),
            StrippedTableItem(
              label: tr!.autionInvLink,
              value: widget.vehicle.auctionInvoice != null && widget.vehicle.auctionInvoice!.isNotEmpty
                  ? tr!.view
                  : tr!.notAttached,
              onTap: widget.vehicle.auctionInvoice != null && widget.vehicle.auctionInvoice!.isNotEmpty
                  ? () {
                      launchUrl(Uri.parse(widget.vehicle.auctionInvoice ?? ''));
                    }
                  : null,
            ),
            if ([
              VehicleState.onHandNoTitle,
              VehicleState.onHandWithTitle,
            ].contains(widget.vehicle.vehicleState))
              StrippedTableItem(
                label: tr!.ageAtPGLWH,
                value: widget.vehicle.deliverDate != null
                    ? tr!.countDays(
                        DateTime.now().difference(widget.vehicle.deliverDate ?? DateTime.now()).inDays,
                      )
                    : '',
              ),
            StrippedTableItem(
              label: tr!.purchasedDate,
              value: widget.vehicle.purchaseAt != null
                  ? DateFormat(cardsDateFormat).format(
                      widget.vehicle.purchaseAt ?? DateTime.now(),
                    )
                  : '',
            ),
            if ([
              VehicleState.auctionPaid,
              VehicleState.auctionUnpaid,
              VehicleState.onTheWay,
            ].contains(
              widget.vehicle.vehicleState,
            )) ...[
              StrippedTableItem(
                label: tr!.reportDate,
                value: widget.vehicle.createdAt != null
                    ? DateFormat(cardsDateFormat).format(
                        widget.vehicle.createdAt ?? DateTime.now(),
                      )
                    : '',
              ),
              StrippedTableItem(
                label: tr!.paymentDate,
                value: widget.vehicle.paymentDate != null
                    ? DateFormat(cardsDateFormat).format(
                        widget.vehicle.paymentDate ?? DateTime.now(),
                      )
                    : '',
              ),
              if (widget.vehicle.towingRequestDate != null)
                StrippedTableItem(
                  label: tr!.towRequestDate,
                  value: widget.vehicle.towingRequestDate != null
                      ? DateFormat(cardsDateFormat).format(
                          widget.vehicle.towingRequestDate ?? DateTime.now(),
                        )
                      : '',
                ),
              StrippedTableItem(
                label: tr!.pickUpDate,
                value: widget.vehicle.pickUpDate != null
                    ? DateFormat(cardsDateFormat).format(
                        widget.vehicle.pickUpDate ?? DateTime.now(),
                      )
                    : '',
              ),
              StrippedTableItem(
                label: tr!.pickUpDaysFromPurchase,
                value: widget.vehicle.pickUpDate != null
                    ? tr!.countDays(
                        DateTime.now().difference(widget.vehicle.pickUpDate ?? DateTime.now()).inDays,
                      )
                    : '',
              ),
              StrippedTableItem(
                label: tr!.pickUpDaysFromReport,
                value: widget.vehicle.createdAt != null
                    ? tr!.countDays(
                        DateTime.now().difference(widget.vehicle.createdAt ?? DateTime.now()).inDays,
                      )
                    : '',
              ),
            ],
            StrippedTableItem(
              label: tr!.deliverDate,
              value: widget.vehicle.deliverDate != null
                  ? DateFormat(cardsDateFormat).format(
                      widget.vehicle.deliverDate ?? DateTime.now(),
                    )
                  : '',
            ),
            if (widget.vehicle.vehicleState == VehicleState.shipped)
              StrippedTableItem(
                label: tr!.loadingDate,
                value: widget.vehicle.container != null
                    ? DateFormat(cardsDateFormat).format(
                        widget.vehicle.container!.loadingDate,
                      )
                    : '',
              ),
            if (widget.vehicle.container != null)
              StrippedTableItem(
                label: tr!.etd,
                value: widget.vehicle.container != null
                    ? DateFormat(cardsDateFormat).format(
                        widget.vehicle.container!.booking.etd ?? DateTime.now(),
                      )
                    : '',
              ),
            if (widget.vehicle.container != null)
              StrippedTableItem(
                label: tr!.eta,
                value: widget.vehicle.container != null
                    ? DateFormat(cardsDateFormat).format(
                        widget.vehicle.container!.booking.eta ?? DateTime.now(),
                      )
                    : '',
              ),
            if (widget.vehicle.vehicleState == VehicleState.onHandWithTitle ||
                (widget.vehicle.vehicleState == VehicleState.shipped && widget.vehicle.titleRecievedDate != null))
              StrippedTableItem(
                label: tr!.titleReceiveDate,
                value: widget.vehicle.titleRecievedDate != null
                    ? DateFormat(cardsDateFormat).format(
                        widget.vehicle.titleRecievedDate ?? DateTime.now(),
                      )
                    : '',
              ),
            StrippedTableItem(
              label: tr!.titleState,
              value: widget.vehicle.titleState ?? '',
              valueWidget: widget.vehicle.isTitleExist
                  ? const Icon(
                      Symbols.check_circle,
                      color: Colors.green,
                      size: 20,
                    )
                  : const Icon(
                      Symbols.cancel,
                      color: Colors.red,
                      size: 20,
                    ),
            ),
            StrippedTableItem(
              label: tr!.titleStatus,
              value: widget.vehicle.titleStatus ?? '',
            ),
          ],
        ),
        if (widget.vehicle.vehicleState == VehicleState.shipped) ..._vehiclePdfsSection(),
        Container(
          height: 32,
        )
      ],
    );
  }

  Widget _lotSection() {
    return Row(
      children: [
        Icon(
          Symbols.tag,
          size: 18,
          color: Theme.of(context).colorScheme.primary,
          weight: 700,
        ),
        const SizedBox(
          width: 2,
        ),
        SelectableText(
          widget.vehicle.lotNumber,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w700,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  Widget _vinSection() {
    return Row(
      children: [
        Icon(
          Symbols.directions_car,
          color: Theme.of(context).colorScheme.primary,
          size: 18,
          weight: 700,
        ),
        const SizedBox(
          width: 2,
        ),
        SelectableText(
          widget.vehicle.vin,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w700,
            color: Theme.of(context).brightness == Brightness.light ? Colors.black54 : Colors.white70,
          ),
        ),
      ],
    );
  }

  bool dockReceiptDownloading = false;
  bool bolDownloading = false;

  Future<void> _downloadDockReceipt() async {
    try {
      setState(() {
        dockReceiptDownloading = true;
      });
      File dockReceipt = await RepositoryProvider.of<VehiclesRepository>(context).getDockReceiptPDF(widget.vehicle.id);
      OpenFile.open(dockReceipt.path);
      setState(() {
        dockReceiptDownloading = false;
      });
    } catch (e) {
      setState(() {
        dockReceiptDownloading = false;
      });
    }
  }

  Future<void> _downloadBOL() async {
    try {
      setState(() {
        bolDownloading = true;
      });
      File bol = await RepositoryProvider.of<VehiclesRepository>(context).getbillOfLoadingPDF(widget.vehicle.id);
      OpenFile.open(bol.path);
      setState(() {
        bolDownloading = false;
      });
    } catch (e) {
      setState(() {
        bolDownloading = false;
      });
    }
  }

  List<Widget> _vehiclePdfsSection() {
    return [
      const SizedBox(
        height: 24,
      ),
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            height: 32,
            child: FilledButton(
              style: const ButtonStyle(
                padding: WidgetStatePropertyAll(
                  EdgeInsets.symmetric(horizontal: 12),
                ),
              ),
              onPressed: _downloadDockReceipt,
              child: Row(
                children: [
                  dockReceiptDownloading
                      ? SizedBox(
                          height: 18,
                          width: 18,
                          child: Align(
                            child: SizedBox(
                              height: 16,
                              width: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Theme.of(context).colorScheme.onPrimary,
                              ),
                            ),
                          ),
                        )
                      : const Icon(
                          Symbols.download,
                          size: 18,
                        ),
                  const SizedBox(
                    width: 6,
                  ),
                  Text(
                    tr!.dockReceipt,
                    style: const TextStyle(
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(
            width: 12,
          ),
          SizedBox(
            height: 32,
            child: FilledButton(
              style: const ButtonStyle(
                padding: WidgetStatePropertyAll(
                  EdgeInsets.symmetric(horizontal: 12),
                ),
              ),
              onPressed: _downloadBOL,
              child: Row(
                children: [
                  bolDownloading
                      ? SizedBox(
                          height: 18,
                          width: 18,
                          child: Align(
                            child: SizedBox(
                              height: 16,
                              width: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Theme.of(context).colorScheme.onPrimary,
                              ),
                            ),
                          ),
                        )
                      : const Icon(
                          Symbols.download,
                          size: 18,
                        ),
                  const SizedBox(
                    width: 6,
                  ),
                  Text(
                    tr!.billOfLoading,
                    style: const TextStyle(
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      )
    ];
  }
}
