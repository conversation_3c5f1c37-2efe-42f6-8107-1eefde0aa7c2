import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:invoices_repository/invoices_repository.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/constants/date_formats.const.dart';
import 'package:pgl_mobile_app/logic/classes/stripped_table_item.class.dart';
import 'package:pgl_mobile_app/presentation/widgets/badges/invoice_state_badge.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/bottom_sheets/invoices/invoice_payments.bottom_sheet.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/bottom_sheet_style2.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/c_stripped_table.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:open_file/open_file.dart';

class InvoiceDetailBottomSheet extends StatefulWidget {
  const InvoiceDetailBottomSheet({
    super.key,
    required this.invoice,
  });

  final Invoice invoice;

  @override
  State<InvoiceDetailBottomSheet> createState() => _InvoiceDetailBottomSheetState();
}

class _InvoiceDetailBottomSheetState extends State<InvoiceDetailBottomSheet> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  bool invoiceDownloading = false;

  Future<void> _downloadInvoice() async {
    try {
      setState(() {
        invoiceDownloading = true;
      });
      File invoice = await RepositoryProvider.of<InvoicesRepository>(context).getInvoicePDF(widget.invoice.id);
      OpenFile.open(invoice.path);
      setState(() {
        invoiceDownloading = false;
      });
    } catch (e) {
      setState(() {
        invoiceDownloading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BottomSheetStyle2(
      children: [
        Row(
          children: [
            Expanded(
              child: SelectableText(
                widget.invoice.containerNumber,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
            const SizedBox(
              width: 8,
            ),
            Row(
              children: [
                SizedBox(
                  height: 32,
                  child: FilledButton(
                    style: const ButtonStyle(
                      padding: WidgetStatePropertyAll(
                        EdgeInsets.symmetric(horizontal: 12),
                      ),
                    ),
                    onPressed: _downloadInvoice,
                    child: Row(
                      children: [
                        invoiceDownloading
                            ? SizedBox(
                                height: 18,
                                width: 18,
                                child: Align(
                                  child: SizedBox(
                                    height: 16,
                                    width: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: Theme.of(context).colorScheme.onPrimary,
                                    ),
                                  ),
                                ),
                              )
                            : const Icon(
                                Symbols.download,
                                size: 18,
                              ),
                        const SizedBox(
                          width: 6,
                        ),
                        Text(
                          tr!.download,
                          style: const TextStyle(
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            )
          ],
        ),
        const SizedBox(
          height: 8,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _invoiceNoSection(),
          ],
        ),
        const SizedBox(
          height: 8,
        ),
        _datesSection(),
        const SizedBox(
          height: 8,
        ),
        CStrippedTable(
          data: [
            if (widget.invoice.getPaymentReceivedNumber() > 0)
              StrippedTableItem(
                label: tr!.invoiceApplied,
                value: tr!.view,
                onTap: () async => {
                  await showModalBottomSheet(
                    isScrollControlled: true,
                    useSafeArea: true,
                    context: context,
                    showDragHandle: true,
                    constraints: BoxConstraints(
                      maxHeight: MediaQuery.of(context).size.height * .9,
                    ),
                    builder: (context) => InvoicePaymentsBottomSheet(
                      invoice: widget.invoice,
                    ),
                  )
                },
              ),
            StrippedTableItem(
              label: tr!.status,
              value: tr!.status,
              valueWidget: InvoiceStateBadge(state: widget.invoice.invoiceState),
            ),
            StrippedTableItem(
              label: tr!.purpose,
              value: widget.invoice.purpose,
            ),
            StrippedTableItem(
              label: tr!.amount,
              value: '\$${widget.invoice.getAmount()}',
            ),
            StrippedTableItem(
              label: tr!.received,
              value: '\$${widget.invoice.getPaymentReceived()}',
            ),
            StrippedTableItem(
              label: tr!.amountDue,
              value: '\$${widget.invoice.getAmountDue()}',
            ),
            StrippedTableItem(
              label: tr!.issueDate,
              value: DateFormat(cardsDateFormat).format(
                widget.invoice.invoiceDate,
              ),
            ),
            if (widget.invoice.invoiceDueDate != null)
              StrippedTableItem(
                label: tr!.dueDate,
                value: DateFormat(cardsDateFormat).format(
                  widget.invoice.invoiceDueDate ?? DateTime.now(),
                ),
              ),
            if ([
                  InvoiceState.open,
                  InvoiceState.pastDue,
                ].contains(widget.invoice.invoiceState) &&
                widget.invoice.invoiceDueDate != null)
              StrippedTableItem(
                label: tr!.pastDueDays,
                value: tr!.countDays(
                  DateTime.now().difference(widget.invoice.invoiceDueDate ?? DateTime.now()).inDays,
                ),
              ),
            if (widget.invoice.invoiceState == InvoiceState.paid)
              StrippedTableItem(
                label: tr!.receivedDate,
                value: widget.invoice.receivedDates.isNotEmpty
                    ? widget.invoice.receivedDates
                        .map((receivedDate) => DateFormat(cardsDateFormat).format(
                              receivedDate,
                            ))
                        .join(' | ')
                    : '',
              ),
            StrippedTableItem(
              label: tr!.description,
              value: widget.invoice.description ?? '',
            ),
          ],
        ),
        Container(
          height: 32,
        )
      ],
    );
  }

  Widget _invoiceNoSection() {
    return Row(
      children: [
        Icon(
          Symbols.tag,
          color: Theme.of(context).colorScheme.primary,
          size: 18,
          weight: 700,
        ),
        const SizedBox(
          width: 2,
        ),
        SelectableText(
          widget.invoice.invoiceNumber,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w700,
            color: Theme.of(context).brightness == Brightness.light ? Colors.black54 : Colors.white70,
          ),
        ),
      ],
    );
  }

  Widget _datesSection() {
    return Row(
      children: [
        Expanded(
          child: Row(
            children: [
              Icon(
                Symbols.edit_calendar,
                color: Theme.of(context).colorScheme.primary,
                size: 18,
                weight: 700,
              ),
              const SizedBox(
                width: 2,
              ),
              Text(
                DateFormat(cardsDateFormat).format(
                  widget.invoice.invoiceDate,
                ),
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w700,
                  color: Theme.of(context).brightness == Brightness.light ? Colors.black54 : Colors.white70,
                ),
              ),
            ],
          ),
        ),
        if (widget.invoice.invoiceDueDate != null)
          Expanded(
            child: Row(
              children: [
                Icon(
                  Symbols.calendar_clock,
                  color: Theme.of(context).colorScheme.primary,
                  size: 18,
                  weight: 700,
                ),
                const SizedBox(
                  width: 2,
                ),
                Text(
                  DateFormat(cardsDateFormat).format(
                    widget.invoice.invoiceDueDate ?? DateTime.now(),
                  ),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                    color: Theme.of(context).brightness == Brightness.light ? Colors.black54 : Colors.white70,
                  ),
                ),
              ],
            ),
          )
      ],
    );
  }
}
