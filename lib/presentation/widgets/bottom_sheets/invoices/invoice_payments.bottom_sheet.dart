import 'package:flutter/material.dart';
import 'package:invoices_repository/invoices_repository.dart';
import 'package:payments_repository/payments_repository.dart';
import 'package:pgl_mobile_app/exports/blocs.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/invoices/invoice_payment_item.card.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/invoices/invoice_payment_item.skeleton.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/bottom_sheet_style2.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

class InvoicePaymentsBottomSheet extends StatefulWidget {
  const InvoicePaymentsBottomSheet({
    super.key,
    required this.invoice,
  });

  final Invoice invoice;

  @override
  State<InvoicePaymentsBottomSheet> createState() => _InvoicePaymentsBottomSheetState();
}

class _InvoicePaymentsBottomSheetState extends State<InvoicePaymentsBottomSheet> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late InvoicesBloc invoicesBloc = context.read<InvoicesBloc>();
  late Invoice invoice = widget.invoice;
  bool _loading = false;

  @override
  void initState() {
    _getPayments();
    super.initState();
  }

  void _getPayments() async {
    if (invoice.payments.isEmpty) {
      setState(() {
        _loading = true;
      });
      final Invoice newInvoice =
          await RepositoryProvider.of<InvoicesRepository>(context).getInvoicePayments(widget.invoice);
      invoicesBloc.add(AddInvoicePayments(invoice: widget.invoice));
      setState(() {
        _loading = false;
        invoice = newInvoice;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BottomSheetStyle2(
      children: [
        Text(
          tr!.invoiceApplied,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(
          height: 8,
        ),
        BlocBuilder<InvoicesBloc, InvoicesState>(
          builder: (context, state) {
            Invoice foundPayment = state.invoices.firstWhere((element) => element.id == widget.invoice.id);
            return Column(
              children: _loading
                  ? List.generate(1, (index) => InvoicePaymentItemCardSkeleton())
                  : foundPayment.payments
                      .map(
                        (PaymentReceived<Invoice> payment) => InvoicePaymentItemCard(
                          payment: payment,
                        ),
                      )
                      .toList(),
            );
          },
        ),
        Container(
          height: 32,
        )
      ],
    );
  }
}
