import 'package:dio_client/dio_client.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/constants/constants.dart';
import 'package:pgl_mobile_app/constants/date_formats.const.dart';
import 'package:pgl_mobile_app/exports/blocs.exports.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/logic/classes/select_item.class.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/bottom_sheet_style.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/widgets/filter/fields/auto_complete_field.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/filter/fields/fields.exports.dart';

class ShipmentFilterBottomSheet extends StatefulWidget {
  const ShipmentFilterBottomSheet({
    super.key,
  });

  @override
  State<ShipmentFilterBottomSheet> createState() => _ShipmentFilterBottomSheetState();
}

class _ShipmentFilterBottomSheetState extends State<ShipmentFilterBottomSheet> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late List<SelectItem> shipmentsSummaryItems = getShipmentsSummaryItems(context);

  late TextEditingController containerController;
  late TextEditingController bookingContoller;
  final TextEditingController loadingDateFromController = TextEditingController();
  final TextEditingController loadingDateToController = TextEditingController();
  final TextEditingController etdFromController = TextEditingController();
  final TextEditingController etdToController = TextEditingController();

  final _formKey = GlobalKey<FormState>();

  late ShipmentFilterData shipmentFilterData;
  List<ShipmentState> status = [];

  @override
  void initState() {
    // intial values
    shipmentFilterData = context.read<ShipmentBloc>().state.shipmentsFilter.filterData ?? const ShipmentFilterData();
    status = [...shipmentFilterData.status];
    loadingDateFromController.text = shipmentFilterData.loadingFromDate != null
        ? DateFormat(cardsDateFormat).format(
            shipmentFilterData.loadingFromDate ?? DateTime.now(),
          )
        : '';
    loadingDateToController.text = shipmentFilterData.loadingToDate != null
        ? DateFormat(cardsDateFormat).format(
            shipmentFilterData.loadingToDate ?? DateTime.now(),
          )
        : '';
    etdFromController.text = shipmentFilterData.etdFromDate != null
        ? DateFormat(cardsDateFormat).format(
            shipmentFilterData.etdFromDate ?? DateTime.now(),
          )
        : '';
    etdToController.text = shipmentFilterData.etdToDate != null
        ? DateFormat(cardsDateFormat).format(
            shipmentFilterData.etdToDate ?? DateTime.now(),
          )
        : '';

    super.initState();
  }

  void _clearFilterData() {
    setState(() {
      shipmentFilterData = const ShipmentFilterData();
      status = [];
      containerController.text = '';
      bookingContoller.text = '';
      loadingDateFromController.text = '';
      loadingDateToController.text = '';
      etdFromController.text = '';
      etdToController.text = '';
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ShipmentBloc, ShipmentsState>(
      builder: (context, state) {
        return Form(
          key: _formKey,
          child: BottomSheetStyle(
            header: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      tr!.filter,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    SizedBox(
                      height: 32,
                      child: FilledButton(
                        style: const ButtonStyle(
                          padding: WidgetStatePropertyAll(
                            EdgeInsets.symmetric(horizontal: 12),
                          ),
                        ),
                        onPressed: () {
                          _clearFilterData();
                        },
                        child: Row(
                          children: [
                            const Icon(
                              Symbols.close,
                              size: 16,
                              weight: 800,
                            ),
                            const SizedBox(
                              width: 4,
                            ),
                            Text(
                              tr!.clear,
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                const Divider(),
              ],
            ),
            footer: Column(
              children: [
                const SizedBox(
                  height: 16,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    SizedBox(
                      height: 32,
                      child: TextButton(
                        style: const ButtonStyle(
                          padding: WidgetStatePropertyAll(
                            EdgeInsets.symmetric(horizontal: 12),
                          ),
                        ),
                        onPressed: () {
                          _clearFilterData();
                          Get.back();
                        },
                        child: Text(
                          tr!.cancel,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 8,
                    ),
                    SizedBox(
                      height: 32,
                      child: FilledButton(
                        style: const ButtonStyle(
                          padding: WidgetStatePropertyAll(
                            EdgeInsets.symmetric(horizontal: 12),
                          ),
                        ),
                        onPressed: () {
                          context.read<ShipmentBloc>().add(
                                ShipmentsFilterChanged(
                                  shipmentsFilter: state.shipmentsFilter.copyWith(
                                    page: 1,
                                    filterData: shipmentFilterData,
                                  ),
                                ),
                              );
                          context.read<ShipmentBloc>().add(
                                ShipmentsFetched(),
                              );
                          Get.back();
                        },
                        child: Text(
                          tr!.apply,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 32,
                ),
              ],
            ),
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 0),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _getTitle('${tr!.idFiltering} :'),
                      _idFilteringSection(),
                      _getTitle('${tr!.loadingDate} :'),
                      _purchaseDateSection(),
                      _getTitle('${tr!.etd} :'),
                      _etdSection(),
                      _getTitle('${tr!.status} :'),
                      _statusSection(),
                      const SizedBox(
                        height: 16,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _getTitle(String text) {
    return Column(
      children: [
        const SizedBox(
          height: 16,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              text,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: .8),
              ),
            ),
          ],
        ),
        const SizedBox(
          height: 16,
        ),
      ],
    );
  }

  Future<Iterable<ShipmentFilterContainer>> _getContainerItems(String search) async {
    try {
      var res = await RepositoryProvider.of<DioClient>(context).get(
        '/api/customer/autoComplete',
        queryParameters: {
          'column_name': 'container_number',
          'modal': 'containers',
          'ids': '',
          'content': '',
          'container_number': search
        },
      );
      return res['data'].map<ShipmentFilterContainer>((item) => ShipmentFilterContainer.fromMap(item)).toList();
    } catch (e) {
      return [];
    }
  }

  Future<Iterable<ShipmentFilterBooking>> _getBookingItems(String search) async {
    try {
      var res = await RepositoryProvider.of<DioClient>(context).get(
        '/api/customer/autoComplete',
        queryParameters: {
          'column_name': 'booking_number',
          'modal': 'bookings',
          'ids': '',
          'content': '',
          'booking_number': search
        },
      );
      return res['data'].map<ShipmentFilterBooking>((item) => ShipmentFilterBooking.fromMap(item)).toList();
    } catch (e) {
      return [];
    }
  }

  Widget _idFilteringSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: AutoCompleteField<ShipmentFilterContainer>(
            label: tr!.containerNo,
            icon: Symbols.tag,
            getItems: _getContainerItems,
            displayStringForOption: (ShipmentFilterContainer option) {
              return option.containerNo.trim();
            },
            onChanged: (value) {
              setState(() {
                shipmentFilterData = shipmentFilterData.copyWith(filterContainer: value);
              });
            },
            setController: (cn) {
              containerController = cn;
            },
            initialValue: shipmentFilterData.filterContainer?.containerNo,
          ),
        ),
        const SizedBox(
          width: 8,
        ),
        Expanded(
          child: AutoCompleteField<ShipmentFilterBooking>(
            label: tr!.bookingNo,
            icon: Symbols.tag,
            getItems: _getBookingItems,
            displayStringForOption: (ShipmentFilterBooking option) {
              return option.bookingNo.trim();
            },
            onChanged: (value) {
              setState(() {
                shipmentFilterData = shipmentFilterData.copyWith(filterBooking: value);
              });
            },
            setController: (cn) {
              bookingContoller = cn;
            },
            initialValue: shipmentFilterData.filterBooking?.bookingNo,
          ),
        ),
      ],
    );
  }

  Widget _purchaseDateSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: SizedBox(
            height: 44,
            child: FilterTextField(
              label: tr!.fromDate,
              icon: Symbols.calendar_month,
              textEditingController: loadingDateFromController,
              onTap: () async {
                DateTime? date = await showDatePicker(
                  context: context,
                  firstDate: DateTime(2015),
                  lastDate: shipmentFilterData.loadingToDate ?? DateTime.now(),
                  initialDate: shipmentFilterData.loadingFromDate,
                );
                if (date != null) {
                  loadingDateFromController.text = DateFormat(cardsDateFormat).format(
                    date,
                  );
                  shipmentFilterData = shipmentFilterData.copyWith(loadingFromDate: date);
                }
              },
              readOnly: true,
            ),
          ),
        ),
        const SizedBox(
          width: 8,
        ),
        Expanded(
          child: SizedBox(
            height: 44,
            child: FilterTextField(
              label: tr!.toDate,
              icon: Symbols.calendar_month,
              textEditingController: loadingDateToController,
              onTap: () async {
                DateTime? date = await showDatePicker(
                  context: context,
                  firstDate: shipmentFilterData.loadingFromDate ?? DateTime(2015),
                  lastDate: DateTime.now(),
                  initialDate: shipmentFilterData.loadingToDate,
                );
                if (date != null) {
                  loadingDateToController.text = DateFormat(cardsDateFormat).format(
                    date,
                  );
                  shipmentFilterData = shipmentFilterData.copyWith(loadingToDate: date);
                }
              },
              readOnly: true,
            ),
          ),
        ),
      ],
    );
  }

  Widget _etdSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: SizedBox(
            height: 44,
            child: FilterTextField(
              label: tr!.fromDate,
              icon: Symbols.calendar_month,
              textEditingController: etdFromController,
              onTap: () async {
                DateTime? date = await showDatePicker(
                  context: context,
                  firstDate: DateTime(2015),
                  lastDate: shipmentFilterData.etdToDate ?? DateTime.now(),
                  initialDate: shipmentFilterData.etdFromDate,
                );
                if (date != null) {
                  etdFromController.text = DateFormat(cardsDateFormat).format(
                    date,
                  );
                  shipmentFilterData = shipmentFilterData.copyWith(etdFromDate: date);
                }
              },
              readOnly: true,
            ),
          ),
        ),
        const SizedBox(
          width: 8,
        ),
        Expanded(
          child: SizedBox(
            height: 44,
            child: FilterTextField(
              label: tr!.toDate,
              icon: Symbols.calendar_month,
              textEditingController: etdToController,
              onTap: () async {
                DateTime? date = await showDatePicker(
                  context: context,
                  firstDate: shipmentFilterData.etdFromDate ?? DateTime(2015),
                  lastDate: DateTime.now(),
                  initialDate: shipmentFilterData.etdToDate,
                );
                if (date != null) {
                  etdToController.text = DateFormat(cardsDateFormat).format(
                    date,
                  );
                  shipmentFilterData = shipmentFilterData.copyWith(etdToDate: date);
                }
              },
              readOnly: true,
            ),
          ),
        ),
      ],
    );
  }

  Widget _statusSection() {
    return Wrap(
      spacing: 5.0,
      children: shipmentsSummaryItems.where((el) => el.value != 'total').map<Widget>((SelectItem item) {
        return FilterChip.elevated(
          padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 6),
          labelPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 6),
          iconTheme: const IconThemeData(
            size: 10,
          ),
          showCheckmark: false,
          label: Text(item.label),
          selected: status.contains(ShipmentState.fromJson(item.value)),
          onSelected: (bool selected) {
            setState(() {
              if (selected) {
                status.add(ShipmentState.fromJson(item.value));
              } else {
                status.remove(ShipmentState.fromJson(item.value));
              }
              shipmentFilterData = shipmentFilterData.copyWith(status: status);
            });
          },
        );
      }).toList(),
    );
  }
}
