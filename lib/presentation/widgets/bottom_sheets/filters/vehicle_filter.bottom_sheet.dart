import 'package:dio_client/dio_client.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/constants/constants.dart';
import 'package:pgl_mobile_app/constants/date_formats.const.dart';
import 'package:pgl_mobile_app/exports/blocs.exports.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/logic/classes/select_item.class.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/bottom_sheet_style.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/widgets/filter/fields/auto_complete_field.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/filter/fields/fields.exports.dart';

class VehicleFilterBottomSheet extends StatefulWidget {
  const VehicleFilterBottomSheet({
    super.key,
  });

  @override
  State<VehicleFilterBottomSheet> createState() => _VehicleFilterBottomSheetState();
}

class _VehicleFilterBottomSheetState extends State<VehicleFilterBottomSheet> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late List<SelectItem> vehiclesSummaryItems = getVehiclesSummaryItems(context);

  final TextEditingController lotNoController = TextEditingController();
  final TextEditingController vinController = TextEditingController();
  final TextEditingController makeController = TextEditingController();
  final TextEditingController modelController = TextEditingController();
  late TextEditingController yearController;
  late TextEditingController pointOfLoadingController;
  late TextEditingController containerController;
  final TextEditingController priceStartController = TextEditingController();
  final TextEditingController priceEndController = TextEditingController();
  final TextEditingController purchaseDateFromController = TextEditingController();
  final TextEditingController purchaseDateToController = TextEditingController();
  final TextEditingController paymentDateFromController = TextEditingController();
  final TextEditingController paymentDateToController = TextEditingController();
  final TextEditingController deliverDateFromController = TextEditingController();
  final TextEditingController deliverDateToController = TextEditingController();

  final _formKey = GlobalKey<FormState>();

  late VehicleFilterData vehicleFilterData;
  List<VehicleState> carState = [];

  @override
  void initState() {
    // intial values
    vehicleFilterData = context.read<VehiclesBloc>().state.vehiclesFilter.filterData ?? const VehicleFilterData();
    carState = [...vehicleFilterData.carState];
    lotNoController.text = vehicleFilterData.lotNumber;
    vinController.text = vehicleFilterData.vin;
    modelController.text = vehicleFilterData.vin;
    priceStartController.text = vehicleFilterData.price?['min'] ?? '';
    priceEndController.text = vehicleFilterData.price?['max'] ?? '';
    purchaseDateFromController.text = vehicleFilterData.purchaseFromDate != null
        ? DateFormat(cardsDateFormat).format(
            vehicleFilterData.purchaseFromDate ?? DateTime.now(),
          )
        : '';
    purchaseDateToController.text = vehicleFilterData.purchaseToDate != null
        ? DateFormat(cardsDateFormat).format(
            vehicleFilterData.purchaseToDate ?? DateTime.now(),
          )
        : '';
    paymentDateFromController.text = vehicleFilterData.paymentFromDate != null
        ? DateFormat(cardsDateFormat).format(
            vehicleFilterData.paymentFromDate ?? DateTime.now(),
          )
        : '';
    paymentDateToController.text = vehicleFilterData.paymentToDate != null
        ? DateFormat(cardsDateFormat).format(
            vehicleFilterData.paymentToDate ?? DateTime.now(),
          )
        : '';
    deliverDateFromController.text = vehicleFilterData.deliverFromDate != null
        ? DateFormat(cardsDateFormat).format(
            vehicleFilterData.deliverFromDate ?? DateTime.now(),
          )
        : '';
    deliverDateToController.text = vehicleFilterData.deliverToDate != null
        ? DateFormat(cardsDateFormat).format(
            vehicleFilterData.deliverToDate ?? DateTime.now(),
          )
        : '';

    // listeners
    lotNoController.addListener(() {
      setState(() {
        vehicleFilterData = vehicleFilterData.copyWith(lotNumber: lotNoController.text);
      });
    });
    vinController.addListener(() {
      setState(() {
        vehicleFilterData = vehicleFilterData.copyWith(vin: vinController.text);
      });
    });
    makeController.addListener(() {
      setState(() {
        vehicleFilterData = vehicleFilterData.copyWith(make: makeController.text);
      });
    });
    modelController.addListener(() {
      setState(() {
        vehicleFilterData = vehicleFilterData.copyWith(model: modelController.text);
      });
    });
    priceStartController.addListener(() {
      setState(() {
        if (vehicleFilterData.price?['max'] == null && priceStartController.text == '') {
          vehicleFilterData = vehicleFilterData.copyWith(price: null);
        } else {
          vehicleFilterData = vehicleFilterData.copyWith(price: {
            if (vehicleFilterData.price?['max'] != null) 'max': vehicleFilterData.price?['max'],
            if (priceStartController.text != '') 'min': priceStartController.text,
          });
        }
      });
    });
    priceEndController.addListener(() {
      setState(() {
        if (vehicleFilterData.price?['min'] == null && priceEndController.text == '') {
          vehicleFilterData = vehicleFilterData.copyWith(price: null);
        } else {
          vehicleFilterData = vehicleFilterData.copyWith(price: {
            if (vehicleFilterData.price?['min'] != null) 'min': vehicleFilterData.price?['min'],
            if (priceEndController.text != '') 'max': priceEndController.text,
          });
        }
      });
    });
    super.initState();
  }

  void _clearFilterData() {
    setState(() {
      vehicleFilterData = const VehicleFilterData();
      carState = [];
      lotNoController.text = '';
      vinController.text = '';
      makeController.text = '';
      modelController.text = '';
      priceStartController.text = '';
      priceEndController.text = '';
      yearController.text = '';
      pointOfLoadingController.text = '';
      containerController.text = '';
      purchaseDateFromController.text = '';
      purchaseDateToController.text = '';
      paymentDateFromController.text = '';
      paymentDateToController.text = '';
      deliverDateFromController.text = '';
      deliverDateToController.text = '';
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<VehiclesBloc, VehiclesState>(
      builder: (context, state) {
        return Form(
          key: _formKey,
          child: BottomSheetStyle(
            header: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      tr!.filter,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    SizedBox(
                      height: 32,
                      child: FilledButton(
                        style: const ButtonStyle(
                          padding: WidgetStatePropertyAll(
                            EdgeInsets.symmetric(horizontal: 12),
                          ),
                        ),
                        onPressed: () {
                          _clearFilterData();
                        },
                        child: Row(
                          children: [
                            const Icon(
                              Symbols.close,
                              size: 16,
                              weight: 800,
                            ),
                            const SizedBox(
                              width: 4,
                            ),
                            Text(
                              tr!.clear,
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                const Divider(),
              ],
            ),
            footer: Column(
              children: [
                const SizedBox(
                  height: 16,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    SizedBox(
                      height: 32,
                      child: TextButton(
                        style: const ButtonStyle(
                          padding: WidgetStatePropertyAll(
                            EdgeInsets.symmetric(horizontal: 12),
                          ),
                        ),
                        onPressed: () {
                          _clearFilterData();
                          Get.back();
                        },
                        child: Text(
                          tr!.cancel,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 8,
                    ),
                    SizedBox(
                      height: 32,
                      child: FilledButton(
                        style: const ButtonStyle(
                          padding: WidgetStatePropertyAll(
                            EdgeInsets.symmetric(horizontal: 12),
                          ),
                        ),
                        onPressed: () {
                          context.read<VehiclesBloc>().add(
                                VehiclesFilterChanged(
                                  vehiclesFilter: state.vehiclesFilter.copyWith(
                                    page: 1,
                                    filterData: vehicleFilterData,
                                  ),
                                ),
                              );
                          context.read<VehiclesBloc>().add(
                                VehiclesFetched(),
                              );
                          Get.back();
                        },
                        child: Text(
                          tr!.apply,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 32,
                ),
              ],
            ),
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 0),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _getTitle('${tr!.idFiltering} :'),
                      _idFilteringSection(),
                      _getTitle('${tr!.data} :'),
                      ..._dataSection(),
                      _getTitle('${tr!.price} :'),
                      _priceSection(),
                      _getTitle('${tr!.purchaseDate} :'),
                      _purchaseDateSection(),
                      _getTitle('${tr!.paymentDate} :'),
                      _paymentDateSection(),
                      _getTitle('${tr!.deliverDate} :'),
                      _deliveryDateSection(),
                      _getTitle('${tr!.status} :'),
                      _statusSection(),
                      const SizedBox(
                        height: 16,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _getTitle(String text) {
    return Column(
      children: [
        const SizedBox(
          height: 16,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              text,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: .8),
              ),
            ),
          ],
        ),
        const SizedBox(
          height: 16,
        ),
      ],
    );
  }

  Future<Iterable<VehicleFilterContainer>> _getContainerItems(String search) async {
    try {
      var res = await RepositoryProvider.of<DioClient>(context).get(
        '/api/customer/autoComplete',
        queryParameters: {
          'column_name': 'container_number',
          'modal': 'containers',
          'ids': '',
          'content': '',
          'container_number': search
        },
      );

      return res['data'].map<VehicleFilterContainer>((item) => VehicleFilterContainer.fromMap(item)).toList();
    } catch (e) {
      return [];
    }
  }

  Future<Iterable<VehicleFilterPointOfLoading>> _getPointOfLoadingItems(String search) async {
    try {
      var res = await RepositoryProvider.of<DioClient>(context).get(
        '/api/customer/autoComplete',
        queryParameters: {
          'column_name': 'name',
          'modal': 'locations',
          'ids': '',
          'content': '',
          'container_number': search
        },
      );
      return res['data'].map<VehicleFilterPointOfLoading>((item) => VehicleFilterPointOfLoading.fromMap(item)).toList();
    } catch (e) {
      return [];
    }
  }

  Widget _idFilteringSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: AutoCompleteField<VehicleFilterContainer>(
            label: tr!.containerNo,
            icon: Symbols.tag,
            getItems: _getContainerItems,
            displayStringForOption: (VehicleFilterContainer option) {
              return option.containerNo.trim();
            },
            onChanged: (value) {
              setState(() {
                vehicleFilterData = vehicleFilterData.copyWith(filterContainer: value);
              });
            },
            setController: (cn) {
              containerController = cn;
            },
            initialValue: vehicleFilterData.filterContainer?.containerNo,
          ),
        ),
        const SizedBox(
          width: 8,
        ),
        Expanded(
          child: AutoCompleteField<VehicleFilterPointOfLoading>(
            label: tr!.pointOfLoading,
            icon: Symbols.add_location_alt,
            getItems: _getPointOfLoadingItems,
            displayStringForOption: (VehicleFilterPointOfLoading option) {
              return option.name.trim();
            },
            onChanged: (value) {
              setState(() {
                vehicleFilterData = vehicleFilterData.copyWith(filterPointOfLoading: value);
              });
            },
            setController: (cn) {
              containerController = cn;
            },
            initialValue: vehicleFilterData.filterContainer?.containerNo,
          ),
        ),
      ],
    );
  }

  List<Widget> _dataSection() {
    return [
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: SizedBox(
              height: 44,
              child: FilterTextField(
                label: tr!.lotNumber,
                textEditingController: lotNoController,
                icon: Symbols.tag,
              ),
            ),
          ),
          const SizedBox(
            width: 8,
          ),
          Expanded(
            child: SizedBox(
              height: 44,
              child: FilterTextField(
                label: tr!.vin,
                textEditingController: vinController,
                icon: Symbols.directions_car,
              ),
            ),
          ),
        ],
      ),
      const SizedBox(
        height: 16,
      ),
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: SizedBox(
              height: 44,
              child: FilterTextField(
                label: tr!.make,
                textEditingController: makeController,
                icon: Symbols.build,
              ),
            ),
          ),
          const SizedBox(
            width: 8,
          ),
          Expanded(
            child: SizedBox(
              height: 44,
              child: FilterTextField(
                label: tr!.model,
                textEditingController: modelController,
                icon: Symbols.directions_car,
              ),
            ),
          ),
        ],
      ),
      const SizedBox(
        height: 16,
      ),
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: YearField(
              onChanged: (text) {
                setState(() {
                  vehicleFilterData = vehicleFilterData.copyWith(year: text);
                });
              },
              setController: (cn) {
                yearController = cn;
              },
              initialValue: vehicleFilterData.year,
            ),
          ),
        ],
      ),
    ];
  }

  Widget _priceSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: SizedBox(
            height: 44,
            child: FilterTextField(
              label: tr!.start,
              textEditingController: priceStartController,
              icon: Symbols.attach_money,
              isNumber: true,
            ),
          ),
        ),
        const SizedBox(
          width: 8,
        ),
        Expanded(
          child: SizedBox(
            height: 44,
            child: FilterTextField(
              label: tr!.end,
              textEditingController: priceEndController,
              icon: Symbols.attach_money,
              isNumber: true,
            ),
          ),
        ),
      ],
    );
  }

  Widget _purchaseDateSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: SizedBox(
            height: 44,
            child: FilterTextField(
              label: tr!.fromDate,
              icon: Symbols.calendar_month,
              textEditingController: purchaseDateFromController,
              onTap: () async {
                DateTime? date = await showDatePicker(
                  context: context,
                  firstDate: DateTime(2015),
                  lastDate: vehicleFilterData.purchaseToDate ?? DateTime.now(),
                  initialDate: vehicleFilterData.purchaseFromDate,
                );
                if (date != null) {
                  purchaseDateFromController.text = DateFormat(cardsDateFormat).format(
                    date,
                  );
                  vehicleFilterData = vehicleFilterData.copyWith(purchaseFromDate: date);
                }
              },
              readOnly: true,
            ),
          ),
        ),
        const SizedBox(
          width: 8,
        ),
        Expanded(
          child: SizedBox(
            height: 44,
            child: FilterTextField(
              label: tr!.toDate,
              icon: Symbols.calendar_month,
              textEditingController: purchaseDateToController,
              onTap: () async {
                DateTime? date = await showDatePicker(
                  context: context,
                  firstDate: vehicleFilterData.purchaseFromDate ?? DateTime(2015),
                  lastDate: DateTime.now(),
                  initialDate: vehicleFilterData.purchaseToDate,
                );
                if (date != null) {
                  purchaseDateToController.text = DateFormat(cardsDateFormat).format(
                    date,
                  );
                  vehicleFilterData = vehicleFilterData.copyWith(purchaseToDate: date);
                }
              },
              readOnly: true,
            ),
          ),
        ),
      ],
    );
  }

  Widget _paymentDateSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: SizedBox(
            height: 44,
            child: FilterTextField(
              label: tr!.fromDate,
              icon: Symbols.calendar_month,
              textEditingController: paymentDateFromController,
              onTap: () async {
                DateTime? date = await showDatePicker(
                  context: context,
                  firstDate: DateTime(2015),
                  lastDate: vehicleFilterData.paymentToDate ?? DateTime.now(),
                  initialDate: vehicleFilterData.paymentFromDate,
                );
                if (date != null) {
                  paymentDateFromController.text = DateFormat(cardsDateFormat).format(
                    date,
                  );
                  vehicleFilterData = vehicleFilterData.copyWith(paymentFromDate: date);
                }
              },
              readOnly: true,
            ),
          ),
        ),
        const SizedBox(
          width: 8,
        ),
        Expanded(
          child: SizedBox(
            height: 44,
            child: FilterTextField(
              label: tr!.toDate,
              icon: Symbols.calendar_month,
              textEditingController: paymentDateToController,
              onTap: () async {
                DateTime? date = await showDatePicker(
                  context: context,
                  firstDate: vehicleFilterData.paymentFromDate ?? DateTime(2015),
                  lastDate: DateTime.now(),
                  initialDate: vehicleFilterData.paymentToDate,
                );
                if (date != null) {
                  paymentDateToController.text = DateFormat(cardsDateFormat).format(
                    date,
                  );
                  vehicleFilterData = vehicleFilterData.copyWith(paymentToDate: date);
                }
              },
              readOnly: true,
            ),
          ),
        ),
      ],
    );
  }

  Widget _deliveryDateSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: SizedBox(
            height: 44,
            child: FilterTextField(
              label: tr!.fromDate,
              icon: Symbols.calendar_month,
              textEditingController: deliverDateFromController,
              onTap: () async {
                DateTime? date = await showDatePicker(
                  context: context,
                  firstDate: DateTime(2015),
                  lastDate: vehicleFilterData.deliverToDate ?? DateTime.now(),
                  initialDate: vehicleFilterData.deliverFromDate,
                );
                if (date != null) {
                  deliverDateFromController.text = DateFormat(cardsDateFormat).format(
                    date,
                  );
                  vehicleFilterData = vehicleFilterData.copyWith(deliverFromDate: date);
                }
              },
              readOnly: true,
            ),
          ),
        ),
        const SizedBox(
          width: 8,
        ),
        Expanded(
          child: SizedBox(
            height: 44,
            child: FilterTextField(
              label: tr!.toDate,
              icon: Symbols.calendar_month,
              textEditingController: deliverDateToController,
              onTap: () async {
                DateTime? date = await showDatePicker(
                  context: context,
                  firstDate: vehicleFilterData.deliverFromDate ?? DateTime(2015),
                  lastDate: DateTime.now(),
                  initialDate: vehicleFilterData.deliverToDate,
                );
                if (date != null) {
                  deliverDateToController.text = DateFormat(cardsDateFormat).format(
                    date,
                  );
                  vehicleFilterData = vehicleFilterData.copyWith(deliverToDate: date);
                }
              },
              readOnly: true,
            ),
          ),
        ),
      ],
    );
  }

  Widget _statusSection() {
    return Wrap(
      spacing: 5.0,
      children: vehiclesSummaryItems
          .where((el) => el.value != 'total' && el.value != 'on_hand_with_load')
          .map<Widget>((SelectItem item) {
        return FilterChip.elevated(
          padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 6),
          labelPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 6),
          iconTheme: const IconThemeData(
            size: 10,
          ),
          showCheckmark: false,
          label: Text(item.label),
          selected: carState.contains(VehicleState.fromJson(item.value)),
          onSelected: (bool selected) {
            setState(() {
              if (selected) {
                carState.add(VehicleState.fromJson(item.value));
              } else {
                carState.remove(VehicleState.fromJson(item.value));
              }
              vehicleFilterData = vehicleFilterData.copyWith(carState: carState);
            });
          },
        );
      }).toList(),
    );
  }
}
