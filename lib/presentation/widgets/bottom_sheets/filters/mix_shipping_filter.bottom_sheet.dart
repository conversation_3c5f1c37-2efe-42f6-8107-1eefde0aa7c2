import 'package:dio_client/dio_client.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:pgl_mobile_app/constants/constants.dart';
import 'package:pgl_mobile_app/constants/date_formats.const.dart';
import 'package:pgl_mobile_app/exports/blocs.exports.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/logic/classes/select_item.class.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/bottom_sheet_style.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/widgets/filter/fields/auto_complete_field.widget.dart';
import 'package:pgl_mobile_app/presentation/widgets/filter/fields/fields.exports.dart';

class MixShippingFilterBottomSheet extends StatefulWidget {
  const MixShippingFilterBottomSheet({
    super.key,
  });

  @override
  State<MixShippingFilterBottomSheet> createState() => _MixShippingFilterBottomSheetState();
}

class _MixShippingFilterBottomSheetState extends State<MixShippingFilterBottomSheet> {
  late final AppLocalizations? tr = AppLocalizations.of(context);
  late List<SelectItem> invoicesSummaryItems = getInvoicesSummaryItems(context);

  late TextEditingController pointOfLoadingController;
  late TextEditingController containerController;
  late TextEditingController invoiceNoController;
  final TextEditingController issueDateFromController = TextEditingController();
  final TextEditingController issueDateToController = TextEditingController();

  final TextEditingController invoiceAmountStartController = TextEditingController();
  final TextEditingController invoiceAmountEndController = TextEditingController();
  final TextEditingController paymentReceivedStartController = TextEditingController();
  final TextEditingController paymentReceivedEndController = TextEditingController();

  final _formKey = GlobalKey<FormState>();

  late MixShippingFilterData invoiceFilterData;
  List<MixShippingStatus> status = [];

  @override
  void initState() {
    // intial values
    invoiceFilterData =
        context.read<MixShippingBloc>().state.mixShippingFilter.filterData ?? const MixShippingFilterData();
    status = [...invoiceFilterData.status];
    invoiceAmountStartController.text = invoiceFilterData.invoiceAmount?['min'] ?? '';
    invoiceAmountEndController.text = invoiceFilterData.invoiceAmount?['max'] ?? '';
    paymentReceivedStartController.text = invoiceFilterData.paymentReceived?['min'] ?? '';
    paymentReceivedEndController.text = invoiceFilterData.paymentReceived?['max'] ?? '';
    issueDateFromController.text = invoiceFilterData.issueFromDate != null
        ? DateFormat(cardsDateFormat).format(
            invoiceFilterData.issueFromDate ?? DateTime.now(),
          )
        : '';
    issueDateToController.text = invoiceFilterData.issueToDate != null
        ? DateFormat(cardsDateFormat).format(
            invoiceFilterData.issueToDate ?? DateTime.now(),
          )
        : '';

    //listeners
    invoiceAmountStartController.addListener(() {
      setState(() {
        if (invoiceFilterData.invoiceAmount?['max'] == null && invoiceAmountStartController.text == '') {
          invoiceFilterData = invoiceFilterData.copyWith(invoiceAmount: null);
        } else {
          invoiceFilterData = invoiceFilterData.copyWith(invoiceAmount: {
            if (invoiceFilterData.invoiceAmount?['max'] != null) 'max': invoiceFilterData.invoiceAmount?['max'],
            if (invoiceAmountStartController.text != '') 'min': invoiceAmountStartController.text,
          });
        }
      });
    });
    invoiceAmountEndController.addListener(() {
      setState(() {
        if (invoiceFilterData.invoiceAmount?['min'] == null && invoiceAmountEndController.text == '') {
          invoiceFilterData = invoiceFilterData.copyWith(invoiceAmount: null);
        } else {
          invoiceFilterData = invoiceFilterData.copyWith(invoiceAmount: {
            if (invoiceFilterData.invoiceAmount?['min'] != null) 'min': invoiceFilterData.invoiceAmount?['min'],
            if (invoiceAmountEndController.text != '') 'max': invoiceAmountEndController.text,
          });
        }
      });
    });
    paymentReceivedStartController.addListener(() {
      setState(() {
        if (invoiceFilterData.paymentReceived?['max'] == null && paymentReceivedStartController.text == '') {
          invoiceFilterData = invoiceFilterData.copyWith(paymentReceived: null);
        } else {
          invoiceFilterData = invoiceFilterData.copyWith(paymentReceived: {
            if (invoiceFilterData.paymentReceived?['max'] != null) 'max': invoiceFilterData.paymentReceived?['max'],
            if (paymentReceivedStartController.text != '') 'min': paymentReceivedStartController.text,
          });
        }
      });
    });
    paymentReceivedEndController.addListener(() {
      setState(() {
        if (invoiceFilterData.paymentReceived?['min'] == null && paymentReceivedEndController.text == '') {
          invoiceFilterData = invoiceFilterData.copyWith(paymentReceived: null);
        } else {
          invoiceFilterData = invoiceFilterData.copyWith(paymentReceived: {
            if (invoiceFilterData.paymentReceived?['min'] != null) 'min': invoiceFilterData.paymentReceived?['min'],
            if (paymentReceivedEndController.text != '') 'max': paymentReceivedEndController.text,
          });
        }
      });
    });

    super.initState();
  }

  void _clearFilterData() {
    setState(() {
      invoiceFilterData = const MixShippingFilterData();
      status = [];
      pointOfLoadingController.text = '';
      containerController.text = '';
      invoiceNoController.text = '';
      issueDateFromController.text = '';
      issueDateToController.text = '';
      invoiceAmountStartController.text = '';
      invoiceAmountEndController.text = '';
      paymentReceivedStartController.text = '';
      paymentReceivedEndController.text = '';
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MixShippingBloc, MixShippingState>(
      builder: (context, state) {
        return Form(
          key: _formKey,
          child: BottomSheetStyle(
            header: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      tr!.filter,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    SizedBox(
                      height: 32,
                      child: FilledButton(
                        style: const ButtonStyle(
                          padding: WidgetStatePropertyAll(
                            EdgeInsets.symmetric(horizontal: 12),
                          ),
                        ),
                        onPressed: () {
                          _clearFilterData();
                        },
                        child: Row(
                          children: [
                            const Icon(
                              Symbols.close,
                              size: 16,
                              weight: 800,
                            ),
                            const SizedBox(
                              width: 4,
                            ),
                            Text(
                              tr!.clear,
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                const Divider(),
              ],
            ),
            footer: Column(
              children: [
                const SizedBox(
                  height: 16,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    SizedBox(
                      height: 32,
                      child: TextButton(
                        style: const ButtonStyle(
                          padding: WidgetStatePropertyAll(
                            EdgeInsets.symmetric(horizontal: 12),
                          ),
                        ),
                        onPressed: () {
                          _clearFilterData();
                          Get.back();
                        },
                        child: Text(
                          tr!.cancel,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 8,
                    ),
                    SizedBox(
                      height: 32,
                      child: FilledButton(
                        style: const ButtonStyle(
                          padding: WidgetStatePropertyAll(
                            EdgeInsets.symmetric(horizontal: 12),
                          ),
                        ),
                        onPressed: () {
                          context.read<MixShippingBloc>().add(
                                MixShippingFilterChanged(
                                  mixShippingFilter: state.mixShippingFilter.copyWith(
                                    page: 1,
                                    filterData: invoiceFilterData,
                                  ),
                                ),
                              );
                          context.read<MixShippingBloc>().add(
                                MixShippingFetched(),
                              );
                          Get.back();
                        },
                        child: Text(
                          tr!.apply,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 32,
                ),
              ],
            ),
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 0),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _getTitle('${tr!.idFiltering} :'),
                      _idFilteringSection(),
                      _getTitle('${tr!.invoiceAmount} :'),
                      _invoiceAmountSection(),
                      _getTitle('${tr!.paymentReceived} :'),
                      _paymentReceivedSection(),
                      _getTitle('${tr!.issueDate} :'),
                      _purchaseDateSection(),
                      _getTitle('${tr!.status} :'),
                      _statusSection(),
                      const SizedBox(
                        height: 16,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _getTitle(String text) {
    return Column(
      children: [
        const SizedBox(
          height: 16,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              text,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: .8),
              ),
            ),
          ],
        ),
        const SizedBox(
          height: 16,
        ),
      ],
    );
  }

  Future<Iterable<MixShippingFilterContainer>> _getContainerItems(String search) async {
    try {
      var res = await RepositoryProvider.of<DioClient>(context).get(
        '/api/customer/autoComplete',
        queryParameters: {
          'column_name': 'container_number',
          'modal': 'containers',
          'ids': '',
          'content': '',
          'container_number': search
        },
      );
      return res['data'].map<MixShippingFilterContainer>((item) => MixShippingFilterContainer.fromMap(item)).toList();
    } catch (e) {
      return [];
    }
  }

  Future<Iterable<MixShippingFilterInvoiceNo>> _getInvoiceItems(String search) async {
    try {
      var res = await RepositoryProvider.of<DioClient>(context).get(
        '/api/customer/autoComplete',
        queryParameters: {
          'column_name': 'invoice_number',
          'modal': 'invoices',
          'ids': '',
          'content': '',
          'invoice_number': search
        },
      );
      return res['data'].map<MixShippingFilterInvoiceNo>((item) => MixShippingFilterInvoiceNo.fromMap(item)).toList();
    } catch (e) {
      return [];
    }
  }

  Widget _idFilteringSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: AutoCompleteField<MixShippingFilterInvoiceNo>(
            label: tr!.invoiceNo,
            icon: Symbols.tag,
            getItems: _getInvoiceItems,
            displayStringForOption: (MixShippingFilterInvoiceNo option) {
              return option.invoiceNo.trim();
            },
            onChanged: (value) {
              setState(() {
                invoiceFilterData = invoiceFilterData.copyWith(filterInvoiceNo: value);
              });
            },
            setController: (cn) {
              invoiceNoController = cn;
            },
            initialValue: invoiceFilterData.filterInvoiceNo?.invoiceNo,
          ),
        ),
        const SizedBox(
          width: 8,
        ),
        Expanded(
          child: AutoCompleteField<MixShippingFilterContainer>(
            label: tr!.containerNo,
            icon: Symbols.tag,
            getItems: _getContainerItems,
            displayStringForOption: (MixShippingFilterContainer option) {
              return option.containerNo.trim();
            },
            onChanged: (value) {
              setState(() {
                invoiceFilterData = invoiceFilterData.copyWith(filterContainer: value);
              });
            },
            setController: (cn) {
              containerController = cn;
            },
            initialValue: invoiceFilterData.filterContainer?.containerNo,
          ),
        ),
      ],
    );
  }

  Widget _invoiceAmountSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: SizedBox(
            height: 44,
            child: FilterTextField(
              label: tr!.start,
              textEditingController: invoiceAmountStartController,
              icon: Symbols.attach_money,
              isNumber: true,
            ),
          ),
        ),
        const SizedBox(
          width: 8,
        ),
        Expanded(
          child: SizedBox(
            height: 44,
            child: FilterTextField(
              label: tr!.end,
              textEditingController: invoiceAmountEndController,
              icon: Symbols.attach_money,
              isNumber: true,
            ),
          ),
        ),
      ],
    );
  }

  Widget _paymentReceivedSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: SizedBox(
            height: 44,
            child: FilterTextField(
              label: tr!.start,
              textEditingController: paymentReceivedStartController,
              icon: Symbols.attach_money,
              isNumber: true,
            ),
          ),
        ),
        const SizedBox(
          width: 8,
        ),
        Expanded(
          child: SizedBox(
            height: 44,
            child: FilterTextField(
              label: tr!.end,
              textEditingController: paymentReceivedEndController,
              icon: Symbols.attach_money,
              isNumber: true,
            ),
          ),
        ),
      ],
    );
  }

  Widget _purchaseDateSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: SizedBox(
            height: 44,
            child: FilterTextField(
              label: tr!.fromDate,
              icon: Symbols.calendar_month,
              textEditingController: issueDateFromController,
              onTap: () async {
                DateTime? date = await showDatePicker(
                  context: context,
                  firstDate: DateTime(2015),
                  lastDate: invoiceFilterData.issueToDate ?? DateTime.now(),
                  initialDate: invoiceFilterData.issueFromDate,
                );
                if (date != null) {
                  issueDateFromController.text = DateFormat(cardsDateFormat).format(
                    date,
                  );
                  invoiceFilterData = invoiceFilterData.copyWith(issueFromDate: date);
                }
              },
              readOnly: true,
            ),
          ),
        ),
        const SizedBox(
          width: 8,
        ),
        Expanded(
          child: SizedBox(
            height: 44,
            child: FilterTextField(
              label: tr!.toDate,
              icon: Symbols.calendar_month,
              textEditingController: issueDateToController,
              onTap: () async {
                DateTime? date = await showDatePicker(
                  context: context,
                  firstDate: invoiceFilterData.issueFromDate ?? DateTime(2015),
                  lastDate: DateTime.now(),
                  initialDate: invoiceFilterData.issueToDate,
                );
                if (date != null) {
                  issueDateToController.text = DateFormat(cardsDateFormat).format(
                    date,
                  );
                  invoiceFilterData = invoiceFilterData.copyWith(issueToDate: date);
                }
              },
              readOnly: true,
            ),
          ),
        ),
      ],
    );
  }

  Widget _statusSection() {
    return Wrap(
      spacing: 5.0,
      children: invoicesSummaryItems.where((el) => el.value != 'total').map<Widget>((SelectItem item) {
        return FilterChip.elevated(
          padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 6),
          labelPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 6),
          iconTheme: const IconThemeData(
            size: 10,
          ),
          showCheckmark: false,
          label: Text(item.label),
          selected: status.contains(MixShippingStatus.fromJson(item.value)),
          onSelected: (bool selected) {
            setState(() {
              if (selected) {
                status.add(MixShippingStatus.fromJson(item.value));
              } else {
                status.remove(MixShippingStatus.fromJson(item.value));
              }
              invoiceFilterData = invoiceFilterData.copyWith(status: status);
            });
          },
        );
      }).toList(),
    );
  }
}
