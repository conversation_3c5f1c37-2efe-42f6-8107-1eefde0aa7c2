import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/bottom_sheet_style2.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';
import 'package:pgl_mobile_app/presentation/widgets/tracking/tracking_screen_body.widget.dart';

class TrackingBottomSheet extends StatefulWidget {
  const TrackingBottomSheet({
    super.key,
    required this.query,
    this.isShipment = false,
  });

  final String query;
  final bool isShipment;

  @override
  State<TrackingBottomSheet> createState() => _TrackingBottomSheetState();
}

class _TrackingBottomSheetState extends State<TrackingBottomSheet> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return BottomSheetStyle2(
      children: [
        TrackingScreenBody(
          query: widget.query,
          hasPadding: false,
          isShipment: widget.isShipment,
        ),
        Container(
          height: 32,
        )
      ],
    );
  }
}
