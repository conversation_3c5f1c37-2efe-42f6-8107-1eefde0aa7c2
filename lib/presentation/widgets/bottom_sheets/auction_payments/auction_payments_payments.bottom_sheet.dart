import 'package:flutter/material.dart';
import 'package:pgl_mobile_app/exports/repositories.exports.dart';
import 'package:pgl_mobile_app/presentation/widgets/cards/auction_payments/auction_payments_payment_item.card.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/bottom_sheet_style2.widget.dart';
import 'package:pgl_mobile_app/l10n/app_localizations.dart';

class AuctionPaymentsPaymentsBottomSheet extends StatefulWidget {
  const AuctionPaymentsPaymentsBottomSheet({
    super.key,
    required this.payments,
  });

  final List<PaymentReceived<AuctionPaymentModel>> payments;

  @override
  State<AuctionPaymentsPaymentsBottomSheet> createState() => _AuctionPaymentsPaymentsBottomSheetState();
}

class _AuctionPaymentsPaymentsBottomSheetState extends State<AuctionPaymentsPaymentsBottomSheet> {
  late final AppLocalizations? tr = AppLocalizations.of(context);

  @override
  Widget build(BuildContext context) {
    return BottomSheetStyle2(
      children: [
        Text(
          tr!.invoiceApplied,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(
          height: 8,
        ),
        ...widget.payments.map(
          (PaymentReceived<AuctionPaymentModel> payment) => AuctionPaymentsPaymentItemCard(
            payment: payment,
          ),
        ),
        const SizedBox(
          height: 32,
        ),
      ],
    );
  }
}
