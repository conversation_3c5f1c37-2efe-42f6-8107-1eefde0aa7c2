// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart' show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyA2NmRn_XWSnOMq2ppDMNZhKdvkz8XgG2w',
    appId: '1:50537089118:android:dcff06e32cd4ddb5bfcccf',
    messagingSenderId: '50537089118',
    projectId: 'pgl-407619',
    storageBucket: 'pgl-407619.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBRqsWRzdjo09EpZqmkH-HFsPX0v79uzVo',
    appId: '1:50537089118:ios:6df6f0ceba9fdd84bfcccf',
    messagingSenderId: '50537089118',
    projectId: 'pgl-407619',
    storageBucket: 'pgl-407619.firebasestorage.app',
    androidClientId: '50537089118-50csnmqlepqde8tal36p5jv2ugvjnv8e.apps.googleusercontent.com',
    iosClientId: '50537089118-298ca8uvk13plljjq4c7mg8dtcff5cjb.apps.googleusercontent.com',
    iosBundleId: 'org.tonext.pgl',
  );

}