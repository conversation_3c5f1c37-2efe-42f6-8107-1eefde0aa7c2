import 'package:flutter/material.dart';
import 'package:get/route_manager.dart';
import 'package:pgl_mobile_app/constants/routes.const.dart';
import 'package:pgl_mobile_app/presentation/screens/screens.exports.dart';
import 'package:pgl_mobile_app/presentation/screens/settings/settings.screen.dart';
import 'package:pgl_mobile_app/presentation/screens/statements/statements_list.screen.dart';
import 'package:pgl_mobile_app/presentation/screens/tracking/tracking.screen.dart';

const Curve curve = Curves.easeInOut;

final pages = [
  GetPage(
    name: home,
    page: () => const HomeScreen(),
    curve: curve,
  ),
  GetPage(name: login, page: () => const LoginScreen()),
  GetPage(
    name: notifications,
    page: () => const NotificationsScreen(),
    curve: curve,
  ),
  GetPage(
    name: vehiclesList,
    page: () => const VehiclesListScreen(),
    curve: curve,
  ),
  GetPage(
    name: shipmentsList,
    page: () => const ShipmentsListScreen(),
    curve: curve,
  ),
  GetPage(
    name: invoicesList,
    page: () => const InvoicesListScreen(),
    curve: curve,
  ),
  GetPage(
    name: mixShippingList,
    page: () => const MixShippingListScreen(),
    curve: curve,
  ),
  GetPage(
    name: annoncements,
    page: () => const AnnouncementsScreen(),
    curve: curve,
  ),
  GetPage(
    name: imagePreview,
    page: () => const ImagesPreviewScreen(),
  ),
  GetPage(
    name: imageGallery,
    page: () => const ImageGalleryScreen(),
  ),
  GetPage(
    name: contactUs,
    page: () => const ContactUsScreen(),
    curve: curve,
  ),
  GetPage(
    name: splash,
    page: () => const SplashScreen(),
    curve: curve,
  ),
  GetPage(
    name: globalSearch,
    page: () => const GlobalSeachScreen(),
    curve: curve,
  ),
  GetPage(
    name: shippingRates,
    page: () => const ShippingRatesScreen(),
    curve: curve,
  ),
  GetPage(
    name: towingRates,
    page: () => const TowingRatesScreen(),
    curve: curve,
  ),
  GetPage(
    name: mixShippingCalculator,
    page: () => const MixShippingCalculator(),
    curve: curve,
  ),
  GetPage(
    name: mixShippingCalculatorTerms,
    page: () => const MixShippingCalculatorTerms(),
    curve: curve,
  ),
  GetPage(
    name: arrivalNoticeSingle,
    page: () => const ArrivalNoticeSingleScreen(),
    curve: curve,
  ),
  GetPage(
    name: statementsList,
    page: () => const StatementsListScreen(),
    curve: curve,
  ),
  GetPage(
    name: paymentsList,
    page: () => const PaymentsListScreen(),
    curve: curve,
  ),
  GetPage(
    name: auctionPayments,
    page: () => const AuctionPaymentsListScreen(),
    curve: curve,
  ),
  GetPage(
    name: settings,
    page: () => const SettingsScreen(),
    curve: curve,
  ),
  GetPage(
    name: tracking,
    page: () => const TrackingScreen(),
    curve: curve,
  ),
];
