import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pgl_mobile_app/configs/themes/colors.theme.dart';
import 'package:google_fonts/google_fonts.dart';

ThemeData getLightTheme(String locale) {
  ThemeData baseTheme = ThemeData(
    useMaterial3: true,
    colorScheme: lightColorScheme,
    dividerTheme: DividerThemeData(color: Colors.grey.withValues(alpha: .4)),
    appBarTheme: AppBarTheme(
      backgroundColor: lightColorScheme.primary,
      foregroundColor: lightColorScheme.onPrimary,
      centerTitle: true,
      titleSpacing: 0,
      titleTextStyle: getTextStyle(locale).copyWith(fontSize: 20, color: lightColorScheme.onPrimary),
      toolbarHeight: 64,
      systemOverlayStyle: SystemUiOverlayStyle(
        systemNavigationBarColor: lightColorScheme.surface,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    ),
    filledButtonTheme: FilledButtonThemeData(
      style: ButtonStyle(
        backgroundColor: WidgetStatePropertyAll(lightColorScheme.primary),
      ),
    ),
    navigationBarTheme: _navigationBarThemeData,
  );
  return baseTheme.copyWith(
    textTheme: getTextTheme(locale, baseTheme.textTheme),
  );
}

ThemeData getDarkTheme(String locale) {
  ThemeData baseTheme = ThemeData(
    useMaterial3: true,
    colorScheme: darkColorScheme,
    appBarTheme: AppBarTheme(
      backgroundColor: darkColorScheme.primary,
      foregroundColor: darkColorScheme.onPrimary,
      centerTitle: true,
      titleSpacing: 0,
      titleTextStyle: getTextStyle(locale).copyWith(
        fontSize: 20,
        color: darkColorScheme.onPrimary,
      ),
      toolbarHeight: 64,
      systemOverlayStyle: SystemUiOverlayStyle(
        systemNavigationBarColor: darkColorScheme.surface,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
    ),
    filledButtonTheme: FilledButtonThemeData(
      style: ButtonStyle(
        backgroundColor: WidgetStatePropertyAll(darkColorScheme.primary),
      ),
    ),
    navigationBarTheme: _navigationBarThemeData,
  );
  return baseTheme.copyWith(
    textTheme: getTextTheme(locale, baseTheme.textTheme),
  );
}

TextTheme getTextTheme(String locale, TextTheme textTheme) {
  switch (locale) {
    case 'en':
      return GoogleFonts.nunitoTextTheme(textTheme);
    case 'ar':
      return GoogleFonts.notoSansArabicTextTheme(textTheme);
    case 'ru':
      return GoogleFonts.notoSansTextTheme(textTheme);
    case 'ka':
      return GoogleFonts.notoSansGeorgianTextTheme(textTheme);
    default:
      return GoogleFonts.nunitoTextTheme(textTheme);
  }
}

TextStyle getTextStyle(String locale) {
  switch (locale) {
    case 'en':
      return GoogleFonts.nunito();
    case 'ar':
      return GoogleFonts.notoSansArabic();
    case 'ru':
      return GoogleFonts.notoSans();
    case 'ka':
      return GoogleFonts.notoSansGeorgian();
    default:
      return GoogleFonts.nunito();
  }
}

NavigationBarThemeData _navigationBarThemeData = const NavigationBarThemeData(
  labelTextStyle: WidgetStatePropertyAll(
    TextStyle(
      fontSize: 12,
      overflow: TextOverflow.ellipsis,
      fontWeight: FontWeight.w600,
    ),
  ),
);
