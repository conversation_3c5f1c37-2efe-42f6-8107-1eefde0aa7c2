import 'package:flutter/material.dart';

const lightColorScheme = ColorScheme(
  brightness: Brightness.light,
  primary: Color(0xFF22973A),
  onPrimary: Color(0xFFFFFFFF),
  primaryContainer: Color(0xFF89FD68),
  onPrimaryContainer: Color(0xFF032100),
  secondary: Color(0xFF54624D),
  onSecondary: Color(0xFFFFFFFF),
  secondaryContainer: Color(0xFFD8E7CC),
  onSecondaryContainer: Color(0xFF121F0E),
  tertiary: Color(0xFF386668),
  onTertiary: Color(0xFFFFFFFF),
  tertiaryContainer: Color(0xFFBCEBED),
  onTertiaryContainer: Color(0xFF002021),
  error: Color(0xFFBA1A1A),
  errorContainer: Color(0xFFFFDAD6),
  onError: Color(0xFFFFFFFF),
  onErrorContainer: Color(0xFF410002),
  outline: Color(0xFF73796E),
  onInverseSurface: Color(0xFFF1F1EA),
  inverseSurface: Color(0xFF2F312D),
  inversePrimary: Color(0xFF6EDF4E),
  shadow: Color(0xFF000000),
  surfaceTint: Color(0xFF176E00),
  outlineVariant: Color(0xFFC3C8BC),
  scrim: Color(0xFF000000),
  surface: Color(0xFFFAFAF3),
  onSurface: Color(0xFF1A1C18),
  surfaceContainerHighest: Color(0xFFDFE4D7),
  onSurfaceVariant: Color(0xFF43483F),
  primaryFixed: Color(0xff577f48),
  onPrimaryFixed: Color(0xffffffff),
  primaryFixedDim: Color(0xff3f6532),
  onPrimaryFixedVariant: Color(0xffffffff),
  secondaryFixed: Color(0xff6a7962),
  onSecondaryFixed: Color(0xffffffff),
  secondaryFixedDim: Color(0xff52604a),
  onSecondaryFixedVariant: Color(0xffffffff),
  tertiaryFixed: Color(0xff4f7c7e),
  onTertiaryFixed: Color(0xffffffff),
  tertiaryFixedDim: Color(0xff356365),
  onTertiaryFixedVariant: Color(0xffffffff),
  surfaceDim: Color(0xffd8dbd1),
  surfaceBright: Color(0xfff8faf0),
  surfaceContainerLowest: Color(0xffffffff),
  surfaceContainerLow: Color(0xfff2f5eb),
  surfaceContainer: Color(0xffecefe5),
  surfaceContainerHigh: Color(0xffe7e9df),
);

const darkColorScheme = ColorScheme(
  brightness: Brightness.dark,
  primary: Color(0xFF89FD68),
  onPrimary: Color(0xFF0F5300),
  primaryContainer: Color(0xFF0F5300),
  onPrimaryContainer: Color(0xFF89FD68),
  secondary: Color(0xFFBCCBB1),
  onSecondary: Color(0xFF273421),
  secondaryContainer: Color(0xFF3D4B36),
  onSecondaryContainer: Color(0xFFD8E7CC),
  tertiary: Color(0xFFA0CFD1),
  onTertiary: Color(0xFF003739),
  tertiaryContainer: Color(0xFF1E4E50),
  onTertiaryContainer: Color(0xFFBCEBED),
  error: Color(0xFFFFB4AB),
  errorContainer: Color(0xFF93000A),
  onError: Color(0xFF690005),
  onErrorContainer: Color(0xFFFFDAD6),
  outline: Color(0xFF8D9387),
  onInverseSurface: Color(0xFF1A1C18),
  inverseSurface: Color(0xFFE3E3DC),
  inversePrimary: Color(0xFF176E00),
  shadow: Color(0xFF000000),
  surfaceTint: Color(0xFF6EDF4E),
  outlineVariant: Color(0xFF43483F),
  scrim: Color(0xFF000000),
  surface: Color(0xFF121410),
  onSurface: Color(0xFFC6C7C1),
  surfaceContainerHighest: Color(0xFF43483F),
  onSurfaceVariant: Color(0xFFC3C8BC),
  primaryFixed: Color(0xff264b1b),
  onPrimaryFixed: Color(0xffffffff),
  primaryFixedDim: Color(0xff103406),
  onPrimaryFixedVariant: Color(0xffffffff),
  secondaryFixed: Color(0xff394733),
  onSecondaryFixed: Color(0xffffffff),
  secondaryFixedDim: Color(0xff23301e),
  onSecondaryFixedVariant: Color(0xffffffff),
  tertiaryFixed: Color(0xff194a4c),
  onTertiaryFixed: Color(0xffffffff),
  tertiaryFixedDim: Color(0xff003335),
  onTertiaryFixedVariant: Color(0xffffffff),
  surfaceDim: Color(0xffd8dbd1),
  surfaceBright: Color(0xfff8faf0),
  surfaceContainerLowest: Color(0xffffffff),
  surfaceContainerLow: Color(0xfff2f5eb),
  surfaceContainer: Color(0xffecefe5),
  surfaceContainerHigh: Color(0xffe7e9df),
);

const Color cardBgColorLight = Color(0xFFF4F4F4);
const Color cardBgColorDark = Color(0xFF202020);
const Color dashBoardRedColor = Color(0xFFBA1A1A);
const Color dashBoardPurpleColor = Color(0xFF9747FF);
const Color dashBoardYellowColor = Color(0xFFB19B24);
const Color dashBoardBlueColor = Color.fromARGB(255, 36, 74, 177);
const Color dashBoardPinkColor = Color.fromARGB(255, 247, 0, 255);
const Color dashBoardCyanColor = Color.fromARGB(255, 92, 187, 238);
