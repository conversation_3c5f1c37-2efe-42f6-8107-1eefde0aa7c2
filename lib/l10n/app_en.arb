{"@@locale": "en", "peaceGlobalLogistics": "Peace Global Logistics", "welcome": "Welcome", "copyRight": "Copyright © 2024 Peace Global Logistics", "emailOrUsername": "Email or username", "password": "Password", "forgotPasswordQ": "Forgot password?", "forgotPassword": "Forgot password", "forgotPasswordPrompt": "To reset your password, please contact the administrator for further assistance", "logIn": "Log in", "authenticationFailure": "Authentication Failure", "home": "Home", "vehicles": "Vehicles", "items": "Items", "shipments": "Shipments", "invoices": "Invoices", "mixShipping": "Mix Shipping", "vehicleDescription": "Click to show summary of your vehicles", "invoiceDescription": "Full container invoices", "profile": "Profile", "announcements": "Announcements", "settings": "Settings", "contactUs": "Contact Us", "customerComplains": "Customer Complains", "privacyPolicy": "Privacy Policy", "aboutUs": "About Us", "logout": "Log out", "logoutOfYourAccount": "Log out of your account?", "cancel": "Cancel", "close": "Close", "searchItem": "Search {item}", "@searchItem": {"placeholders": {"item": {"type": "String", "example": "Items"}}}, "all": "All", "auctionPaid": "Auction Paid", "auctionUnpaid": "Auction Unpaid", "onTheWay": "On The Way", "onHandWithTitle": "On Hand W/ Title", "onHandNoTitle": "On Hand No Title", "shipped": "Shipped", "auction": "Auction", "total": "Total", "atLoading": "At Loading", "arrived": "Arrived", "selectVehicleStatus": "Select Vehicle Status", "location": "Location", "open": "Open", "paid": "Paid", "due": "Due", "pastDue": "Past Due", "pastDueDays": "Past Due Days", "allInvoices": "All Invoices", "allVehicles": "All Vehicles", "allShipments": "All Shipments", "allMixShipping": "All Mix Shipping", "notifications": "Notifications", "images": "Images", "status": "Status", "year": "Year", "make": "Make", "model": "Model", "color": "Color", "keyPresent": "Key Present", "pointOfLoading": "Point Of Loading", "pointOfDischarge": "Point Of Discharge", "accountNumber": "Account Number", "auctionName": "Auction Name", "auctionCity": "Auction City", "autionInvLink": "Auction Inv Link", "view": "View", "ageAtPGLWH": "Age at PGL W/H", "purchasedDate": "Purchased Date", "reportDate": "Report Date", "paymentDate": "Payment Date", "towRequestDate": "Tow Request Date", "pickUpDate": "Pick Up Date", "pickUpDaysFromPurchase": "Pick Up Days From Purchase", "pickUpDaysFromReport": "Pick Up Days From Report", "deliverDate": "Delivery Date", "issueDate": "Issue Date", "dueDate": "Due Date", "eta": "ETA", "etd": "ETD", "titleState": "Title State", "titleStatus": "Title Status", "track": "Track", "units": "Units", "portOfLoading": "Port of Loading", "portOfDischarge": "Port of Discharge", "size": "Size", "loadingDate": "Loading Date", "download": "Download", "purpose": "Purpose", "amount": "Amount", "received": "Received", "amountDue": "Amount Due", "receivedDate": "Received Date", "vin": "Vin", "vins": "Vin(s)", "lotNumber": "Lot Number", "lotNumbers": "Lot Number(s)", "receivedDates": "Received Date(s)", "billOfLoading": "Bill Of Loading", "dockReceipt": "Dock Receipt", "clearanceInvoice": "Clearance Invoice", "eitherEmailOrUsername": "Either email or username", "passwordMustBeProvided": "Password must be provided", "itemMustBeProvided": "{name} must be provided", "@itemMustBeProvided": {"placeholders": {"name": {"type": "String"}}}, "itemMustBeAtLeastCountChars": "{name} must be at least {count} charachters", "@itemMustBeAtLeastCountChars": {"placeholders": {"name": {"type": "String"}, "count": {"type": "int"}}}, "invalidCreds": "Invalid credentials. Please check your email, username or password and try again", "unExpectedErrorOccured": "An unexpected error occurred. Please try again", "noConnectionInfoMightBeOutdated": "You are not connected!", "connectionReestablished": "Connection reestablished!", "noItemsFound": "No {item} found!", "@noItemsFound": {"placeholders": {"item": {"type": "String"}}}, "description": "Description", "photoLink": "Photos Link", "errorDownloadingFilePleaseTryAgain": "Error downloding file. Please try again.", "containerNo": "Container No", "bookingNo": "Booking No", "invoiceNo": "Invoice No", "invoiceAmount": "Invoice Amount", "paymentReceived": "Payment Received", "start": "Start", "end": "End", "countDays": "{count} Day(s)", "@countDays": {"placeholders": {"count": {"type": "int"}}}, "notAttached": "Not Attached", "shippingRates": "Shipping Rates", "complete": "Complete", "halfcut": "Half Cut", "hc40": "40 HC", "hc45": "45 HC", "mix": "Mix", "towing": "Towing", "sedanRate": "Sedan Rate", "sedanDismantleRate": "Sedan Dismantle Rate", "suvRate": "SUV Rate", "suvDismantleRate": "SUV Dismantle Rate", "companyType": "Company Type", "vehicleType": "Vehicle Type", "from": "From", "to": "To", "filter": "Filter", "clear": "Clear", "apply": "Apply", "effectiveFrom": "Effective From (ETD)", "tapBackAgainToLeave": "Tap back again to leave", "seeMore": "See more", "updateAvailable": "Update available!", "youAreUsingAnOlderVersion": "You are using an older version of this app, please update to continue.", "update": "Update", "idFiltering": "ID Filtering", "data": "Data", "price": "Price", "min": "Min", "max": "Max", "purchaseDate": "Purchase Date", "fromDate": "From Date", "toDate": "To Date", "contactText": "We can help. Our team of experts are on hand to answer your questions.", "name": "Name", "phone": "Phone", "email": "Email", "subject": "Subject", "message": "Message", "submit": "Submit", "ok": "OK", "success": "Success", "failure": "Failure", "contactMessageSuccess": "Your message was successfully send and we will get back to you soon", "contactMessageFailure": "Your message wasn''t send, there might be some error. Check your form and try again", "mixShippingCalculator": "Mix Shipping Calculator", "rateCalculator": "Rate Calculator", "state": "State", "branch": "Branch", "city": "City", "vehiclePrice": "Vehicle Price", "fullSizeSUVs": "Full size SUVs", "manheimAdesa": "Manheim Adesa", "majorAccident": "Major Accident", "priceOver10000AED": "Price Over 10000 AED", "mixShippingCalculationTerms": "For detailed information regarding terms and conditions as well as charges, please ", "clickHere": "click here", "calculate": "Calculate", "shippingRatesTerms": "Shipping Rates Terms", "note": "Note", "arrivalNotice": "Arrival Notice", "arrivalNotices": "Arrival Notices", "company": "Company", "downloadMixShippingRates": "Download Mix Shipping Rates", "statements": "Statements", "destination": "Destination", "reviewed": "Reviewed", "pending": "Pending", "canceled": "Canceled", "type": "Type", "debit": "Debit", "credit": "Credit", "paymentMethod": "Payment Method", "cash": "Cash", "bankTransfer": "Bank Transfer", "date": "Date", "failedToFetchItems": "Failed to fetch {item}!", "@failedToFetchItems": {"placeholders": {"item": {"type": "String"}}}, "notSupportedDestination": "Currently, we do not offer shipment services from the chosen city to the selected destination!", "selectLanguage": "Select Language", "language": "Language", "preview": "Preview", "titleReceiveDate": "Title Receive Date", "disclaimer": "Disclaimer", "anEstimateCustomDutyCharge": "A custom duty charge of about 10% of the vehicle price may apply", "vehicleAndContainerTracking": "Vehicle & Container Tracking", "vinLotContainerNumber": "VIN / Lot / Container Number", "enterYourVinNoLotNoOrContainerNoToTrackYourShipments": "Enter your Vin No, Lot No, or Container No to track your shipments.", "onTheWayToWarehouse": "On The Way To Warehouse", "onTheWayToDestination": "On The Way To Destination", "onHandWithLoad": "On Hand W/ Load", "atTheDock": "At The Dock", "arrival": "Arrival", "tracking": "Tracking", "vehicle": "Vehicle", "container": "Container", "towingRates": "Towing Rates", "payments": "Payments", "approved": "Approved", "shipment": "Shipment", "clearance": "Clearance", "clearLog": "Clear Log", "singleVcc": "Single VCC", "exitClaimCharge": "Exit Claim Charge", "deliveryCharge": "Delivery Charge", "detentionCharge": "Detention Charge", "amountApplied": "Amount Applied", "remainingAmount": "Remaining Amount", "remark": "Remark", "paymentAllocations": "Payment Allocations", "shipping": "Shipping", "title": "Title", "storageClr": "Storage Clearance", "storageDo": "Storage Delivery Order", "charge": "Charge", "other": "Other", "storage": "Storage", "dismantle": "Dismantle", "vatCustom": "VAT Custom", "freight": "Freight", "attestationFee": "Attestation Fee", "inspectionCharges": "Inspection Charges", "auctionStorage": "Auction Storage", "sharjahYardStorage": "Sharjah Yard Storage", "fedExOrMailingFee": "Fed Ex or Mailing Fee", "recoveryFee": "Recovery Fee", "customHold": "Custom Hold", "relistFee": "Relist <PERSON><PERSON>", "detentionCharges": "Detention Charges", "shortage": "Shortage", "suvCharges": "SUV Charges", "tdsCharges": "TDS Charges", "registrationFee": "Registration Fee", "transportationFee": "Transportation Fee", "officeFeeAndBank": "Office Fee and Bank", "emptyContainers": "Empty Containers", "previousPayment": "Previous Payment", "currency": "<PERSON><PERSON><PERSON><PERSON>", "wire": "Wire", "check": "Check", "mukhasa": "<PERSON><PERSON><PERSON>", "salesTax": "Sales tax", "damageCredit": "Damage credit", "demurrageCredit": "Demurrage credit", "storageCredit": "Storage credit", "exitPaperCredit": "Exit paper credit", "exchangeRate": "Exchange Rate", "auctionPayments": "Auction Payments", "balance": "Balance", "booking": "Booking", "tdsAmount": "TDS Amount", "invoiceApplied": "Invoice Applied", "freightPayments": "Freight Payments"}