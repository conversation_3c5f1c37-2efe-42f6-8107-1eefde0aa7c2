// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Russian (`ru`).
class AppLocalizationsRu extends AppLocalizations {
  AppLocalizationsRu([String locale = 'ru']) : super(locale);

  @override
  String get peaceGlobalLogistics => 'Peace Global Logistics';

  @override
  String get welcome => 'Добро пожаловать';

  @override
  String get copyRight => 'Авторское право © 2024 Peace Global Logistics';

  @override
  String get emailOrUsername => 'Электронная почта или имя пользователя';

  @override
  String get password => 'Пароль';

  @override
  String get forgotPasswordQ => 'Забыли пароль?';

  @override
  String get forgotPassword => 'Забыли пароль';

  @override
  String get forgotPasswordPrompt =>
      'Для сброса пароля свяжитесь с администратором для получения дополнительной помощи';

  @override
  String get logIn => 'Войти';

  @override
  String get authenticationFailure => 'Ошибка аутентификации';

  @override
  String get home => 'Главная';

  @override
  String get vehicles => 'Транспортные средства';

  @override
  String get items => 'Товары';

  @override
  String get shipments => 'Грузы';

  @override
  String get invoices => 'Счета';

  @override
  String get mixShipping => 'Смешанная перевозка';

  @override
  String get vehicleDescription =>
      'Нажмите, чтобы показать сводку ваших транспортных средств';

  @override
  String get invoiceDescription => 'Счета за полные контейнеры';

  @override
  String get profile => 'Профиль';

  @override
  String get announcements => 'Объявления';

  @override
  String get settings => 'Настройки';

  @override
  String get contactUs => 'Свяжитесь с нами';

  @override
  String get customerComplains => 'Жалобы клиентов';

  @override
  String get privacyPolicy => 'Политика конфиденциальности';

  @override
  String get aboutUs => 'О нас';

  @override
  String get logout => 'Выйти';

  @override
  String get logoutOfYourAccount => 'Выйти из вашей учетной записи?';

  @override
  String get cancel => 'Отмена';

  @override
  String get close => 'Закрыть';

  @override
  String searchItem(String item) {
    return 'Поиск $item';
  }

  @override
  String get all => 'Все';

  @override
  String get auctionPaid => 'Оплаченный аукцион';

  @override
  String get auctionUnpaid => 'Неоплаченный аукцион';

  @override
  String get onTheWay => 'В пути';

  @override
  String get onHandWithTitle => 'В наличии с титулом';

  @override
  String get onHandNoTitle => 'В наличии без титула';

  @override
  String get shipped => 'Отправлено';

  @override
  String get auction => 'Аукцион';

  @override
  String get total => 'Всего';

  @override
  String get atLoading => 'При загрузке';

  @override
  String get arrived => 'Прибыло';

  @override
  String get selectVehicleStatus => 'Выберите статус транспортного средства';

  @override
  String get location => 'Местоположение';

  @override
  String get open => 'Открыто';

  @override
  String get paid => 'Оплачено';

  @override
  String get due => 'До оплаты';

  @override
  String get pastDue => 'Просрочено';

  @override
  String get pastDueDays => 'Дни просрочки';

  @override
  String get allInvoices => 'Все счета';

  @override
  String get allVehicles => 'Все транспортные средства';

  @override
  String get allShipments => 'Все грузы';

  @override
  String get allMixShipping => 'Все смешанные перевозки';

  @override
  String get notifications => 'Уведомления';

  @override
  String get images => 'Изображения';

  @override
  String get status => 'Статус';

  @override
  String get year => 'Год';

  @override
  String get make => 'Марка';

  @override
  String get model => 'Модель';

  @override
  String get color => 'Цвет';

  @override
  String get keyPresent => 'Ключ присутствует';

  @override
  String get pointOfLoading => 'Пункт загрузки';

  @override
  String get pointOfDischarge => 'Пункт разгрузки';

  @override
  String get accountNumber => 'Номер счета';

  @override
  String get auctionName => 'Название аукциона';

  @override
  String get auctionCity => 'Город аукциона';

  @override
  String get autionInvLink => 'Ссылка на аукционный счет';

  @override
  String get view => 'Просмотр';

  @override
  String get ageAtPGLWH => 'Возраст на складе PGL';

  @override
  String get purchasedDate => 'Дата покупки';

  @override
  String get reportDate => 'Дата отчета';

  @override
  String get paymentDate => 'Дата оплаты';

  @override
  String get towRequestDate => 'Дата запроса буксировки';

  @override
  String get pickUpDate => 'Дата забора';

  @override
  String get pickUpDaysFromPurchase => 'Дни забора с момента покупки';

  @override
  String get pickUpDaysFromReport => 'Дни забора с момента отчета';

  @override
  String get deliverDate => 'Дата доставки';

  @override
  String get issueDate => 'Дата выпуска';

  @override
  String get dueDate => 'Срок оплаты';

  @override
  String get eta => 'ETA';

  @override
  String get etd => 'ETD';

  @override
  String get titleState => 'Состояние титула';

  @override
  String get titleStatus => 'Статус титула';

  @override
  String get track => 'Отслеживание';

  @override
  String get units => 'Единицы';

  @override
  String get portOfLoading => 'Порт загрузки';

  @override
  String get portOfDischarge => 'Порт разгрузки';

  @override
  String get size => 'Размер';

  @override
  String get loadingDate => 'Дата загрузки';

  @override
  String get download => 'Загрузить';

  @override
  String get purpose => 'Цель';

  @override
  String get amount => 'Сумма';

  @override
  String get received => 'Получено';

  @override
  String get amountDue => 'Сумма к оплате';

  @override
  String get receivedDate => 'Дата получения';

  @override
  String get vin => 'Vin';

  @override
  String get vins => 'Vin(s)';

  @override
  String get lotNumber => 'Номер лота';

  @override
  String get lotNumbers => 'Номера лотов';

  @override
  String get receivedDates => 'Даты получения';

  @override
  String get billOfLoading => 'Товарная накладная';

  @override
  String get dockReceipt => 'Квитанция о приеме на склад';

  @override
  String get clearanceInvoice => 'Таможенный счет';

  @override
  String get eitherEmailOrUsername =>
      'Либо электронная почта, либо имя пользователя';

  @override
  String get passwordMustBeProvided => 'Необходимо указать пароль';

  @override
  String itemMustBeProvided(String name) {
    return 'Необходимо указать $name';
  }

  @override
  String itemMustBeAtLeastCountChars(String name, int count) {
    return '$name должен быть длиной не менее $count символов';
  }

  @override
  String get invalidCreds =>
      'Неверные учетные данные. Пожалуйста, проверьте свою электронную почту, имя пользователя или пароль и попробуйте снова';

  @override
  String get unExpectedErrorOccured =>
      'Произошла непредвиденная ошибка. Пожалуйста, попробуйте снова';

  @override
  String get noConnectionInfoMightBeOutdated => 'Вы не подключены!';

  @override
  String get connectionReestablished => 'Соединение восстановлено!';

  @override
  String noItemsFound(String item) {
    return 'Не найдено $item!';
  }

  @override
  String get description => 'Описание';

  @override
  String get photoLink => 'Ссылка на фотографии';

  @override
  String get errorDownloadingFilePleaseTryAgain =>
      'Ошибка загрузки файла. Пожалуйста, попробуйте снова.';

  @override
  String get containerNo => 'Номер контейнера';

  @override
  String get bookingNo => 'Номер бронирования';

  @override
  String get invoiceNo => 'Номер счета';

  @override
  String get invoiceAmount => 'Сумма счета';

  @override
  String get paymentReceived => 'Получен платеж';

  @override
  String get start => 'Начало';

  @override
  String get end => 'Конец';

  @override
  String countDays(int count) {
    return '$count день(дней)';
  }

  @override
  String get notAttached => 'Не прикреплено';

  @override
  String get shippingRates => 'Тарифы на доставку';

  @override
  String get complete => 'Завершено';

  @override
  String get halfcut => 'Половина';

  @override
  String get hc40 => '40 HC';

  @override
  String get hc45 => '45 HC';

  @override
  String get mix => 'Смешанная';

  @override
  String get towing => 'Буксировка';

  @override
  String get sedanRate => 'Тариф на седан';

  @override
  String get sedanDismantleRate => 'Тариф на разборку седана';

  @override
  String get suvRate => 'Тариф на внедорожник';

  @override
  String get suvDismantleRate => 'Тариф на разборку внедорожника';

  @override
  String get companyType => 'Тип компании';

  @override
  String get vehicleType => 'Тип транспортного средства';

  @override
  String get from => 'От';

  @override
  String get to => 'До';

  @override
  String get filter => 'Фильтр';

  @override
  String get clear => 'Очистить';

  @override
  String get apply => 'Применить';

  @override
  String get effectiveFrom => 'Действует с (ETD)';

  @override
  String get tapBackAgainToLeave => 'Нажмите еще раз, чтобы выйти';

  @override
  String get seeMore => 'Показать больше';

  @override
  String get updateAvailable => 'Доступно обновление!';

  @override
  String get youAreUsingAnOlderVersion =>
      'Вы используете устаревшую версию приложения, пожалуйста, обновитесь для продолжения.';

  @override
  String get update => 'Обновить';

  @override
  String get idFiltering => 'Фильтрация по ID';

  @override
  String get data => 'Данные';

  @override
  String get price => 'Цена';

  @override
  String get min => 'Мин';

  @override
  String get max => 'Макс';

  @override
  String get purchaseDate => 'Дата покупки';

  @override
  String get fromDate => 'От даты';

  @override
  String get toDate => 'До даты';

  @override
  String get contactText =>
      'Мы можем помочь. Наша команда экспертов готова ответить на ваши вопросы.';

  @override
  String get name => 'Имя';

  @override
  String get phone => 'Телефон';

  @override
  String get email => 'Электронная почта';

  @override
  String get subject => 'Тема';

  @override
  String get message => 'Сообщение';

  @override
  String get submit => 'Отправить';

  @override
  String get ok => 'OK';

  @override
  String get success => 'Успех';

  @override
  String get failure => 'Сбой';

  @override
  String get contactMessageSuccess =>
      'Ваше сообщение успешно отправлено, мы свяжемся с вами в ближайшее время';

  @override
  String get contactMessageFailure =>
      'Ваше сообщение не отправлено, возможна ошибка. Проверьте форму и повторите попытку';

  @override
  String get mixShippingCalculator => 'Калькулятор смешанной перевозки';

  @override
  String get rateCalculator => 'Калькулятор тарифов';

  @override
  String get state => 'Штат';

  @override
  String get branch => 'Филиал';

  @override
  String get city => 'Город';

  @override
  String get vehiclePrice => 'Цена транспортного средства';

  @override
  String get fullSizeSUVs => 'Полноразмерные внедорожники';

  @override
  String get manheimAdesa => 'Manheim Adesa';

  @override
  String get majorAccident => 'Серьезное ДТП';

  @override
  String get priceOver10000AED => 'Цена более 10000 AED';

  @override
  String get mixShippingCalculationTerms =>
      'Для получения подробной информации о условиях и тарифах, пожалуйста, ';

  @override
  String get clickHere => 'нажмите здесь';

  @override
  String get calculate => 'Рассчитать';

  @override
  String get shippingRatesTerms => 'Условия тарифов на доставку';

  @override
  String get note => 'Примечание';

  @override
  String get arrivalNotice => 'Уведомление о прибытии';

  @override
  String get arrivalNotices => 'Уведомления о прибытии';

  @override
  String get company => 'Компания';

  @override
  String get downloadMixShippingRates =>
      'Загрузить тарифы на смешанную перевозку';

  @override
  String get statements => 'Отчеты';

  @override
  String get destination => 'Направление';

  @override
  String get reviewed => 'Рассмотрено';

  @override
  String get pending => 'В ожидании';

  @override
  String get canceled => 'Отменено';

  @override
  String get type => 'Тип';

  @override
  String get debit => 'Дебет';

  @override
  String get credit => 'Кредит';

  @override
  String get paymentMethod => 'Способ оплаты';

  @override
  String get cash => 'Наличные';

  @override
  String get bankTransfer => 'Банковский перевод';

  @override
  String get date => 'Дата';

  @override
  String failedToFetchItems(String item) {
    return 'Не удалось получить $item!';
  }

  @override
  String get notSupportedDestination =>
      'В настоящее время мы не предоставляем услуги по доставке из выбранного города в выбранное направление!';

  @override
  String get selectLanguage => 'Выберите язык';

  @override
  String get language => 'Язык';

  @override
  String get preview => 'Предварительный просмотр';

  @override
  String get titleReceiveDate => 'Дата получения титула';

  @override
  String get disclaimer => 'Отказ от ответственности';

  @override
  String get anEstimateCustomDutyCharge =>
      'Может взиматься таможенная пошлина в размере примерно 10% от стоимости транспортного средства.';

  @override
  String get vehicleAndContainerTracking =>
      'Отслеживание транспортных средств и контейнеров';

  @override
  String get vinLotContainerNumber => 'VIN / Номер партии / Контейнера';

  @override
  String get enterYourVinNoLotNoOrContainerNoToTrackYourShipments =>
      'Введите свой VIN-номер, номер лота или номер контейнера для отслеживания вашей грузовой партии.';

  @override
  String get onTheWayToWarehouse => 'В пути к складу';

  @override
  String get onTheWayToDestination => 'В пути к месту назначения';

  @override
  String get onHandWithLoad => 'Под рукой с грузом';

  @override
  String get atTheDock => 'На доке';

  @override
  String get arrival => 'Прибытие';

  @override
  String get tracking => 'Отслеживание';

  @override
  String get vehicle => 'Транспортное средство';

  @override
  String get container => 'Контейнер';

  @override
  String get towingRates => 'Тарифы на буксировку';

  @override
  String get payments => 'Платежи';

  @override
  String get approved => 'Одобренный';

  @override
  String get shipment => 'Отгрузка';

  @override
  String get clearance => 'Разрешение';

  @override
  String get clearLog => 'Очистить журнал';

  @override
  String get singleVcc => 'Одиночный VCC';

  @override
  String get exitClaimCharge => 'Плата за выход из заявления';

  @override
  String get deliveryCharge => 'Стоимость доставки';

  @override
  String get detentionCharge => 'Обвинение в задержании';

  @override
  String get amountApplied => 'Примененная сумма';

  @override
  String get remainingAmount => 'Оставшаяся сумма';

  @override
  String get remark => 'Замечание';

  @override
  String get paymentAllocations => 'Распределение платежей';

  @override
  String get shipping => 'Перевозки';

  @override
  String get title => 'Заголовок';

  @override
  String get storageClr => 'Освобождение от складских помещений';

  @override
  String get storageDo => 'Заказ на хранение и доставку';

  @override
  String get charge => 'Заряжать';

  @override
  String get other => 'Другой';

  @override
  String get storage => 'Хранилище';

  @override
  String get dismantle => 'Демонтировать';

  @override
  String get vatCustom => 'НДС таможенный';

  @override
  String get freight => 'Груз';

  @override
  String get attestationFee => 'Плата за подтверждение';

  @override
  String get inspectionCharges => 'Расходы на инспекцию';

  @override
  String get auctionStorage => 'Аукционное хранилище';

  @override
  String get sharjahYardStorage => 'Складское помещение в Шардже';

  @override
  String get fedExOrMailingFee => 'Fedex или почтовый сбор';

  @override
  String get recoveryFee => 'Плата за восстановление';

  @override
  String get customHold => 'Пользовательское удержание';

  @override
  String get relistFee => 'Плата за повторный листинг';

  @override
  String get detentionCharges => 'Обвинения в задержании';

  @override
  String get shortage => 'Нехватка';

  @override
  String get suvCharges => 'Сборы за внедорожники';

  @override
  String get tdsCharges => 'Сборы TDS';

  @override
  String get registrationFee => 'Регистрационный взнос';

  @override
  String get transportationFee => 'Плата за транспортировку';

  @override
  String get officeFeeAndBank => 'Офисный сбор и банк';

  @override
  String get emptyContainers => 'Пустые контейнеры';

  @override
  String get previousPayment => 'Предыдущий платеж';

  @override
  String get currency => 'Валюта';

  @override
  String get wire => 'Проволока';

  @override
  String get check => 'Проверять';

  @override
  String get mukhasa => 'Мухаса';

  @override
  String get salesTax => 'Налог с продаж';

  @override
  String get damageCredit => 'Кредит на ущерб';

  @override
  String get demurrageCredit => 'Кредит за простой';

  @override
  String get storageCredit => 'Кредит на хранение';

  @override
  String get exitPaperCredit => 'Выходной бумажный кредит';

  @override
  String get exchangeRate => 'Обменный курс';

  @override
  String get auctionPayments => 'Аукционные платежи';

  @override
  String get balance => 'Баланс';

  @override
  String get booking => 'Бронирование';

  @override
  String get tdsAmount => 'TDS Количество';

  @override
  String get invoiceApplied => 'применяется счет-фактура';

  @override
  String get freightPayments => 'Оплата фрахта';
}
