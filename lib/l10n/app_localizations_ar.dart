// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get peaceGlobalLogistics => 'لوجستيات السلام العالمية';

  @override
  String get welcome => 'مرحبًا';

  @override
  String get copyRight => 'حقوق النشر © 2024 لوجستيات السلام العالمية';

  @override
  String get emailOrUsername => 'البريد الإلكتروني أو اسم المستخدم';

  @override
  String get password => 'كلمة المرور';

  @override
  String get forgotPasswordQ => 'هل نسيت كلمة المرور؟';

  @override
  String get forgotPassword => 'نسيت كلمة المرور';

  @override
  String get forgotPasswordPrompt =>
      'لإعادة تعيين كلمة المرور الخاصة بك, يرجى الاتصال بالمسؤول للمساعدة الإضافية';

  @override
  String get logIn => 'تسجيل الدخول';

  @override
  String get authenticationFailure => 'فشل التوثيق';

  @override
  String get home => 'الرئيسية';

  @override
  String get vehicles => 'المركبات';

  @override
  String get items => 'العناصر';

  @override
  String get shipments => 'الشحنات';

  @override
  String get invoices => 'الفواتير';

  @override
  String get mixShipping => 'الشحن المختلط';

  @override
  String get vehicleDescription => 'انقر لعرض ملخص لمركباتك';

  @override
  String get invoiceDescription => 'فواتير حاوية كاملة';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get announcements => 'الإعلانات';

  @override
  String get settings => 'الإعدادات';

  @override
  String get contactUs => 'اتصل بنا';

  @override
  String get customerComplains => 'شكاوى العملاء';

  @override
  String get privacyPolicy => 'سياسة الخصوصية';

  @override
  String get aboutUs => 'معلومات عنا';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get logoutOfYourAccount => 'هل تريد تسجيل الخروج من حسابك؟';

  @override
  String get cancel => 'إلغاء';

  @override
  String get close => 'إغلاق';

  @override
  String searchItem(String item) {
    return 'البحث عن $item';
  }

  @override
  String get all => 'الكل';

  @override
  String get auctionPaid => 'المزاد المدفوع';

  @override
  String get auctionUnpaid => 'المزاد غير المدفوع';

  @override
  String get onTheWay => 'في الطريق';

  @override
  String get onHandWithTitle => 'موجود بالعنوان';

  @override
  String get onHandNoTitle => 'موجود بدون عنوان';

  @override
  String get shipped => 'تم الشحن';

  @override
  String get auction => 'المزاد';

  @override
  String get total => 'الإجمالي';

  @override
  String get atLoading => 'في التحميل';

  @override
  String get arrived => 'وصل';

  @override
  String get selectVehicleStatus => 'اختر حالة المركبة';

  @override
  String get location => 'الموقع';

  @override
  String get open => 'مفتوح';

  @override
  String get paid => 'مدفوع';

  @override
  String get due => 'مستحق';

  @override
  String get pastDue => 'متأخر عن الدفع';

  @override
  String get pastDueDays => 'أيام متأخرة';

  @override
  String get allInvoices => 'كل الفواتير';

  @override
  String get allVehicles => 'كل المركبات';

  @override
  String get allShipments => 'كل الشحنات';

  @override
  String get allMixShipping => 'كل الشحن المختلط';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get images => 'الصور';

  @override
  String get status => 'الحالة';

  @override
  String get year => 'السنة';

  @override
  String get make => 'الماركة';

  @override
  String get model => 'الطراز';

  @override
  String get color => 'اللون';

  @override
  String get keyPresent => 'المفتاح موجود';

  @override
  String get pointOfLoading => 'نقطة التحميل';

  @override
  String get pointOfDischarge => 'نقطة التفريغ';

  @override
  String get accountNumber => 'رقم الحساب';

  @override
  String get auctionName => 'اسم المزاد';

  @override
  String get auctionCity => 'مدينة المزاد';

  @override
  String get autionInvLink => 'رابط فاتورة المزاد';

  @override
  String get view => 'عرض';

  @override
  String get ageAtPGLWH => 'العمر في PGL W/H';

  @override
  String get purchasedDate => 'تاريخ الشراء';

  @override
  String get reportDate => 'تاريخ التقرير';

  @override
  String get paymentDate => 'تاريخ الدفع';

  @override
  String get towRequestDate => 'تاريخ طلب السحب';

  @override
  String get pickUpDate => 'تاريخ الاستلام';

  @override
  String get pickUpDaysFromPurchase => 'أيام الاستلام من التاريخ';

  @override
  String get pickUpDaysFromReport => 'أيام الاستلام من التقرير';

  @override
  String get deliverDate => 'تاريخ التسليم';

  @override
  String get issueDate => 'تاريخ الإصدار';

  @override
  String get dueDate => 'تاريخ الاستحقاق';

  @override
  String get eta => 'موعد الوصول المتوقع';

  @override
  String get etd => 'موعد المغادرة المتوقع';

  @override
  String get titleState => 'حالة العنوان';

  @override
  String get titleStatus => 'حالة العنوان';

  @override
  String get track => 'تتبع';

  @override
  String get units => 'وحدات';

  @override
  String get portOfLoading => 'ميناء التحميل';

  @override
  String get portOfDischarge => 'ميناء التفريغ';

  @override
  String get size => 'الحجم';

  @override
  String get loadingDate => 'تاريخ التحميل';

  @override
  String get download => 'تحميل';

  @override
  String get purpose => 'الغرض';

  @override
  String get amount => 'المبلغ';

  @override
  String get received => 'تم الاستلام';

  @override
  String get amountDue => 'المبلغ المستحق';

  @override
  String get receivedDate => 'تاريخ الاستلام';

  @override
  String get vin => 'رقم هيكل السيارة (VIN)';

  @override
  String get vins => 'أرقام هيكل السيارات (VIN)';

  @override
  String get lotNumber => 'رقم القطعة';

  @override
  String get lotNumbers => 'أرقام القطع';

  @override
  String get receivedDates => 'تواريخ الاستلام';

  @override
  String get billOfLoading => 'بوليصة الشحن';

  @override
  String get dockReceipt => 'وصل الحوض';

  @override
  String get clearanceInvoice => 'فاتورة التخليص';

  @override
  String get eitherEmailOrUsername => 'البريد الإلكتروني أو اسم المستخدم';

  @override
  String get passwordMustBeProvided => 'يجب توفير كلمة المرور';

  @override
  String itemMustBeProvided(String name) {
    return 'يجب تقديم $name';
  }

  @override
  String itemMustBeAtLeastCountChars(String name, int count) {
    return '$name يجب أن يحتوي على ما لا يقل عن $count أحرف';
  }

  @override
  String get invalidCreds =>
      'بيانات اعتماد غير صالحة. يرجى التحقق من البريد الإلكتروني, اسم المستخدم, أو كلمة المرور الخاصة بك والمحاولة مرة أخرى';

  @override
  String get unExpectedErrorOccured =>
      'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى';

  @override
  String get noConnectionInfoMightBeOutdated => 'أنت غير متصل!';

  @override
  String get connectionReestablished => 'تمت إعادة تأسيس الاتصال!';

  @override
  String noItemsFound(String item) {
    return 'لم يتم العثور على $item!';
  }

  @override
  String get description => 'الوصف';

  @override
  String get photoLink => 'رابط الصور';

  @override
  String get errorDownloadingFilePleaseTryAgain =>
      'حدث خطأ أثناء تنزيل الملف. يرجى المحاولة مرة أخرى.';

  @override
  String get containerNo => 'رقم الحاوية';

  @override
  String get bookingNo => 'رقم الحجز';

  @override
  String get invoiceNo => 'رقم الفاتورة';

  @override
  String get invoiceAmount => 'مبلغ الفاتورة';

  @override
  String get paymentReceived => 'المبلغ المستلم';

  @override
  String get start => 'البداية';

  @override
  String get end => 'النهاية';

  @override
  String countDays(int count) {
    return '$count يوم(أيام)';
  }

  @override
  String get notAttached => 'غير مرفق';

  @override
  String get shippingRates => 'أسعار الشحن';

  @override
  String get complete => 'كامل';

  @override
  String get halfcut => 'نصف القطع';

  @override
  String get hc40 => '40 قدم مكعب';

  @override
  String get hc45 => '45 قدم مكعب';

  @override
  String get mix => 'مزيج';

  @override
  String get towing => 'سحب';

  @override
  String get sedanRate => 'سعر السيدان';

  @override
  String get sedanDismantleRate => 'سعر فك السيدان';

  @override
  String get suvRate => 'سعر الدفع الرباعي';

  @override
  String get suvDismantleRate => 'سعر فك الدفع الرباعي';

  @override
  String get companyType => 'نوع الشركة';

  @override
  String get vehicleType => 'نوع المركبة';

  @override
  String get from => 'من';

  @override
  String get to => 'إلى';

  @override
  String get filter => 'تصفية';

  @override
  String get clear => 'مسح';

  @override
  String get apply => 'تطبيق';

  @override
  String get effectiveFrom => 'ساري المفعول من تاريخ (ETD)';

  @override
  String get tapBackAgainToLeave => 'اضغط مرة أخرى للمغادرة';

  @override
  String get seeMore => 'رؤية المزيد';

  @override
  String get updateAvailable => 'تحديث متاح!';

  @override
  String get youAreUsingAnOlderVersion =>
      'أنت تستخدم نسخة قديمة من هذا التطبيق, يرجى التحديث للمتابعة.';

  @override
  String get update => 'تحديث';

  @override
  String get idFiltering => 'تصفية الهوية';

  @override
  String get data => 'البيانات';

  @override
  String get price => 'السعر';

  @override
  String get min => 'الحد الأدنى';

  @override
  String get max => 'الحد الأقصى';

  @override
  String get purchaseDate => 'تاريخ الشراء';

  @override
  String get fromDate => 'من التاريخ';

  @override
  String get toDate => 'إلى التاريخ';

  @override
  String get contactText =>
      'نحن هنا لمساعدتك. فريقنا من الخبراء متواجد للإجابة على أسئلتك.';

  @override
  String get name => 'الاسم';

  @override
  String get phone => 'الهاتف';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get subject => 'الموضوع';

  @override
  String get message => 'الرسالة';

  @override
  String get submit => 'إرسال';

  @override
  String get ok => 'موافق';

  @override
  String get success => 'نجاح';

  @override
  String get failure => 'فشل';

  @override
  String get contactMessageSuccess =>
      'تم إرسال رسالتك بنجاح وسنقوم بالرد عليك قريبًا';

  @override
  String get contactMessageFailure =>
      'لم يتم إرسال رسالتك, قد تكون هناك بعض الأخطاء. يرجى التحقق من النموذج الخاص بك والمحاولة مرة أخرى';

  @override
  String get mixShippingCalculator => 'آلة حاسبة الشحن المختلط';

  @override
  String get rateCalculator => 'آلة حاسبة الأسعار';

  @override
  String get state => 'الولاية';

  @override
  String get branch => 'الفرع';

  @override
  String get city => 'المدينة';

  @override
  String get vehiclePrice => 'سعر المركبة';

  @override
  String get fullSizeSUVs => 'الدفع الرباعي بحجم كامل';

  @override
  String get manheimAdesa => 'مانهايم أديسا';

  @override
  String get majorAccident => 'حادث كبير';

  @override
  String get priceOver10000AED => 'السعر أكثر من 10000 درهم';

  @override
  String get mixShippingCalculationTerms =>
      'للحصول على معلومات مفصلة حول الشروط والأحكام والرسوم, يرجى ';

  @override
  String get clickHere => 'النقر هنا';

  @override
  String get calculate => 'احسب';

  @override
  String get shippingRatesTerms => 'شروط أسعار الشحن';

  @override
  String get note => 'ملاحظة';

  @override
  String get arrivalNotice => 'إشعار الوصول';

  @override
  String get arrivalNotices => 'إشعارات الوصول';

  @override
  String get company => 'الشركة';

  @override
  String get downloadMixShippingRates => 'تحميل أسعار الشحن المختلط';

  @override
  String get statements => 'بيانات';

  @override
  String get destination => 'الوجهة';

  @override
  String get reviewed => 'تمت المراجعة';

  @override
  String get pending => 'قيد الانتظار';

  @override
  String get canceled => 'ملغاة';

  @override
  String get type => 'النوع';

  @override
  String get debit => 'مدين';

  @override
  String get credit => 'ائتمان';

  @override
  String get paymentMethod => 'طريقة الدفع';

  @override
  String get cash => 'نقداً';

  @override
  String get bankTransfer => 'تحويل بنكي';

  @override
  String get date => 'التاريخ';

  @override
  String failedToFetchItems(String item) {
    return 'فشل في جلب $item!';
  }

  @override
  String get notSupportedDestination =>
      'حاليا, لا نقدم خدمات الشحن من المدينة المختارة إلى الوجهة المحددة!';

  @override
  String get selectLanguage => 'اختر اللغة';

  @override
  String get language => 'اللغة';

  @override
  String get preview => 'معاينة';

  @override
  String get titleReceiveDate => 'تاريخ استلام العنوان';

  @override
  String get disclaimer => 'تنصل';

  @override
  String get anEstimateCustomDutyCharge =>
      'قد يتم فرض رسوم جمركية تبلغ حوالي ۱۰٪ من سعر السيارة';

  @override
  String get vehicleAndContainerTracking => 'تتبع المركبات والحاويات';

  @override
  String get vinLotContainerNumber => 'رقم VIN/الدفعة/الحاوية';

  @override
  String get enterYourVinNoLotNoOrContainerNoToTrackYourShipments =>
      'أدخل رقم VIN الخاص بك أو رقم الكمية أو رقم الحاوية لتتبع شحناتك.';

  @override
  String get onTheWayToWarehouse => 'في الطريق إلى المستودع';

  @override
  String get onTheWayToDestination => 'في الطريق إلى الوجهة';

  @override
  String get onHandWithLoad => 'في متناول اليد, مع الحمل';

  @override
  String get atTheDock => 'في المرسى';

  @override
  String get arrival => 'الوصول';

  @override
  String get tracking => 'التتبع';

  @override
  String get vehicle => 'مركبة';

  @override
  String get container => 'الحاوية';

  @override
  String get towingRates => 'أسعار السحب';

  @override
  String get payments => 'المدفوعات';

  @override
  String get approved => 'موافقة';

  @override
  String get shipment => 'شحنة';

  @override
  String get clearance => 'تصريح';

  @override
  String get clearLog => 'مسح السجل';

  @override
  String get singleVcc => 'بطاقة VCC واحدة';

  @override
  String get exitClaimCharge => 'رسوم المطالبة بالخروج';

  @override
  String get deliveryCharge => 'رسوم التوصيل';

  @override
  String get detentionCharge => 'تهمة الاحتجاز';

  @override
  String get amountApplied => 'المبلغ المطبق';

  @override
  String get remainingAmount => 'المبلغ المتبقي';

  @override
  String get remark => 'ملاحظة';

  @override
  String get paymentAllocations => 'تخصيصات الدفع';

  @override
  String get shipping => 'شحن';

  @override
  String get title => 'عنوان';

  @override
  String get storageClr => 'إخلاء التخزين';

  @override
  String get storageDo => 'طلب توصيل التخزين';

  @override
  String get charge => 'تكلفة';

  @override
  String get other => 'آخر';

  @override
  String get storage => 'تخزين';

  @override
  String get dismantle => 'تفكيك';

  @override
  String get vatCustom => 'ضريبة القيمة المضافة الجمركية';

  @override
  String get freight => 'الشحن';

  @override
  String get attestationFee => 'رسوم التصديق';

  @override
  String get inspectionCharges => 'رسوم التفتيش';

  @override
  String get auctionStorage => 'تخزين المزاد';

  @override
  String get sharjahYardStorage => 'Sharjah Yard Storage';

  @override
  String get fedExOrMailingFee => 'رسوم FedEx أو البريد';

  @override
  String get recoveryFee => 'رسوم الاسترداد';

  @override
  String get customHold => 'Custom Hold';

  @override
  String get relistFee => 'رسوم إعادة الإدراج';

  @override
  String get detentionCharges => 'رسوم الاحتجاز';

  @override
  String get shortage => 'Shortage';

  @override
  String get suvCharges => 'رسوم SUV';

  @override
  String get tdsCharges => 'رسوم TDS';

  @override
  String get registrationFee => 'رسوم التسجيل';

  @override
  String get transportationFee => 'رسوم النقل';

  @override
  String get officeFeeAndBank => 'رسوم المكتب والبنك';

  @override
  String get emptyContainers => 'الحاويات الفارغة';

  @override
  String get previousPayment => 'الدفعة السابقة';

  @override
  String get currency => 'العملة';

  @override
  String get wire => 'التحويل البنكي';

  @override
  String get check => 'الشيك';

  @override
  String get mukhasa => 'Mukhasa';

  @override
  String get salesTax => 'ضريبة المبيعات';

  @override
  String get damageCredit => 'ائتمان الأضرار';

  @override
  String get demurrageCredit => 'ائتمان التأخير';

  @override
  String get storageCredit => 'ائتمان التخزين';

  @override
  String get exitPaperCredit => 'ائتمان الخروج الورقي';

  @override
  String get exchangeRate => 'سعر الصرف';

  @override
  String get auctionPayments => 'مدفوعات المزاد';

  @override
  String get balance => 'الرصيد';

  @override
  String get booking => 'الحجز';

  @override
  String get tdsAmount => 'مبلغ ضريبة الخصم عند المصدر';

  @override
  String get invoiceApplied => 'تم تطبيق الفاتورة';

  @override
  String get freightPayments => 'دفع الشحن';
}
