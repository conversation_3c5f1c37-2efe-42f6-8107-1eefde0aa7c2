// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get peaceGlobalLogistics => 'Peace Global Logistics';

  @override
  String get welcome => 'Welcome';

  @override
  String get copyRight => 'Copyright © 2024 Peace Global Logistics';

  @override
  String get emailOrUsername => 'Email or username';

  @override
  String get password => 'Password';

  @override
  String get forgotPasswordQ => 'Forgot password?';

  @override
  String get forgotPassword => 'Forgot password';

  @override
  String get forgotPasswordPrompt =>
      'To reset your password, please contact the administrator for further assistance';

  @override
  String get logIn => 'Log in';

  @override
  String get authenticationFailure => 'Authentication Failure';

  @override
  String get home => 'Home';

  @override
  String get vehicles => 'Vehicles';

  @override
  String get items => 'Items';

  @override
  String get shipments => 'Shipments';

  @override
  String get invoices => 'Invoices';

  @override
  String get mixShipping => 'Mix Shipping';

  @override
  String get vehicleDescription => 'Click to show summary of your vehicles';

  @override
  String get invoiceDescription => 'Full container invoices';

  @override
  String get profile => 'Profile';

  @override
  String get announcements => 'Announcements';

  @override
  String get settings => 'Settings';

  @override
  String get contactUs => 'Contact Us';

  @override
  String get customerComplains => 'Customer Complains';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get aboutUs => 'About Us';

  @override
  String get logout => 'Log out';

  @override
  String get logoutOfYourAccount => 'Log out of your account?';

  @override
  String get cancel => 'Cancel';

  @override
  String get close => 'Close';

  @override
  String searchItem(String item) {
    return 'Search $item';
  }

  @override
  String get all => 'All';

  @override
  String get auctionPaid => 'Auction Paid';

  @override
  String get auctionUnpaid => 'Auction Unpaid';

  @override
  String get onTheWay => 'On The Way';

  @override
  String get onHandWithTitle => 'On Hand W/ Title';

  @override
  String get onHandNoTitle => 'On Hand No Title';

  @override
  String get shipped => 'Shipped';

  @override
  String get auction => 'Auction';

  @override
  String get total => 'Total';

  @override
  String get atLoading => 'At Loading';

  @override
  String get arrived => 'Arrived';

  @override
  String get selectVehicleStatus => 'Select Vehicle Status';

  @override
  String get location => 'Location';

  @override
  String get open => 'Open';

  @override
  String get paid => 'Paid';

  @override
  String get due => 'Due';

  @override
  String get pastDue => 'Past Due';

  @override
  String get pastDueDays => 'Past Due Days';

  @override
  String get allInvoices => 'All Invoices';

  @override
  String get allVehicles => 'All Vehicles';

  @override
  String get allShipments => 'All Shipments';

  @override
  String get allMixShipping => 'All Mix Shipping';

  @override
  String get notifications => 'Notifications';

  @override
  String get images => 'Images';

  @override
  String get status => 'Status';

  @override
  String get year => 'Year';

  @override
  String get make => 'Make';

  @override
  String get model => 'Model';

  @override
  String get color => 'Color';

  @override
  String get keyPresent => 'Key Present';

  @override
  String get pointOfLoading => 'Point Of Loading';

  @override
  String get pointOfDischarge => 'Point Of Discharge';

  @override
  String get accountNumber => 'Account Number';

  @override
  String get auctionName => 'Auction Name';

  @override
  String get auctionCity => 'Auction City';

  @override
  String get autionInvLink => 'Auction Inv Link';

  @override
  String get view => 'View';

  @override
  String get ageAtPGLWH => 'Age at PGL W/H';

  @override
  String get purchasedDate => 'Purchased Date';

  @override
  String get reportDate => 'Report Date';

  @override
  String get paymentDate => 'Payment Date';

  @override
  String get towRequestDate => 'Tow Request Date';

  @override
  String get pickUpDate => 'Pick Up Date';

  @override
  String get pickUpDaysFromPurchase => 'Pick Up Days From Purchase';

  @override
  String get pickUpDaysFromReport => 'Pick Up Days From Report';

  @override
  String get deliverDate => 'Delivery Date';

  @override
  String get issueDate => 'Issue Date';

  @override
  String get dueDate => 'Due Date';

  @override
  String get eta => 'ETA';

  @override
  String get etd => 'ETD';

  @override
  String get titleState => 'Title State';

  @override
  String get titleStatus => 'Title Status';

  @override
  String get track => 'Track';

  @override
  String get units => 'Units';

  @override
  String get portOfLoading => 'Port of Loading';

  @override
  String get portOfDischarge => 'Port of Discharge';

  @override
  String get size => 'Size';

  @override
  String get loadingDate => 'Loading Date';

  @override
  String get download => 'Download';

  @override
  String get purpose => 'Purpose';

  @override
  String get amount => 'Amount';

  @override
  String get received => 'Received';

  @override
  String get amountDue => 'Amount Due';

  @override
  String get receivedDate => 'Received Date';

  @override
  String get vin => 'Vin';

  @override
  String get vins => 'Vin(s)';

  @override
  String get lotNumber => 'Lot Number';

  @override
  String get lotNumbers => 'Lot Number(s)';

  @override
  String get receivedDates => 'Received Date(s)';

  @override
  String get billOfLoading => 'Bill Of Loading';

  @override
  String get dockReceipt => 'Dock Receipt';

  @override
  String get clearanceInvoice => 'Clearance Invoice';

  @override
  String get eitherEmailOrUsername => 'Either email or username';

  @override
  String get passwordMustBeProvided => 'Password must be provided';

  @override
  String itemMustBeProvided(String name) {
    return '$name must be provided';
  }

  @override
  String itemMustBeAtLeastCountChars(String name, int count) {
    return '$name must be at least $count charachters';
  }

  @override
  String get invalidCreds =>
      'Invalid credentials. Please check your email, username or password and try again';

  @override
  String get unExpectedErrorOccured =>
      'An unexpected error occurred. Please try again';

  @override
  String get noConnectionInfoMightBeOutdated => 'You are not connected!';

  @override
  String get connectionReestablished => 'Connection reestablished!';

  @override
  String noItemsFound(String item) {
    return 'No $item found!';
  }

  @override
  String get description => 'Description';

  @override
  String get photoLink => 'Photos Link';

  @override
  String get errorDownloadingFilePleaseTryAgain =>
      'Error downloding file. Please try again.';

  @override
  String get containerNo => 'Container No';

  @override
  String get bookingNo => 'Booking No';

  @override
  String get invoiceNo => 'Invoice No';

  @override
  String get invoiceAmount => 'Invoice Amount';

  @override
  String get paymentReceived => 'Payment Received';

  @override
  String get start => 'Start';

  @override
  String get end => 'End';

  @override
  String countDays(int count) {
    return '$count Day(s)';
  }

  @override
  String get notAttached => 'Not Attached';

  @override
  String get shippingRates => 'Shipping Rates';

  @override
  String get complete => 'Complete';

  @override
  String get halfcut => 'Half Cut';

  @override
  String get hc40 => '40 HC';

  @override
  String get hc45 => '45 HC';

  @override
  String get mix => 'Mix';

  @override
  String get towing => 'Towing';

  @override
  String get sedanRate => 'Sedan Rate';

  @override
  String get sedanDismantleRate => 'Sedan Dismantle Rate';

  @override
  String get suvRate => 'SUV Rate';

  @override
  String get suvDismantleRate => 'SUV Dismantle Rate';

  @override
  String get companyType => 'Company Type';

  @override
  String get vehicleType => 'Vehicle Type';

  @override
  String get from => 'From';

  @override
  String get to => 'To';

  @override
  String get filter => 'Filter';

  @override
  String get clear => 'Clear';

  @override
  String get apply => 'Apply';

  @override
  String get effectiveFrom => 'Effective From (ETD)';

  @override
  String get tapBackAgainToLeave => 'Tap back again to leave';

  @override
  String get seeMore => 'See more';

  @override
  String get updateAvailable => 'Update available!';

  @override
  String get youAreUsingAnOlderVersion =>
      'You are using an older version of this app, please update to continue.';

  @override
  String get update => 'Update';

  @override
  String get idFiltering => 'ID Filtering';

  @override
  String get data => 'Data';

  @override
  String get price => 'Price';

  @override
  String get min => 'Min';

  @override
  String get max => 'Max';

  @override
  String get purchaseDate => 'Purchase Date';

  @override
  String get fromDate => 'From Date';

  @override
  String get toDate => 'To Date';

  @override
  String get contactText =>
      'We can help. Our team of experts are on hand to answer your questions.';

  @override
  String get name => 'Name';

  @override
  String get phone => 'Phone';

  @override
  String get email => 'Email';

  @override
  String get subject => 'Subject';

  @override
  String get message => 'Message';

  @override
  String get submit => 'Submit';

  @override
  String get ok => 'OK';

  @override
  String get success => 'Success';

  @override
  String get failure => 'Failure';

  @override
  String get contactMessageSuccess =>
      'Your message was successfully send and we will get back to you soon';

  @override
  String get contactMessageFailure =>
      'Your message wasn\'\'t send, there might be some error. Check your form and try again';

  @override
  String get mixShippingCalculator => 'Mix Shipping Calculator';

  @override
  String get rateCalculator => 'Rate Calculator';

  @override
  String get state => 'State';

  @override
  String get branch => 'Branch';

  @override
  String get city => 'City';

  @override
  String get vehiclePrice => 'Vehicle Price';

  @override
  String get fullSizeSUVs => 'Full size SUVs';

  @override
  String get manheimAdesa => 'Manheim Adesa';

  @override
  String get majorAccident => 'Major Accident';

  @override
  String get priceOver10000AED => 'Price Over 10000 AED';

  @override
  String get mixShippingCalculationTerms =>
      'For detailed information regarding terms and conditions as well as charges, please ';

  @override
  String get clickHere => 'click here';

  @override
  String get calculate => 'Calculate';

  @override
  String get shippingRatesTerms => 'Shipping Rates Terms';

  @override
  String get note => 'Note';

  @override
  String get arrivalNotice => 'Arrival Notice';

  @override
  String get arrivalNotices => 'Arrival Notices';

  @override
  String get company => 'Company';

  @override
  String get downloadMixShippingRates => 'Download Mix Shipping Rates';

  @override
  String get statements => 'Statements';

  @override
  String get destination => 'Destination';

  @override
  String get reviewed => 'Reviewed';

  @override
  String get pending => 'Pending';

  @override
  String get canceled => 'Canceled';

  @override
  String get type => 'Type';

  @override
  String get debit => 'Debit';

  @override
  String get credit => 'Credit';

  @override
  String get paymentMethod => 'Payment Method';

  @override
  String get cash => 'Cash';

  @override
  String get bankTransfer => 'Bank Transfer';

  @override
  String get date => 'Date';

  @override
  String failedToFetchItems(String item) {
    return 'Failed to fetch $item!';
  }

  @override
  String get notSupportedDestination =>
      'Currently, we do not offer shipment services from the chosen city to the selected destination!';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get language => 'Language';

  @override
  String get preview => 'Preview';

  @override
  String get titleReceiveDate => 'Title Receive Date';

  @override
  String get disclaimer => 'Disclaimer';

  @override
  String get anEstimateCustomDutyCharge =>
      'A custom duty charge of about 10% of the vehicle price may apply';

  @override
  String get vehicleAndContainerTracking => 'Vehicle & Container Tracking';

  @override
  String get vinLotContainerNumber => 'VIN / Lot / Container Number';

  @override
  String get enterYourVinNoLotNoOrContainerNoToTrackYourShipments =>
      'Enter your Vin No, Lot No, or Container No to track your shipments.';

  @override
  String get onTheWayToWarehouse => 'On The Way To Warehouse';

  @override
  String get onTheWayToDestination => 'On The Way To Destination';

  @override
  String get onHandWithLoad => 'On Hand W/ Load';

  @override
  String get atTheDock => 'At The Dock';

  @override
  String get arrival => 'Arrival';

  @override
  String get tracking => 'Tracking';

  @override
  String get vehicle => 'Vehicle';

  @override
  String get container => 'Container';

  @override
  String get towingRates => 'Towing Rates';

  @override
  String get payments => 'Payments';

  @override
  String get approved => 'Approved';

  @override
  String get shipment => 'Shipment';

  @override
  String get clearance => 'Clearance';

  @override
  String get clearLog => 'Clear Log';

  @override
  String get singleVcc => 'Single VCC';

  @override
  String get exitClaimCharge => 'Exit Claim Charge';

  @override
  String get deliveryCharge => 'Delivery Charge';

  @override
  String get detentionCharge => 'Detention Charge';

  @override
  String get amountApplied => 'Amount Applied';

  @override
  String get remainingAmount => 'Remaining Amount';

  @override
  String get remark => 'Remark';

  @override
  String get paymentAllocations => 'Payment Allocations';

  @override
  String get shipping => 'Shipping';

  @override
  String get title => 'Title';

  @override
  String get storageClr => 'Storage Clearance';

  @override
  String get storageDo => 'Storage Delivery Order';

  @override
  String get charge => 'Charge';

  @override
  String get other => 'Other';

  @override
  String get storage => 'Storage';

  @override
  String get dismantle => 'Dismantle';

  @override
  String get vatCustom => 'VAT Custom';

  @override
  String get freight => 'Freight';

  @override
  String get attestationFee => 'Attestation Fee';

  @override
  String get inspectionCharges => 'Inspection Charges';

  @override
  String get auctionStorage => 'Auction Storage';

  @override
  String get sharjahYardStorage => 'Sharjah Yard Storage';

  @override
  String get fedExOrMailingFee => 'Fed Ex or Mailing Fee';

  @override
  String get recoveryFee => 'Recovery Fee';

  @override
  String get customHold => 'Custom Hold';

  @override
  String get relistFee => 'Relist Fee';

  @override
  String get detentionCharges => 'Detention Charges';

  @override
  String get shortage => 'Shortage';

  @override
  String get suvCharges => 'SUV Charges';

  @override
  String get tdsCharges => 'TDS Charges';

  @override
  String get registrationFee => 'Registration Fee';

  @override
  String get transportationFee => 'Transportation Fee';

  @override
  String get officeFeeAndBank => 'Office Fee and Bank';

  @override
  String get emptyContainers => 'Empty Containers';

  @override
  String get previousPayment => 'Previous Payment';

  @override
  String get currency => 'Currency';

  @override
  String get wire => 'Wire';

  @override
  String get check => 'Check';

  @override
  String get mukhasa => 'Mukhasa';

  @override
  String get salesTax => 'Sales tax';

  @override
  String get damageCredit => 'Damage credit';

  @override
  String get demurrageCredit => 'Demurrage credit';

  @override
  String get storageCredit => 'Storage credit';

  @override
  String get exitPaperCredit => 'Exit paper credit';

  @override
  String get exchangeRate => 'Exchange Rate';

  @override
  String get auctionPayments => 'Auction Payments';

  @override
  String get balance => 'Balance';

  @override
  String get booking => 'Booking';

  @override
  String get tdsAmount => 'TDS Amount';

  @override
  String get invoiceApplied => 'Invoice Applied';

  @override
  String get freightPayments => 'Freight Payments';
}
