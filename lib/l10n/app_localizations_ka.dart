// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Georgian (`ka`).
class AppLocalizationsKa extends AppLocalizations {
  AppLocalizationsKa([String locale = 'ka']) : super(locale);

  @override
  String get peaceGlobalLogistics => 'ფის გლობალ ლოჯისტიკ';

  @override
  String get welcome => 'მოგესალმებით';

  @override
  String get copyRight => 'საავტორო უფლება© 2024 ფის  გლობალ ლოჯისტიკ ';

  @override
  String get emailOrUsername => 'ელ. ფოსტა ან მომხმარებელის სახელი';

  @override
  String get password => 'პაროლი';

  @override
  String get forgotPasswordQ => 'დაგავიწყდათ პაროლი?';

  @override
  String get forgotPassword => 'დაგავიწყდათ პაროლი';

  @override
  String get forgotPasswordPrompt =>
      'პაროლის შეცვლასთან დაკავშირებით  გთხოვთ, დაეკონტაქტოთ ადმინისტრატორს';

  @override
  String get logIn => 'შესვლა';

  @override
  String get authenticationFailure => 'ავტორიზაციის შეცდომა';

  @override
  String get home => 'მთავარი';

  @override
  String get vehicles => 'ავტომობილები';

  @override
  String get items => 'ნივთები';

  @override
  String get shipments => 'გზავნილები';

  @override
  String get invoices => 'ინვოისები';

  @override
  String get mixShipping => 'შერეული გადაზიდვა';

  @override
  String get vehicleDescription =>
      'დააწკაპეთ თქვენი მანქანების რაოდენობის საჩვენებლად';

  @override
  String get invoiceDescription => 'სრული კონტეინერის ინვოისები';

  @override
  String get profile => 'პროფილი';

  @override
  String get announcements => 'განცხადებები';

  @override
  String get settings => 'პარამეტრები';

  @override
  String get contactUs => 'დაგვეკონტაქტეთ';

  @override
  String get customerComplains => 'მომხმარებლის საჩივრები';

  @override
  String get privacyPolicy => 'კონფიდენციალურობის პოლიტიკა';

  @override
  String get aboutUs => 'ჩვენ შესახებ';

  @override
  String get logout => 'გამოსვლა';

  @override
  String get logoutOfYourAccount => 'გსურთ თქვენი ანგარიშიდან გამოსვლა?';

  @override
  String get cancel => 'გაუქმება';

  @override
  String get close => 'დახურვა';

  @override
  String searchItem(String item) {
    return '$item-ის ძებნა';
  }

  @override
  String get all => 'ყველა';

  @override
  String get auctionPaid => 'აუქციონი გადახდილია';

  @override
  String get auctionUnpaid => 'აუქციონი გადასახდელია';

  @override
  String get onTheWay => 'გზაშია';

  @override
  String get onHandWithTitle => 'სერთიფიკატით ';

  @override
  String get onHandNoTitle => 'სერთიფიკატის  გარეშე';

  @override
  String get shipped => 'გაგზავნილია ';

  @override
  String get auction => 'აუქციონი ';

  @override
  String get total => 'ჯამში';

  @override
  String get atLoading => 'ჩატვირთვისას';

  @override
  String get arrived => 'ჩამოსული';

  @override
  String get selectVehicleStatus => 'აირჩიეთ ავტომობილის სტატუსი';

  @override
  String get location => 'მდებარეობა';

  @override
  String get open => 'გახსნა';

  @override
  String get paid => 'გადახდილია';

  @override
  String get due => 'ვადა';

  @override
  String get pastDue => 'ვადაგადაცილებული';

  @override
  String get pastDueDays => 'გასული დღეები';

  @override
  String get allInvoices => 'ყველა ინვოისი';

  @override
  String get allVehicles => 'ყველა ავტომობილი';

  @override
  String get allShipments => 'ყველა გზავნილი';

  @override
  String get allMixShipping => 'ყველა შერეული გადაზიდვა';

  @override
  String get notifications => 'შეტყობინებები';

  @override
  String get images => 'სურათები';

  @override
  String get status => 'სტატუსი';

  @override
  String get year => 'წელი';

  @override
  String get make => 'გაკეთება ';

  @override
  String get model => 'მოდელი';

  @override
  String get color => 'ფერი';

  @override
  String get keyPresent => 'გასაღები აქვს';

  @override
  String get pointOfLoading => 'ჩატვირთვის ადგილი';

  @override
  String get pointOfDischarge => 'ჩამოტვირთვის  ადგილი';

  @override
  String get accountNumber => 'ანგარიშის ნომერი';

  @override
  String get auctionName => 'აუქციონის სახელი';

  @override
  String get auctionCity => 'აუქციონის ქალაქი';

  @override
  String get autionInvLink => 'აუქციონის მოსაწვევი ლინკი';

  @override
  String get view => 'ნახვა';

  @override
  String get ageAtPGLWH => 'ასაკი PGL W/H ';

  @override
  String get purchasedDate => 'შესყიდვის თარიღი';

  @override
  String get reportDate => 'მოხსენების თარიღი';

  @override
  String get paymentDate => 'გადახდის თარიღი';

  @override
  String get towRequestDate => 'ბუქსირების მოთხოვნის თარიღი';

  @override
  String get pickUpDate => 'აღების  თარიღი';

  @override
  String get pickUpDaysFromPurchase => 'აღების  დღეები შეძენიდან';

  @override
  String get pickUpDaysFromReport => 'აიღების  დღეები მოხსენებიდან';

  @override
  String get deliverDate => 'მიტანის თარიღი';

  @override
  String get issueDate => 'გამოშვების თარიღი';

  @override
  String get dueDate => 'ვადა';

  @override
  String get eta => 'ჩამოსვლის სავაურო დრო';

  @override
  String get etd => 'გამოშვების თარიღი';

  @override
  String get titleState => 'სერთიფიკატის მდგომარეობა';

  @override
  String get titleStatus => 'სერთიფიკატის  სტატუსი';

  @override
  String get track => 'თვალყურის დევნება';

  @override
  String get units => 'ერთეულები';

  @override
  String get portOfLoading => 'ჩატვირთვის პორტი';

  @override
  String get portOfDischarge => 'ჩამოტვირთვის პორტი';

  @override
  String get size => 'ზომა';

  @override
  String get loadingDate => 'ჩატვირთვის თარიღი';

  @override
  String get download => 'გადმოწერა';

  @override
  String get purpose => 'დანიშნულება';

  @override
  String get amount => 'თანხა';

  @override
  String get received => 'მიღებულია';

  @override
  String get amountDue => 'გადასახდელი თანხა';

  @override
  String get receivedDate => 'მიღების თარიღი';

  @override
  String get vin => 'VIN';

  @override
  String get vins => 'VIN-ები';

  @override
  String get lotNumber => 'ლოტის ნომერი';

  @override
  String get lotNumbers => 'ლოტის ნომრები';

  @override
  String get receivedDates => 'მიღების თარიღ(ებ)ი';

  @override
  String get billOfLoading => 'ჩატვირთვის ქვითარი';

  @override
  String get dockReceipt => 'ნავსადგურის ქვითარი';

  @override
  String get clearanceInvoice => 'განბაჟების ინვოისი';

  @override
  String get eitherEmailOrUsername => 'ელ. ფოსტა ან მომხმარებელის სახელი';

  @override
  String get passwordMustBeProvided => 'პაროლი უნდა იყოს მითითებული';

  @override
  String itemMustBeProvided(String name) {
    return '$name უნდა იყოს მოწოდებული';
  }

  @override
  String itemMustBeAtLeastCountChars(String name, int count) {
    return '$name უნდა შეიცავდეს სიმბოლოების $count მაინც';
  }

  @override
  String get invalidCreds =>
      'არასწორი მონაცემები. გთხოვთ, შეამოწმოთ თქვენი ელ. ფოსტა, მომხმარებელის სახელი ან პაროლი და სცადოთ ხელახლა';

  @override
  String get unExpectedErrorOccured =>
      'Წარმოიშვა გაუთვალისწინებელი ხარვეზი. Გთხოვთ კიდევ სცადეთ';

  @override
  String get noConnectionInfoMightBeOutdated => 'თქვენ არ ხართ დაკავშირებული!';

  @override
  String get connectionReestablished => 'კავშირი აღდგა!';

  @override
  String noItemsFound(String item) {
    return 'არცერთი $item-ი არ მოიძებნა!';
  }

  @override
  String get description => 'აღწერა';

  @override
  String get photoLink => 'სურათების ლინკი';

  @override
  String get errorDownloadingFilePleaseTryAgain =>
      'ფაილის გადმოწერის შეცდომა. გთხოვთ, კიდევ ერთხელ სცადოთ.';

  @override
  String get containerNo => 'კონტეინერის ნომერი';

  @override
  String get bookingNo => 'ჯავშნის ნომერი';

  @override
  String get invoiceNo => 'ინვოისის ნომერი';

  @override
  String get invoiceAmount => 'ინვოისის თანხა';

  @override
  String get paymentReceived => 'გადახდა მიღებულია';

  @override
  String get start => 'დაწყება';

  @override
  String get end => 'დასრულება';

  @override
  String countDays(int count) {
    return '$count დღე(ები)';
  }

  @override
  String get notAttached => 'არ არის მიმაგრებული';

  @override
  String get shippingRates => 'გადაზიდვის ფასები';

  @override
  String get complete => 'სრული';

  @override
  String get halfcut => 'ნახევრად ჭრილი';

  @override
  String get hc40 => '40 HC';

  @override
  String get hc45 => '45 HC';

  @override
  String get mix => 'შერეული';

  @override
  String get towing => 'ბუქსირება';

  @override
  String get sedanRate => 'სედანის ტარიფები';

  @override
  String get sedanDismantleRate => 'სედანის დემონტაჟის ტარიფები';

  @override
  String get suvRate => 'SUV-ის ტარიფები';

  @override
  String get suvDismantleRate => 'SUV-ის დემონტაჟის ტარიფები';

  @override
  String get companyType => 'კომპანიის ტიპი';

  @override
  String get vehicleType => 'ავტომობილის ტიპი';

  @override
  String get from => 'დან';

  @override
  String get to => 'მდე';

  @override
  String get filter => 'ფილტრი';

  @override
  String get clear => 'გასუფთავება';

  @override
  String get apply => 'შევსება';

  @override
  String get effectiveFrom => 'აქტიურია -----დან (ETD)';

  @override
  String get tapBackAgainToLeave => 'უკან გადასვლას დააჭირეთ აქ';

  @override
  String get seeMore => 'მეტის ნახვა';

  @override
  String get updateAvailable => 'განახლება ხელმისაწვსომია';

  @override
  String get youAreUsingAnOlderVersion =>
      'თქვენ იყენებთ ამ აპის უფრო ძველ ვერსიას, გთხოვთ განაახლოთ გასაგრძელებლად.';

  @override
  String get update => 'განახლება';

  @override
  String get idFiltering => 'ID ფილტრაცია';

  @override
  String get data => 'მონაცემები';

  @override
  String get price => 'ფასი';

  @override
  String get min => 'მინიმუმი';

  @override
  String get max => 'მაქსიმუმი';

  @override
  String get purchaseDate => 'შესყიდვის თარიღი';

  @override
  String get fromDate => 'თარიღიდან';

  @override
  String get toDate => 'თარიღამდე';

  @override
  String get contactText =>
      'Ჩვენ შეგვიძლია დახმარება. ჩვენი ექსპერტების გუნდი მზად არის უპასუხოს თქვენს შეკითხვებს.';

  @override
  String get name => 'სახელი';

  @override
  String get phone => 'ტელეფონი';

  @override
  String get email => 'ელ. ფოსტა';

  @override
  String get subject => 'თემა';

  @override
  String get message => 'შეტყობინება';

  @override
  String get submit => 'წარდგენა';

  @override
  String get ok => 'კარგი';

  @override
  String get success => 'წარმატება';

  @override
  String get failure => 'შეცდომა';

  @override
  String get contactMessageSuccess =>
      'თქვენი შეტყობინება წარმატებით გაიგზავნა და ჩვენ მალე დაგიკავშირდებით';

  @override
  String get contactMessageFailure =>
      'თქვენი შეტყობინება არ გაიგზავნა, შესაძლოა რაიმე შეცდომა იყოს. შეამოწმეთ თქვენი ფორმა და სცადეთ ხელახლა';

  @override
  String get mixShippingCalculator => 'შერეული გადაზიდვის კალკულატორი';

  @override
  String get rateCalculator => 'ფასების კალკულატორი';

  @override
  String get state => 'შტატი';

  @override
  String get branch => 'ფილიალი';

  @override
  String get city => 'ქალაქი';

  @override
  String get vehiclePrice => 'ავტომობილის ფასი';

  @override
  String get fullSizeSUVs => 'სრული ზომის SUV-ები';

  @override
  String get manheimAdesa => 'მენჰეიმი, ადესა';

  @override
  String get majorAccident => 'დიდი დაზიანება';

  @override
  String get priceOver10000AED => 'ფასი 10000 AED-ზე მეტი';

  @override
  String get mixShippingCalculationTerms =>
      'დეტალური ინფორმაციისთვის, ვადების და პირობების, ასევე გადასახადების შესახებ, გთხოვთ ␣';

  @override
  String get clickHere => 'დააჭირეთ აქ';

  @override
  String get calculate => 'გამოთვალეთ';

  @override
  String get shippingRatesTerms => 'გადაზიდვის ტარიფები პირობები';

  @override
  String get note => 'შენიშვნა';

  @override
  String get arrivalNotice => 'ჩამოსვლის შეტყობინება';

  @override
  String get arrivalNotices => 'ჩამოსვლის შეტყობინებები';

  @override
  String get company => 'კომპანია';

  @override
  String get downloadMixShippingRates =>
      'შერეული გადაზიდვის ტარიფების გადმოწერა';

  @override
  String get statements => 'განცხადებები';

  @override
  String get destination => 'დანიშნულება';

  @override
  String get reviewed => 'განხილულია';

  @override
  String get pending => 'მომლოდინე';

  @override
  String get canceled => 'გაუქმებული';

  @override
  String get type => 'ტიპი';

  @override
  String get debit => 'სადებეტო ბარათი';

  @override
  String get credit => 'საკრედიტო ბარათი';

  @override
  String get paymentMethod => 'გადახდის მეთოდი';

  @override
  String get cash => 'ნაღდი ანგარიშსწორება';

  @override
  String get bankTransfer => 'ბანკით გადახდა';

  @override
  String get date => 'თარიღი';

  @override
  String failedToFetchItems(String item) {
    return '$item-ის მიღება ვერ მოხერხდა!';
  }

  @override
  String get notSupportedDestination =>
      'ამჟამად, ჩვენ არ გთავაზობთ გადაზიდვის მომსახურებას არჩეული ქალაქიდან არჩეულ დანიშნულებამდე!';

  @override
  String get selectLanguage => 'ენის არჩევა';

  @override
  String get language => 'ენა';

  @override
  String get preview => 'გადახედვა';

  @override
  String get titleReceiveDate => 'სერტიფიკატის მიღების თარიღი';

  @override
  String get disclaimer => 'პასუხისმგებლობის უარყოფა';

  @override
  String get anEstimateCustomDutyCharge =>
      'შეიძლება დაწესდეს საბაჟო გადასახადი მანქანის ფასის დაახლოებით 10%';

  @override
  String get vehicleAndContainerTracking =>
      'მანქანებისა და კონტეინერების თვალყურის დევნება';

  @override
  String get vinLotContainerNumber => 'VIN / ლოტი / კონტეინერის ნომერი';

  @override
  String get enterYourVinNoLotNoOrContainerNoToTrackYourShipments =>
      'შეიყვანეთ თქვენი Vin No, Lot No, ან Container No, რათა თვალყური ადევნოთ თქვენს გადაზიდვებს.';

  @override
  String get onTheWayToWarehouse => 'საწყობისკენ მიმავალი';

  @override
  String get onTheWayToDestination => 'დანიშნულების ადგილისკენ მიმავალი';

  @override
  String get onHandWithLoad => 'დატვირთვით';

  @override
  String get atTheDock => 'ნავსადგურებზე';

  @override
  String get arrival => 'ჩამოსვლა';

  @override
  String get tracking => 'თვალყურის დევნება';

  @override
  String get vehicle => 'სატრანსპორტო საშუალება';

  @override
  String get container => 'კონტეინერი';

  @override
  String get towingRates => 'ბუქსირების ტარიფები';

  @override
  String get payments => 'გადახდები';

  @override
  String get approved => 'დადასტურებული';

  @override
  String get shipment => 'გადაზიდვა';

  @override
  String get clearance => 'განბაჟება';

  @override
  String get clearLog => 'განბაჟების ლოჯისტიკა';

  @override
  String get singleVcc => 'ერთი VCC';

  @override
  String get exitClaimCharge => 'გასვლის გადასახადი';

  @override
  String get deliveryCharge => 'მიწოდების საფასური';

  @override
  String get detentionCharge => 'გაჩერების გადასახადი';

  @override
  String get amountApplied => 'გამოყენებული თანხა';

  @override
  String get remainingAmount => 'დარჩენილი თანხა';

  @override
  String get remark => 'შენიშვნა';

  @override
  String get paymentAllocations => 'გადახდის ასიგნებები';

  @override
  String get shipping => 'მიწოდება';

  @override
  String get title => 'სერტიფიკატი';

  @override
  String get storageClr => 'ტვირთის შენახვის საფასური';

  @override
  String get storageDo => 'შეკვეთის მიწოდების ბრძანება';

  @override
  String get charge => 'ხარჯები';

  @override
  String get other => 'სხვა';

  @override
  String get storage => 'შენახვა';

  @override
  String get dismantle => 'დემონტაჟი';

  @override
  String get vatCustom => 'დღგ';

  @override
  String get freight => 'სატვირთო';

  @override
  String get attestationFee => 'ხელშეკრულების საფასური';

  @override
  String get inspectionCharges => 'ინსპექტირების ხარჯები';

  @override
  String get auctionStorage => 'აუქციონის საცავი';

  @override
  String get sharjahYardStorage => 'შარჯას ეზოს საცავი';

  @override
  String get fedExOrMailingFee => 'Fedex ან საფოსტო საკომისიო';

  @override
  String get recoveryFee => 'აღდგენის საფასური';

  @override
  String get customHold => 'საბაჟო შემოწმება';

  @override
  String get relistFee => 'გამეორებით განთავსების გადასახადი ';

  @override
  String get detentionCharges => 'ავტომობილის გაჩერების გადასახადი';

  @override
  String get shortage => 'დეფიციტი';

  @override
  String get suvCharges => 'SUV გადასახადები';

  @override
  String get tdsCharges => 'TDS გადასახადები';

  @override
  String get registrationFee => 'რეგისტრაციის საფასური';

  @override
  String get transportationFee => 'ტრანსპორტის საფასური';

  @override
  String get officeFeeAndBank => 'ოფისის და ბანკის მოსაკრებელი';

  @override
  String get emptyContainers => 'ცარიელი კონტეინერები';

  @override
  String get previousPayment => 'წინა გადახდა';

  @override
  String get currency => 'ვალუტა';

  @override
  String get wire => 'wire გადარიცხვა';

  @override
  String get check => 'შემოწმება';

  @override
  String get mukhasa => 'საშვი';

  @override
  String get salesTax => 'გაყიდვის გადასახადი';

  @override
  String get damageCredit => 'ზარალის ანაზღურება';

  @override
  String get demurrageCredit =>
      'შენახვის ვადაგადაცილების გადასახადი (დემურაჟი)';

  @override
  String get storageCredit => 'შენახვის საფასური';

  @override
  String get exitPaperCredit => 'გატანის დოკუმენტის საფასური';

  @override
  String get exchangeRate => 'გაცვლითი კურსი';

  @override
  String get auctionPayments => 'აუქციონის გადასახადები';

  @override
  String get balance => 'ბალანსი';

  @override
  String get booking => 'დაჯავშნა';

  @override
  String get tdsAmount => 'TDS თანხა';

  @override
  String get invoiceApplied => 'ინვოისი გამოყენებულია';

  @override
  String get freightPayments => 'ტვირთის გადახდა';
}
