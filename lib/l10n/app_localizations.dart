import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';
import 'app_localizations_ka.dart';
import 'app_localizations_ru.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en'),
    Locale('ka'),
    Locale('ru')
  ];

  /// No description provided for @peaceGlobalLogistics.
  ///
  /// In en, this message translates to:
  /// **'Peace Global Logistics'**
  String get peaceGlobalLogistics;

  /// No description provided for @welcome.
  ///
  /// In en, this message translates to:
  /// **'Welcome'**
  String get welcome;

  /// No description provided for @copyRight.
  ///
  /// In en, this message translates to:
  /// **'Copyright © 2024 Peace Global Logistics'**
  String get copyRight;

  /// No description provided for @emailOrUsername.
  ///
  /// In en, this message translates to:
  /// **'Email or username'**
  String get emailOrUsername;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @forgotPasswordQ.
  ///
  /// In en, this message translates to:
  /// **'Forgot password?'**
  String get forgotPasswordQ;

  /// No description provided for @forgotPassword.
  ///
  /// In en, this message translates to:
  /// **'Forgot password'**
  String get forgotPassword;

  /// No description provided for @forgotPasswordPrompt.
  ///
  /// In en, this message translates to:
  /// **'To reset your password, please contact the administrator for further assistance'**
  String get forgotPasswordPrompt;

  /// No description provided for @logIn.
  ///
  /// In en, this message translates to:
  /// **'Log in'**
  String get logIn;

  /// No description provided for @authenticationFailure.
  ///
  /// In en, this message translates to:
  /// **'Authentication Failure'**
  String get authenticationFailure;

  /// No description provided for @home.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// No description provided for @vehicles.
  ///
  /// In en, this message translates to:
  /// **'Vehicles'**
  String get vehicles;

  /// No description provided for @items.
  ///
  /// In en, this message translates to:
  /// **'Items'**
  String get items;

  /// No description provided for @shipments.
  ///
  /// In en, this message translates to:
  /// **'Shipments'**
  String get shipments;

  /// No description provided for @invoices.
  ///
  /// In en, this message translates to:
  /// **'Invoices'**
  String get invoices;

  /// No description provided for @mixShipping.
  ///
  /// In en, this message translates to:
  /// **'Mix Shipping'**
  String get mixShipping;

  /// No description provided for @vehicleDescription.
  ///
  /// In en, this message translates to:
  /// **'Click to show summary of your vehicles'**
  String get vehicleDescription;

  /// No description provided for @invoiceDescription.
  ///
  /// In en, this message translates to:
  /// **'Full container invoices'**
  String get invoiceDescription;

  /// No description provided for @profile.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// No description provided for @announcements.
  ///
  /// In en, this message translates to:
  /// **'Announcements'**
  String get announcements;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @contactUs.
  ///
  /// In en, this message translates to:
  /// **'Contact Us'**
  String get contactUs;

  /// No description provided for @customerComplains.
  ///
  /// In en, this message translates to:
  /// **'Customer Complains'**
  String get customerComplains;

  /// No description provided for @privacyPolicy.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// No description provided for @aboutUs.
  ///
  /// In en, this message translates to:
  /// **'About Us'**
  String get aboutUs;

  /// No description provided for @logout.
  ///
  /// In en, this message translates to:
  /// **'Log out'**
  String get logout;

  /// No description provided for @logoutOfYourAccount.
  ///
  /// In en, this message translates to:
  /// **'Log out of your account?'**
  String get logoutOfYourAccount;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @close.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// No description provided for @searchItem.
  ///
  /// In en, this message translates to:
  /// **'Search {item}'**
  String searchItem(String item);

  /// No description provided for @all.
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// No description provided for @auctionPaid.
  ///
  /// In en, this message translates to:
  /// **'Auction Paid'**
  String get auctionPaid;

  /// No description provided for @auctionUnpaid.
  ///
  /// In en, this message translates to:
  /// **'Auction Unpaid'**
  String get auctionUnpaid;

  /// No description provided for @onTheWay.
  ///
  /// In en, this message translates to:
  /// **'On The Way'**
  String get onTheWay;

  /// No description provided for @onHandWithTitle.
  ///
  /// In en, this message translates to:
  /// **'On Hand W/ Title'**
  String get onHandWithTitle;

  /// No description provided for @onHandNoTitle.
  ///
  /// In en, this message translates to:
  /// **'On Hand No Title'**
  String get onHandNoTitle;

  /// No description provided for @shipped.
  ///
  /// In en, this message translates to:
  /// **'Shipped'**
  String get shipped;

  /// No description provided for @auction.
  ///
  /// In en, this message translates to:
  /// **'Auction'**
  String get auction;

  /// No description provided for @total.
  ///
  /// In en, this message translates to:
  /// **'Total'**
  String get total;

  /// No description provided for @atLoading.
  ///
  /// In en, this message translates to:
  /// **'At Loading'**
  String get atLoading;

  /// No description provided for @arrived.
  ///
  /// In en, this message translates to:
  /// **'Arrived'**
  String get arrived;

  /// No description provided for @selectVehicleStatus.
  ///
  /// In en, this message translates to:
  /// **'Select Vehicle Status'**
  String get selectVehicleStatus;

  /// No description provided for @location.
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get location;

  /// No description provided for @open.
  ///
  /// In en, this message translates to:
  /// **'Open'**
  String get open;

  /// No description provided for @paid.
  ///
  /// In en, this message translates to:
  /// **'Paid'**
  String get paid;

  /// No description provided for @due.
  ///
  /// In en, this message translates to:
  /// **'Due'**
  String get due;

  /// No description provided for @pastDue.
  ///
  /// In en, this message translates to:
  /// **'Past Due'**
  String get pastDue;

  /// No description provided for @pastDueDays.
  ///
  /// In en, this message translates to:
  /// **'Past Due Days'**
  String get pastDueDays;

  /// No description provided for @allInvoices.
  ///
  /// In en, this message translates to:
  /// **'All Invoices'**
  String get allInvoices;

  /// No description provided for @allVehicles.
  ///
  /// In en, this message translates to:
  /// **'All Vehicles'**
  String get allVehicles;

  /// No description provided for @allShipments.
  ///
  /// In en, this message translates to:
  /// **'All Shipments'**
  String get allShipments;

  /// No description provided for @allMixShipping.
  ///
  /// In en, this message translates to:
  /// **'All Mix Shipping'**
  String get allMixShipping;

  /// No description provided for @notifications.
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// No description provided for @images.
  ///
  /// In en, this message translates to:
  /// **'Images'**
  String get images;

  /// No description provided for @status.
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// No description provided for @year.
  ///
  /// In en, this message translates to:
  /// **'Year'**
  String get year;

  /// No description provided for @make.
  ///
  /// In en, this message translates to:
  /// **'Make'**
  String get make;

  /// No description provided for @model.
  ///
  /// In en, this message translates to:
  /// **'Model'**
  String get model;

  /// No description provided for @color.
  ///
  /// In en, this message translates to:
  /// **'Color'**
  String get color;

  /// No description provided for @keyPresent.
  ///
  /// In en, this message translates to:
  /// **'Key Present'**
  String get keyPresent;

  /// No description provided for @pointOfLoading.
  ///
  /// In en, this message translates to:
  /// **'Point Of Loading'**
  String get pointOfLoading;

  /// No description provided for @pointOfDischarge.
  ///
  /// In en, this message translates to:
  /// **'Point Of Discharge'**
  String get pointOfDischarge;

  /// No description provided for @accountNumber.
  ///
  /// In en, this message translates to:
  /// **'Account Number'**
  String get accountNumber;

  /// No description provided for @auctionName.
  ///
  /// In en, this message translates to:
  /// **'Auction Name'**
  String get auctionName;

  /// No description provided for @auctionCity.
  ///
  /// In en, this message translates to:
  /// **'Auction City'**
  String get auctionCity;

  /// No description provided for @autionInvLink.
  ///
  /// In en, this message translates to:
  /// **'Auction Inv Link'**
  String get autionInvLink;

  /// No description provided for @view.
  ///
  /// In en, this message translates to:
  /// **'View'**
  String get view;

  /// No description provided for @ageAtPGLWH.
  ///
  /// In en, this message translates to:
  /// **'Age at PGL W/H'**
  String get ageAtPGLWH;

  /// No description provided for @purchasedDate.
  ///
  /// In en, this message translates to:
  /// **'Purchased Date'**
  String get purchasedDate;

  /// No description provided for @reportDate.
  ///
  /// In en, this message translates to:
  /// **'Report Date'**
  String get reportDate;

  /// No description provided for @paymentDate.
  ///
  /// In en, this message translates to:
  /// **'Payment Date'**
  String get paymentDate;

  /// No description provided for @towRequestDate.
  ///
  /// In en, this message translates to:
  /// **'Tow Request Date'**
  String get towRequestDate;

  /// No description provided for @pickUpDate.
  ///
  /// In en, this message translates to:
  /// **'Pick Up Date'**
  String get pickUpDate;

  /// No description provided for @pickUpDaysFromPurchase.
  ///
  /// In en, this message translates to:
  /// **'Pick Up Days From Purchase'**
  String get pickUpDaysFromPurchase;

  /// No description provided for @pickUpDaysFromReport.
  ///
  /// In en, this message translates to:
  /// **'Pick Up Days From Report'**
  String get pickUpDaysFromReport;

  /// No description provided for @deliverDate.
  ///
  /// In en, this message translates to:
  /// **'Delivery Date'**
  String get deliverDate;

  /// No description provided for @issueDate.
  ///
  /// In en, this message translates to:
  /// **'Issue Date'**
  String get issueDate;

  /// No description provided for @dueDate.
  ///
  /// In en, this message translates to:
  /// **'Due Date'**
  String get dueDate;

  /// No description provided for @eta.
  ///
  /// In en, this message translates to:
  /// **'ETA'**
  String get eta;

  /// No description provided for @etd.
  ///
  /// In en, this message translates to:
  /// **'ETD'**
  String get etd;

  /// No description provided for @titleState.
  ///
  /// In en, this message translates to:
  /// **'Title State'**
  String get titleState;

  /// No description provided for @titleStatus.
  ///
  /// In en, this message translates to:
  /// **'Title Status'**
  String get titleStatus;

  /// No description provided for @track.
  ///
  /// In en, this message translates to:
  /// **'Track'**
  String get track;

  /// No description provided for @units.
  ///
  /// In en, this message translates to:
  /// **'Units'**
  String get units;

  /// No description provided for @portOfLoading.
  ///
  /// In en, this message translates to:
  /// **'Port of Loading'**
  String get portOfLoading;

  /// No description provided for @portOfDischarge.
  ///
  /// In en, this message translates to:
  /// **'Port of Discharge'**
  String get portOfDischarge;

  /// No description provided for @size.
  ///
  /// In en, this message translates to:
  /// **'Size'**
  String get size;

  /// No description provided for @loadingDate.
  ///
  /// In en, this message translates to:
  /// **'Loading Date'**
  String get loadingDate;

  /// No description provided for @download.
  ///
  /// In en, this message translates to:
  /// **'Download'**
  String get download;

  /// No description provided for @purpose.
  ///
  /// In en, this message translates to:
  /// **'Purpose'**
  String get purpose;

  /// No description provided for @amount.
  ///
  /// In en, this message translates to:
  /// **'Amount'**
  String get amount;

  /// No description provided for @received.
  ///
  /// In en, this message translates to:
  /// **'Received'**
  String get received;

  /// No description provided for @amountDue.
  ///
  /// In en, this message translates to:
  /// **'Amount Due'**
  String get amountDue;

  /// No description provided for @receivedDate.
  ///
  /// In en, this message translates to:
  /// **'Received Date'**
  String get receivedDate;

  /// No description provided for @vin.
  ///
  /// In en, this message translates to:
  /// **'Vin'**
  String get vin;

  /// No description provided for @vins.
  ///
  /// In en, this message translates to:
  /// **'Vin(s)'**
  String get vins;

  /// No description provided for @lotNumber.
  ///
  /// In en, this message translates to:
  /// **'Lot Number'**
  String get lotNumber;

  /// No description provided for @lotNumbers.
  ///
  /// In en, this message translates to:
  /// **'Lot Number(s)'**
  String get lotNumbers;

  /// No description provided for @receivedDates.
  ///
  /// In en, this message translates to:
  /// **'Received Date(s)'**
  String get receivedDates;

  /// No description provided for @billOfLoading.
  ///
  /// In en, this message translates to:
  /// **'Bill Of Loading'**
  String get billOfLoading;

  /// No description provided for @dockReceipt.
  ///
  /// In en, this message translates to:
  /// **'Dock Receipt'**
  String get dockReceipt;

  /// No description provided for @clearanceInvoice.
  ///
  /// In en, this message translates to:
  /// **'Clearance Invoice'**
  String get clearanceInvoice;

  /// No description provided for @eitherEmailOrUsername.
  ///
  /// In en, this message translates to:
  /// **'Either email or username'**
  String get eitherEmailOrUsername;

  /// No description provided for @passwordMustBeProvided.
  ///
  /// In en, this message translates to:
  /// **'Password must be provided'**
  String get passwordMustBeProvided;

  /// No description provided for @itemMustBeProvided.
  ///
  /// In en, this message translates to:
  /// **'{name} must be provided'**
  String itemMustBeProvided(String name);

  /// No description provided for @itemMustBeAtLeastCountChars.
  ///
  /// In en, this message translates to:
  /// **'{name} must be at least {count} charachters'**
  String itemMustBeAtLeastCountChars(String name, int count);

  /// No description provided for @invalidCreds.
  ///
  /// In en, this message translates to:
  /// **'Invalid credentials. Please check your email, username or password and try again'**
  String get invalidCreds;

  /// No description provided for @unExpectedErrorOccured.
  ///
  /// In en, this message translates to:
  /// **'An unexpected error occurred. Please try again'**
  String get unExpectedErrorOccured;

  /// No description provided for @noConnectionInfoMightBeOutdated.
  ///
  /// In en, this message translates to:
  /// **'You are not connected!'**
  String get noConnectionInfoMightBeOutdated;

  /// No description provided for @connectionReestablished.
  ///
  /// In en, this message translates to:
  /// **'Connection reestablished!'**
  String get connectionReestablished;

  /// No description provided for @noItemsFound.
  ///
  /// In en, this message translates to:
  /// **'No {item} found!'**
  String noItemsFound(String item);

  /// No description provided for @description.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// No description provided for @photoLink.
  ///
  /// In en, this message translates to:
  /// **'Photos Link'**
  String get photoLink;

  /// No description provided for @errorDownloadingFilePleaseTryAgain.
  ///
  /// In en, this message translates to:
  /// **'Error downloding file. Please try again.'**
  String get errorDownloadingFilePleaseTryAgain;

  /// No description provided for @containerNo.
  ///
  /// In en, this message translates to:
  /// **'Container No'**
  String get containerNo;

  /// No description provided for @bookingNo.
  ///
  /// In en, this message translates to:
  /// **'Booking No'**
  String get bookingNo;

  /// No description provided for @invoiceNo.
  ///
  /// In en, this message translates to:
  /// **'Invoice No'**
  String get invoiceNo;

  /// No description provided for @invoiceAmount.
  ///
  /// In en, this message translates to:
  /// **'Invoice Amount'**
  String get invoiceAmount;

  /// No description provided for @paymentReceived.
  ///
  /// In en, this message translates to:
  /// **'Payment Received'**
  String get paymentReceived;

  /// No description provided for @start.
  ///
  /// In en, this message translates to:
  /// **'Start'**
  String get start;

  /// No description provided for @end.
  ///
  /// In en, this message translates to:
  /// **'End'**
  String get end;

  /// No description provided for @countDays.
  ///
  /// In en, this message translates to:
  /// **'{count} Day(s)'**
  String countDays(int count);

  /// No description provided for @notAttached.
  ///
  /// In en, this message translates to:
  /// **'Not Attached'**
  String get notAttached;

  /// No description provided for @shippingRates.
  ///
  /// In en, this message translates to:
  /// **'Shipping Rates'**
  String get shippingRates;

  /// No description provided for @complete.
  ///
  /// In en, this message translates to:
  /// **'Complete'**
  String get complete;

  /// No description provided for @halfcut.
  ///
  /// In en, this message translates to:
  /// **'Half Cut'**
  String get halfcut;

  /// No description provided for @hc40.
  ///
  /// In en, this message translates to:
  /// **'40 HC'**
  String get hc40;

  /// No description provided for @hc45.
  ///
  /// In en, this message translates to:
  /// **'45 HC'**
  String get hc45;

  /// No description provided for @mix.
  ///
  /// In en, this message translates to:
  /// **'Mix'**
  String get mix;

  /// No description provided for @towing.
  ///
  /// In en, this message translates to:
  /// **'Towing'**
  String get towing;

  /// No description provided for @sedanRate.
  ///
  /// In en, this message translates to:
  /// **'Sedan Rate'**
  String get sedanRate;

  /// No description provided for @sedanDismantleRate.
  ///
  /// In en, this message translates to:
  /// **'Sedan Dismantle Rate'**
  String get sedanDismantleRate;

  /// No description provided for @suvRate.
  ///
  /// In en, this message translates to:
  /// **'SUV Rate'**
  String get suvRate;

  /// No description provided for @suvDismantleRate.
  ///
  /// In en, this message translates to:
  /// **'SUV Dismantle Rate'**
  String get suvDismantleRate;

  /// No description provided for @companyType.
  ///
  /// In en, this message translates to:
  /// **'Company Type'**
  String get companyType;

  /// No description provided for @vehicleType.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Type'**
  String get vehicleType;

  /// No description provided for @from.
  ///
  /// In en, this message translates to:
  /// **'From'**
  String get from;

  /// No description provided for @to.
  ///
  /// In en, this message translates to:
  /// **'To'**
  String get to;

  /// No description provided for @filter.
  ///
  /// In en, this message translates to:
  /// **'Filter'**
  String get filter;

  /// No description provided for @clear.
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get clear;

  /// No description provided for @apply.
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get apply;

  /// No description provided for @effectiveFrom.
  ///
  /// In en, this message translates to:
  /// **'Effective From (ETD)'**
  String get effectiveFrom;

  /// No description provided for @tapBackAgainToLeave.
  ///
  /// In en, this message translates to:
  /// **'Tap back again to leave'**
  String get tapBackAgainToLeave;

  /// No description provided for @seeMore.
  ///
  /// In en, this message translates to:
  /// **'See more'**
  String get seeMore;

  /// No description provided for @updateAvailable.
  ///
  /// In en, this message translates to:
  /// **'Update available!'**
  String get updateAvailable;

  /// No description provided for @youAreUsingAnOlderVersion.
  ///
  /// In en, this message translates to:
  /// **'You are using an older version of this app, please update to continue.'**
  String get youAreUsingAnOlderVersion;

  /// No description provided for @update.
  ///
  /// In en, this message translates to:
  /// **'Update'**
  String get update;

  /// No description provided for @idFiltering.
  ///
  /// In en, this message translates to:
  /// **'ID Filtering'**
  String get idFiltering;

  /// No description provided for @data.
  ///
  /// In en, this message translates to:
  /// **'Data'**
  String get data;

  /// No description provided for @price.
  ///
  /// In en, this message translates to:
  /// **'Price'**
  String get price;

  /// No description provided for @min.
  ///
  /// In en, this message translates to:
  /// **'Min'**
  String get min;

  /// No description provided for @max.
  ///
  /// In en, this message translates to:
  /// **'Max'**
  String get max;

  /// No description provided for @purchaseDate.
  ///
  /// In en, this message translates to:
  /// **'Purchase Date'**
  String get purchaseDate;

  /// No description provided for @fromDate.
  ///
  /// In en, this message translates to:
  /// **'From Date'**
  String get fromDate;

  /// No description provided for @toDate.
  ///
  /// In en, this message translates to:
  /// **'To Date'**
  String get toDate;

  /// No description provided for @contactText.
  ///
  /// In en, this message translates to:
  /// **'We can help. Our team of experts are on hand to answer your questions.'**
  String get contactText;

  /// No description provided for @name.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// No description provided for @phone.
  ///
  /// In en, this message translates to:
  /// **'Phone'**
  String get phone;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @subject.
  ///
  /// In en, this message translates to:
  /// **'Subject'**
  String get subject;

  /// No description provided for @message.
  ///
  /// In en, this message translates to:
  /// **'Message'**
  String get message;

  /// No description provided for @submit.
  ///
  /// In en, this message translates to:
  /// **'Submit'**
  String get submit;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @success.
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// No description provided for @failure.
  ///
  /// In en, this message translates to:
  /// **'Failure'**
  String get failure;

  /// No description provided for @contactMessageSuccess.
  ///
  /// In en, this message translates to:
  /// **'Your message was successfully send and we will get back to you soon'**
  String get contactMessageSuccess;

  /// No description provided for @contactMessageFailure.
  ///
  /// In en, this message translates to:
  /// **'Your message wasn\'\'t send, there might be some error. Check your form and try again'**
  String get contactMessageFailure;

  /// No description provided for @mixShippingCalculator.
  ///
  /// In en, this message translates to:
  /// **'Mix Shipping Calculator'**
  String get mixShippingCalculator;

  /// No description provided for @rateCalculator.
  ///
  /// In en, this message translates to:
  /// **'Rate Calculator'**
  String get rateCalculator;

  /// No description provided for @state.
  ///
  /// In en, this message translates to:
  /// **'State'**
  String get state;

  /// No description provided for @branch.
  ///
  /// In en, this message translates to:
  /// **'Branch'**
  String get branch;

  /// No description provided for @city.
  ///
  /// In en, this message translates to:
  /// **'City'**
  String get city;

  /// No description provided for @vehiclePrice.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Price'**
  String get vehiclePrice;

  /// No description provided for @fullSizeSUVs.
  ///
  /// In en, this message translates to:
  /// **'Full size SUVs'**
  String get fullSizeSUVs;

  /// No description provided for @manheimAdesa.
  ///
  /// In en, this message translates to:
  /// **'Manheim Adesa'**
  String get manheimAdesa;

  /// No description provided for @majorAccident.
  ///
  /// In en, this message translates to:
  /// **'Major Accident'**
  String get majorAccident;

  /// No description provided for @priceOver10000AED.
  ///
  /// In en, this message translates to:
  /// **'Price Over 10000 AED'**
  String get priceOver10000AED;

  /// No description provided for @mixShippingCalculationTerms.
  ///
  /// In en, this message translates to:
  /// **'For detailed information regarding terms and conditions as well as charges, please '**
  String get mixShippingCalculationTerms;

  /// No description provided for @clickHere.
  ///
  /// In en, this message translates to:
  /// **'click here'**
  String get clickHere;

  /// No description provided for @calculate.
  ///
  /// In en, this message translates to:
  /// **'Calculate'**
  String get calculate;

  /// No description provided for @shippingRatesTerms.
  ///
  /// In en, this message translates to:
  /// **'Shipping Rates Terms'**
  String get shippingRatesTerms;

  /// No description provided for @note.
  ///
  /// In en, this message translates to:
  /// **'Note'**
  String get note;

  /// No description provided for @arrivalNotice.
  ///
  /// In en, this message translates to:
  /// **'Arrival Notice'**
  String get arrivalNotice;

  /// No description provided for @arrivalNotices.
  ///
  /// In en, this message translates to:
  /// **'Arrival Notices'**
  String get arrivalNotices;

  /// No description provided for @company.
  ///
  /// In en, this message translates to:
  /// **'Company'**
  String get company;

  /// No description provided for @downloadMixShippingRates.
  ///
  /// In en, this message translates to:
  /// **'Download Mix Shipping Rates'**
  String get downloadMixShippingRates;

  /// No description provided for @statements.
  ///
  /// In en, this message translates to:
  /// **'Statements'**
  String get statements;

  /// No description provided for @destination.
  ///
  /// In en, this message translates to:
  /// **'Destination'**
  String get destination;

  /// No description provided for @reviewed.
  ///
  /// In en, this message translates to:
  /// **'Reviewed'**
  String get reviewed;

  /// No description provided for @pending.
  ///
  /// In en, this message translates to:
  /// **'Pending'**
  String get pending;

  /// No description provided for @canceled.
  ///
  /// In en, this message translates to:
  /// **'Canceled'**
  String get canceled;

  /// No description provided for @type.
  ///
  /// In en, this message translates to:
  /// **'Type'**
  String get type;

  /// No description provided for @debit.
  ///
  /// In en, this message translates to:
  /// **'Debit'**
  String get debit;

  /// No description provided for @credit.
  ///
  /// In en, this message translates to:
  /// **'Credit'**
  String get credit;

  /// No description provided for @paymentMethod.
  ///
  /// In en, this message translates to:
  /// **'Payment Method'**
  String get paymentMethod;

  /// No description provided for @cash.
  ///
  /// In en, this message translates to:
  /// **'Cash'**
  String get cash;

  /// No description provided for @bankTransfer.
  ///
  /// In en, this message translates to:
  /// **'Bank Transfer'**
  String get bankTransfer;

  /// No description provided for @date.
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get date;

  /// No description provided for @failedToFetchItems.
  ///
  /// In en, this message translates to:
  /// **'Failed to fetch {item}!'**
  String failedToFetchItems(String item);

  /// No description provided for @notSupportedDestination.
  ///
  /// In en, this message translates to:
  /// **'Currently, we do not offer shipment services from the chosen city to the selected destination!'**
  String get notSupportedDestination;

  /// No description provided for @selectLanguage.
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectLanguage;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @preview.
  ///
  /// In en, this message translates to:
  /// **'Preview'**
  String get preview;

  /// No description provided for @titleReceiveDate.
  ///
  /// In en, this message translates to:
  /// **'Title Receive Date'**
  String get titleReceiveDate;

  /// No description provided for @disclaimer.
  ///
  /// In en, this message translates to:
  /// **'Disclaimer'**
  String get disclaimer;

  /// No description provided for @anEstimateCustomDutyCharge.
  ///
  /// In en, this message translates to:
  /// **'A custom duty charge of about 10% of the vehicle price may apply'**
  String get anEstimateCustomDutyCharge;

  /// No description provided for @vehicleAndContainerTracking.
  ///
  /// In en, this message translates to:
  /// **'Vehicle & Container Tracking'**
  String get vehicleAndContainerTracking;

  /// No description provided for @vinLotContainerNumber.
  ///
  /// In en, this message translates to:
  /// **'VIN / Lot / Container Number'**
  String get vinLotContainerNumber;

  /// No description provided for @enterYourVinNoLotNoOrContainerNoToTrackYourShipments.
  ///
  /// In en, this message translates to:
  /// **'Enter your Vin No, Lot No, or Container No to track your shipments.'**
  String get enterYourVinNoLotNoOrContainerNoToTrackYourShipments;

  /// No description provided for @onTheWayToWarehouse.
  ///
  /// In en, this message translates to:
  /// **'On The Way To Warehouse'**
  String get onTheWayToWarehouse;

  /// No description provided for @onTheWayToDestination.
  ///
  /// In en, this message translates to:
  /// **'On The Way To Destination'**
  String get onTheWayToDestination;

  /// No description provided for @onHandWithLoad.
  ///
  /// In en, this message translates to:
  /// **'On Hand W/ Load'**
  String get onHandWithLoad;

  /// No description provided for @atTheDock.
  ///
  /// In en, this message translates to:
  /// **'At The Dock'**
  String get atTheDock;

  /// No description provided for @arrival.
  ///
  /// In en, this message translates to:
  /// **'Arrival'**
  String get arrival;

  /// No description provided for @tracking.
  ///
  /// In en, this message translates to:
  /// **'Tracking'**
  String get tracking;

  /// No description provided for @vehicle.
  ///
  /// In en, this message translates to:
  /// **'Vehicle'**
  String get vehicle;

  /// No description provided for @container.
  ///
  /// In en, this message translates to:
  /// **'Container'**
  String get container;

  /// No description provided for @towingRates.
  ///
  /// In en, this message translates to:
  /// **'Towing Rates'**
  String get towingRates;

  /// No description provided for @payments.
  ///
  /// In en, this message translates to:
  /// **'Payments'**
  String get payments;

  /// No description provided for @approved.
  ///
  /// In en, this message translates to:
  /// **'Approved'**
  String get approved;

  /// No description provided for @shipment.
  ///
  /// In en, this message translates to:
  /// **'Shipment'**
  String get shipment;

  /// No description provided for @clearance.
  ///
  /// In en, this message translates to:
  /// **'Clearance'**
  String get clearance;

  /// No description provided for @clearLog.
  ///
  /// In en, this message translates to:
  /// **'Clear Log'**
  String get clearLog;

  /// No description provided for @singleVcc.
  ///
  /// In en, this message translates to:
  /// **'Single VCC'**
  String get singleVcc;

  /// No description provided for @exitClaimCharge.
  ///
  /// In en, this message translates to:
  /// **'Exit Claim Charge'**
  String get exitClaimCharge;

  /// No description provided for @deliveryCharge.
  ///
  /// In en, this message translates to:
  /// **'Delivery Charge'**
  String get deliveryCharge;

  /// No description provided for @detentionCharge.
  ///
  /// In en, this message translates to:
  /// **'Detention Charge'**
  String get detentionCharge;

  /// No description provided for @amountApplied.
  ///
  /// In en, this message translates to:
  /// **'Amount Applied'**
  String get amountApplied;

  /// No description provided for @remainingAmount.
  ///
  /// In en, this message translates to:
  /// **'Remaining Amount'**
  String get remainingAmount;

  /// No description provided for @remark.
  ///
  /// In en, this message translates to:
  /// **'Remark'**
  String get remark;

  /// No description provided for @paymentAllocations.
  ///
  /// In en, this message translates to:
  /// **'Payment Allocations'**
  String get paymentAllocations;

  /// No description provided for @shipping.
  ///
  /// In en, this message translates to:
  /// **'Shipping'**
  String get shipping;

  /// No description provided for @title.
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get title;

  /// No description provided for @storageClr.
  ///
  /// In en, this message translates to:
  /// **'Storage Clearance'**
  String get storageClr;

  /// No description provided for @storageDo.
  ///
  /// In en, this message translates to:
  /// **'Storage Delivery Order'**
  String get storageDo;

  /// No description provided for @charge.
  ///
  /// In en, this message translates to:
  /// **'Charge'**
  String get charge;

  /// No description provided for @other.
  ///
  /// In en, this message translates to:
  /// **'Other'**
  String get other;

  /// No description provided for @storage.
  ///
  /// In en, this message translates to:
  /// **'Storage'**
  String get storage;

  /// No description provided for @dismantle.
  ///
  /// In en, this message translates to:
  /// **'Dismantle'**
  String get dismantle;

  /// No description provided for @vatCustom.
  ///
  /// In en, this message translates to:
  /// **'VAT Custom'**
  String get vatCustom;

  /// No description provided for @freight.
  ///
  /// In en, this message translates to:
  /// **'Freight'**
  String get freight;

  /// No description provided for @attestationFee.
  ///
  /// In en, this message translates to:
  /// **'Attestation Fee'**
  String get attestationFee;

  /// No description provided for @inspectionCharges.
  ///
  /// In en, this message translates to:
  /// **'Inspection Charges'**
  String get inspectionCharges;

  /// No description provided for @auctionStorage.
  ///
  /// In en, this message translates to:
  /// **'Auction Storage'**
  String get auctionStorage;

  /// No description provided for @sharjahYardStorage.
  ///
  /// In en, this message translates to:
  /// **'Sharjah Yard Storage'**
  String get sharjahYardStorage;

  /// No description provided for @fedExOrMailingFee.
  ///
  /// In en, this message translates to:
  /// **'Fed Ex or Mailing Fee'**
  String get fedExOrMailingFee;

  /// No description provided for @recoveryFee.
  ///
  /// In en, this message translates to:
  /// **'Recovery Fee'**
  String get recoveryFee;

  /// No description provided for @customHold.
  ///
  /// In en, this message translates to:
  /// **'Custom Hold'**
  String get customHold;

  /// No description provided for @relistFee.
  ///
  /// In en, this message translates to:
  /// **'Relist Fee'**
  String get relistFee;

  /// No description provided for @detentionCharges.
  ///
  /// In en, this message translates to:
  /// **'Detention Charges'**
  String get detentionCharges;

  /// No description provided for @shortage.
  ///
  /// In en, this message translates to:
  /// **'Shortage'**
  String get shortage;

  /// No description provided for @suvCharges.
  ///
  /// In en, this message translates to:
  /// **'SUV Charges'**
  String get suvCharges;

  /// No description provided for @tdsCharges.
  ///
  /// In en, this message translates to:
  /// **'TDS Charges'**
  String get tdsCharges;

  /// No description provided for @registrationFee.
  ///
  /// In en, this message translates to:
  /// **'Registration Fee'**
  String get registrationFee;

  /// No description provided for @transportationFee.
  ///
  /// In en, this message translates to:
  /// **'Transportation Fee'**
  String get transportationFee;

  /// No description provided for @officeFeeAndBank.
  ///
  /// In en, this message translates to:
  /// **'Office Fee and Bank'**
  String get officeFeeAndBank;

  /// No description provided for @emptyContainers.
  ///
  /// In en, this message translates to:
  /// **'Empty Containers'**
  String get emptyContainers;

  /// No description provided for @previousPayment.
  ///
  /// In en, this message translates to:
  /// **'Previous Payment'**
  String get previousPayment;

  /// No description provided for @currency.
  ///
  /// In en, this message translates to:
  /// **'Currency'**
  String get currency;

  /// No description provided for @wire.
  ///
  /// In en, this message translates to:
  /// **'Wire'**
  String get wire;

  /// No description provided for @check.
  ///
  /// In en, this message translates to:
  /// **'Check'**
  String get check;

  /// No description provided for @mukhasa.
  ///
  /// In en, this message translates to:
  /// **'Mukhasa'**
  String get mukhasa;

  /// No description provided for @salesTax.
  ///
  /// In en, this message translates to:
  /// **'Sales tax'**
  String get salesTax;

  /// No description provided for @damageCredit.
  ///
  /// In en, this message translates to:
  /// **'Damage credit'**
  String get damageCredit;

  /// No description provided for @demurrageCredit.
  ///
  /// In en, this message translates to:
  /// **'Demurrage credit'**
  String get demurrageCredit;

  /// No description provided for @storageCredit.
  ///
  /// In en, this message translates to:
  /// **'Storage credit'**
  String get storageCredit;

  /// No description provided for @exitPaperCredit.
  ///
  /// In en, this message translates to:
  /// **'Exit paper credit'**
  String get exitPaperCredit;

  /// No description provided for @exchangeRate.
  ///
  /// In en, this message translates to:
  /// **'Exchange Rate'**
  String get exchangeRate;

  /// No description provided for @auctionPayments.
  ///
  /// In en, this message translates to:
  /// **'Auction Payments'**
  String get auctionPayments;

  /// No description provided for @balance.
  ///
  /// In en, this message translates to:
  /// **'Balance'**
  String get balance;

  /// No description provided for @booking.
  ///
  /// In en, this message translates to:
  /// **'Booking'**
  String get booking;

  /// No description provided for @tdsAmount.
  ///
  /// In en, this message translates to:
  /// **'TDS Amount'**
  String get tdsAmount;

  /// No description provided for @invoiceApplied.
  ///
  /// In en, this message translates to:
  /// **'Invoice Applied'**
  String get invoiceApplied;

  /// No description provided for @freightPayments.
  ///
  /// In en, this message translates to:
  /// **'Freight Payments'**
  String get freightPayments;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar', 'en', 'ka', 'ru'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'en':
      return AppLocalizationsEn();
    case 'ka':
      return AppLocalizationsKa();
    case 'ru':
      return AppLocalizationsRu();
  }

  throw FlutterError(
      'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue '
      'on GitHub with a reproducible sample app and the gen-l10n configuration '
      'that was used.');
}
