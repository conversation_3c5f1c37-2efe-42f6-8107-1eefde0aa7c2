// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:announcements_repository/announcements_repository.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:notifications_repository/notifications_repository.dart';
import 'package:pgl_mobile_app/logic/blocs/announcements/announcements.exports.dart';
import 'package:pgl_mobile_app/logic/blocs/dashboard/bloc/dashboard_bloc.dart';
import 'package:pgl_mobile_app/logic/blocs/notifications/bloc/notifications_bloc.dart';
import 'package:pgl_mobile_app/presentation/widgets/common/notification_item.widget.dart';
import 'package:user_repository/user_repository.dart';

const AndroidNotificationChannel channel = AndroidNotificationChannel(
  'high_importance_channel',
  'High Importance Notifications',
  description: 'This channel is used for important notifications.',
  importance: Importance.max,
  playSound: true,
);

void onDidReceiveBackgroundNotificationResponse(NotificationResponse response) {
  NotificationModel notificationModel = NotificationModel.fromMap(
    jsonDecode(response.payload ?? ''),
  );
  onNotificationTap(notificationModel);
}

Future<void> onBackgroundMessage(RemoteMessage message) async {
  NotificationModel notificationModel = NotificationModel.fromMap(
    jsonDecode(message.data['notification'] ?? ''),
  );
  onNotificationTap(notificationModel);
}

class FirebaseMessagingConfig {
  FirebaseMessaging messaging = FirebaseMessaging.instance;
  final BuildContext context;
  late AuthorizationStatus authorizationStatus;
  late NotificationsBloc _notificationsBloc;
  late AnnouncementsBloc _announcementsBloc;
  late DashboardBloc _dashboardBloc;
  late StreamSubscription forgroundStream;
  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
  late StreamSubscription _tokenRefresh;

  FirebaseMessagingConfig.init({required this.context}) {
    _notificationsBloc = context.read<NotificationsBloc>();
    _announcementsBloc = context.read<AnnouncementsBloc>();
    _dashboardBloc = context.read<DashboardBloc>();
    _tokenRefresh = messaging.onTokenRefresh.listen(onRefreshFCMToken);
    _requestPermission();
  }

  Future<void> _requestPermission() async {
    try {
      NotificationSettings settings = await messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );
      flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.requestNotificationsPermission();

      authorizationStatus = settings.authorizationStatus;

      if (authorizationStatus == AuthorizationStatus.authorized) {
        _initFirebaseMessaging();
        // initialise the plugin. app_icon needs to be a added as a drawable resource to the Android head project
        const AndroidInitializationSettings initializationSettingsAndroid =
            AndroidInitializationSettings('ic_notification');
        final DarwinInitializationSettings initializationSettingsDarwin = DarwinInitializationSettings();
        final InitializationSettings initializationSettings = InitializationSettings(
          android: initializationSettingsAndroid,
          iOS: initializationSettingsDarwin,
        );
        flutterLocalNotificationsPlugin.initialize(
          initializationSettings,
          onDidReceiveNotificationResponse: (NotificationResponse response) {
            NotificationModel notificationModel = NotificationModel.fromMap(
              jsonDecode(response.payload ?? ''),
            );
            onNotificationTap(notificationModel);
          },
          onDidReceiveBackgroundNotificationResponse: onDidReceiveBackgroundNotificationResponse,
        );

        await flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
            ?.createNotificationChannel(channel);
      }
    } catch (e) {
      //
    }
  }

  void onDidReceiveLocalNotification(int id, String? title, String? body, String? payload) async {
    // display a dialog with the notification details, tap ok to go to another page
    showDialog(
      context: context,
      builder: (BuildContext context) => CupertinoAlertDialog(
        title: Text(title ?? ''),
        content: Text(body ?? ''),
        actions: [
          CupertinoDialogAction(
            isDefaultAction: true,
            child: const Text('Ok'),
            onPressed: () async {
              Navigator.of(context, rootNavigator: true).pop();
            },
          )
        ],
      ),
    );
  }

  Future<void> _initFirebaseMessaging() async {
    setFCMToken();
    _registerOnBackgroundMessage();
    _registerOnForgroundMessage();
    _registerOnInitialOpen();
    _registedOnMessageOpenedApp();
  }

  void _registerOnBackgroundMessage() {
    // FirebaseMessaging.onBackgroundMessage(
    //   onBackgroundMessage,
    // );
  }

  Future<void> _registerOnInitialOpen() async {
    RemoteMessage? initialMessage = await FirebaseMessaging.instance.getInitialMessage();
    if (initialMessage != null) {
      onNotificationTap(_getNotificationModelFromMessage(initialMessage));
    }
  }

  void _registerOnForgroundMessage() {
    // IOS show message when app is open start
    messaging.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );
    // IOS show message when app is open end
    forgroundStream = FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
      if (message.data['announcement'] != null) {
        _announcementsBloc.add(
          AnnouncementReceived(
            announcement: _getAnnouncementModelFromMessage(message),
          ),
        );
        _dashboardBloc.add(AnnouncementCountIncreement());
      }
      if (message.notification != null) {
        _notificationsBloc.add(
          NotificationReceived(
            notification: _getNotificationModelFromMessage(message),
          ),
        );
        // implement flutter_local_notification here
        RemoteNotification? notification = message.notification;
        AndroidNotification? android = message.notification?.android;
        if (notification != null && android != null) {
          await flutterLocalNotificationsPlugin
              .show(
                notification.hashCode,
                notification.title,
                notification.body,
                NotificationDetails(
                  android: AndroidNotificationDetails(
                    channel.id, channel.name,
                    channelDescription: channel.description,
                    icon: android.smallIcon,
                    color: Theme.of(context).colorScheme.primary,
                    colorized: true,
                    // other properties...
                  ),
                ),
                payload: jsonEncode(_getNotificationModelFromMessage(message).toMap()),
              )
              .catchError(
                (e) {},
              );
        }
      }
    });
  }

  Future<void> _registedOnMessageOpenedApp() async {
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      onNotificationTap(_getNotificationModelFromMessage(message));
    });
  }

  NotificationModel _getNotificationModelFromMessage(RemoteMessage message) {
    return NotificationModel.fromMap(
      json.decode(message.data['notification']),
    );
  }

  Announcement _getAnnouncementModelFromMessage(RemoteMessage message) {
    return Announcement.fromMap(
      json.decode(message.data['announcement']),
    );
  }

  Future<void> setFCMToken() async {
    if (Platform.isIOS) {
      int retries = 0;
      Timer.periodic(const Duration(seconds: 2), (Timer t) async {
        retries++;
        String? apnsToken = await FirebaseMessaging.instance.getAPNSToken();
        if (apnsToken != null) {
          await messaging.getToken().then((String? token) {
            t.cancel();
            RepositoryProvider.of<UserRepository>(context).setFCMToken(token ?? '');
          });
        }
        if (retries == 100) {
          // dont retry after 100 times
          t.cancel();
        }
      });
    } else {
      await messaging.getToken().then((String? token) {
        log(token ?? '');
        RepositoryProvider.of<UserRepository>(context).setFCMToken(token ?? '');
      });
    }
  }

  Future<void> onRefreshFCMToken(String token) async {
    RepositoryProvider.of<UserRepository>(context).setFCMToken(token);
  }

  Future<void> deleteFCMToken() async {
    await messaging.getToken().then((String? token) {
      RepositoryProvider.of<UserRepository>(context).deleteFCMToken(token ?? '').then(
        (bool res) {
          messaging.deleteToken();
        },
      );
    });
  }

  void dispose() {
    if (authorizationStatus == AuthorizationStatus.authorized) {
      forgroundStream.cancel();
    }
    _tokenRefresh.cancel();
  }
}
